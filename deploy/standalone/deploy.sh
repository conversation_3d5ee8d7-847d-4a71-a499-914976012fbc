#!/bin/bash

# Standalone Deployment Script
# This script deploys the Labwise Web application without requiring Docker or system modifications
# It runs nginx as a user process with all dependencies self-contained

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_NAME="labwise-web"
DEFAULT_PORT="8080"
DEFAULT_USER="$(whoami)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if port is available
check_port() {
    local port="$1"
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        return 1
    fi
    return 0
}

# Function to find available port
find_available_port() {
    local start_port="$1"
    local port="$start_port"
    
    while [ "$port" -lt $((start_port + 100)) ]; do
        if check_port "$port"; then
            echo "$port"
            return 0
        fi
        port=$((port + 1))
    done
    
    log_error "No available port found in range $start_port-$((start_port + 100))"
    exit 1
}

# Function to validate deployment environment
validate_environment() {
    log_info "Validating deployment environment..."

    # Check system architecture compatibility
    local system_arch=$(uname -m)
    log_info "System architecture: $system_arch"

    if [ "$system_arch" != "x86_64" ]; then
        log_error "Architecture mismatch detected!"
        log_error "This deployment package is built for x86_64 (linux/amd64)"
        log_error "Current system architecture: $system_arch"
        log_error ""
        log_error "Solutions:"
        log_error "1. Deploy on an x86_64 system (Rocky Linux 8.10, CentOS 8, RHEL 8)"
        log_error "2. Rebuild the package on the correct architecture"
        log_error ""
        exit 1
    fi

    # Check if we're in the right directory
    if [ ! -f "$SCRIPT_DIR/nginx/nginx" ]; then
        log_error "nginx binary not found. Are you in the correct deployment directory?"
        exit 1
    fi

    if [ ! -d "$SCRIPT_DIR/html" ]; then
        log_error "html directory not found. Are you in the correct deployment directory?"
        exit 1
    fi

    if [ ! -f "$SCRIPT_DIR/conf/nginx.conf" ]; then
        log_error "nginx.conf not found. Are you in the correct deployment directory?"
        exit 1
    fi

    # Check if nginx binary is executable and compatible
    if [ ! -x "$SCRIPT_DIR/nginx/nginx" ]; then
        log_warning "Making nginx binary executable..."
        chmod +x "$SCRIPT_DIR/nginx/nginx"
    fi

    # Test nginx binary compatibility
    log_info "Testing nginx binary compatibility..."
    if ! "$SCRIPT_DIR/nginx/nginx" -v &> /dev/null; then
        log_error "Nginx binary compatibility test failed!"
        log_error "This usually indicates an architecture mismatch."
        log_error ""

        # Try to get more detailed error information
        if command -v file &> /dev/null; then
            local binary_info=$(file "$SCRIPT_DIR/nginx/nginx")
            log_error "Binary info: $binary_info"
        fi

        log_error "Common causes:"
        log_error "1. Binary built for different architecture (ARM64 vs x86_64)"
        log_error "2. Missing system libraries"
        log_error "3. Incompatible glibc version"
        log_error ""
        log_error "Solutions:"
        log_error "1. Verify system architecture: uname -m"
        log_error "2. Rebuild package with correct platform targeting"
        log_error "3. Check system compatibility (Rocky Linux 8.10 x86_64)"
        exit 1
    fi

    local nginx_version=$("$SCRIPT_DIR/nginx/nginx" -v 2>&1)
    log_success "✓ Nginx binary is compatible: $nginx_version"

    # Check system requirements
    if ! command -v netstat &> /dev/null && ! command -v ss &> /dev/null; then
        log_warning "Neither netstat nor ss found. Port checking may not work properly."
    fi

    # Check if we're on a supported OS
    if [ -f /etc/os-release ]; then
        local os_info=$(grep "^PRETTY_NAME=" /etc/os-release | cut -d'"' -f2)
        log_info "Operating system: $os_info"

        # Check for known compatible systems
        if echo "$os_info" | grep -qi "rocky\|centos\|red hat\|rhel"; then
            log_success "✓ Running on supported RHEL-based system"
        elif echo "$os_info" | grep -qi "ubuntu\|debian"; then
            log_info "Running on Debian-based system (should be compatible)"
        else
            log_warning "Running on unverified system: $os_info"
            log_info "This package is tested on Rocky Linux 8.10"
        fi
    fi

    log_success "Environment validation passed"
}

# Function to create runtime configuration
create_runtime_config() {
    local port="$1"
    local user="$2"
    
    log_info "Creating runtime configuration for port $port..."
    
    # Create runtime directories
    mkdir -p "$SCRIPT_DIR/run" "$SCRIPT_DIR/logs" "$SCRIPT_DIR/cache"
    
    # Create nginx configuration with runtime settings
    cat > "$SCRIPT_DIR/run/nginx.conf" << EOF
# Standalone nginx configuration for Labwise Web
# Generated on $(date)

# Run as current user (no privilege escalation required)
user $user;
worker_processes auto;
pid $SCRIPT_DIR/run/nginx.pid;
error_log $SCRIPT_DIR/logs/error.log warn;

# Load dynamic modules
load_module $SCRIPT_DIR/nginx/modules/ngx_http_headers_more_filter_module.so;

events {
    worker_connections 1024;
    use epoll;
}

http {
    include $SCRIPT_DIR/conf/mime.types;
    default_type application/octet-stream;
    
    # Logging
    log_format main '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                    '\$status \$body_bytes_sent "\$http_referer" '
                    '"\$http_user_agent" "\$http_x_forwarded_for"';
    
    access_log $SCRIPT_DIR/logs/access.log main;
    
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip compression
    gzip on;
    gzip_comp_level 5;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/json
        application/xhtml+xml;
    
    # Cache settings
    proxy_cache_path $SCRIPT_DIR/cache levels=1:2 keys_zone=my_cache:10m max_size=10g 
                     inactive=60m use_temp_path=off;
    
    server {
        listen $port;
        server_name _;
        
        # Document root
        root $SCRIPT_DIR/html;
        index index.html index.htm;
        
        # Main location for SPA
        location / {
            try_files \$uri \$uri/ /index.html;
            
            # Security headers
            add_header X-Frame-Options "SAMEORIGIN" always;
            add_header X-Content-Type-Options "nosniff" always;
            add_header X-XSS-Protection "1; mode=block" always;
            add_header Referrer-Policy "strict-origin-when-cross-origin" always;
            
            # ETag support
            etag on;
        }
        
        # Static assets with long-term caching
        location ~* ^.+\.(?!html$|svg$)[^.]+$ {
            add_header Cache-Control "public, max-age=31536000, immutable";
            etag on;
        }
        
        # API proxy configuration (if needed)
        # Note: Update these endpoints based on your actual backend services
        location /api/ {
            # This is a placeholder - update with your actual backend URL
            # proxy_pass http://your-backend-server:port/api/;
            
            # For now, return a 503 to indicate backend is not configured
            return 503 '{"error": "Backend not configured. Please update nginx configuration."}';
            add_header Content-Type application/json;
        }
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 '{"status": "healthy", "service": "labwise-web", "timestamp": "\$time_iso8601"}';
            add_header Content-Type application/json;
        }
        
        # Error pages
        error_page 404 /index.html;
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root $SCRIPT_DIR/html;
        }
    }
}
EOF

    log_success "Runtime configuration created"
}

# Function to test nginx configuration
test_nginx_config() {
    log_info "Testing nginx configuration..."
    
    if "$SCRIPT_DIR/nginx/nginx" -t -c "$SCRIPT_DIR/run/nginx.conf" -p "$SCRIPT_DIR"; then
        log_success "Nginx configuration test passed"
    else
        log_error "Nginx configuration test failed"
        exit 1
    fi
}

# Function to start the service
start_service() {
    local port="$1"
    
    log_info "Starting Labwise Web service on port $port..."
    
    # Set library path for nginx dependencies
    export LD_LIBRARY_PATH="$SCRIPT_DIR/nginx/lib:$LD_LIBRARY_PATH"
    
    # Start nginx
    "$SCRIPT_DIR/nginx/nginx" -c "$SCRIPT_DIR/run/nginx.conf" -p "$SCRIPT_DIR"
    
    # Wait a moment and check if it started successfully
    sleep 2
    
    if [ -f "$SCRIPT_DIR/run/nginx.pid" ] && kill -0 "$(cat "$SCRIPT_DIR/run/nginx.pid")" 2>/dev/null; then
        local pid=$(cat "$SCRIPT_DIR/run/nginx.pid")
        log_success "Labwise Web service started successfully!"
        log_info "PID: $pid"
        log_info "Port: $port"
        log_info "URL: http://localhost:$port"
        log_info ""
        log_info "Service management commands:"
        log_info "  Start:  ./start.sh"
        log_info "  Stop:   ./stop.sh"
        log_info "  Status: ./status.sh"
        log_info ""
        log_info "Logs are available in: $SCRIPT_DIR/logs/"
    else
        log_error "Failed to start Labwise Web service"
        log_info "Check error log: $SCRIPT_DIR/logs/error.log"
        exit 1
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -p, --port PORT     Port to run the service on (default: $DEFAULT_PORT)"
    echo "  -u, --user USER     User to run nginx as (default: current user)"
    echo "  -h, --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                  # Deploy with default settings"
    echo "  $0 -p 9000          # Deploy on port 9000"
    echo "  $0 --port 8080      # Deploy on port 8080"
}

# Main deployment function
main() {
    local port="$DEFAULT_PORT"
    local user="$DEFAULT_USER"
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--port)
                port="$2"
                shift 2
                ;;
            -u|--user)
                user="$2"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Validate port number
    if ! [[ "$port" =~ ^[0-9]+$ ]] || [ "$port" -lt 1024 ] || [ "$port" -gt 65535 ]; then
        log_error "Invalid port number: $port (must be between 1024-65535)"
        exit 1
    fi
    
    # Check if port is available
    if ! check_port "$port"; then
        log_warning "Port $port is already in use"
        port=$(find_available_port "$port")
        log_info "Using alternative port: $port"
    fi
    
    log_info "Deploying Labwise Web standalone service..."
    log_info "Port: $port"
    log_info "User: $user"
    log_info "Directory: $SCRIPT_DIR"
    
    # Validate environment
    validate_environment
    
    # Create runtime configuration
    create_runtime_config "$port" "$user"
    
    # Test configuration
    test_nginx_config
    
    # Start service
    start_service "$port"
}

# Run main function
main "$@"
