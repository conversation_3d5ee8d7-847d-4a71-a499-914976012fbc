FROM --platform=linux/amd64 nginx:1.25.3-alpine AS nginx_base

# Install additional modules if needed
RUN apk add --no-cache nginx-mod-http-headers-more

# Verify architecture
RUN echo "Architecture check:" && uname -m && file /usr/sbin/nginx

FROM scratch AS export
COPY --from=nginx_base /usr/sbin/nginx /nginx/
COPY --from=nginx_base /usr/lib/nginx/ /nginx/modules/
COPY --from=nginx_base /etc/nginx/ /nginx/conf/
COPY --from=nginx_base /var/log/nginx/ /nginx/logs/
COPY --from=nginx_base /var/cache/nginx/ /nginx/cache/
COPY --from=nginx_base /usr/share/nginx/ /nginx/html/
COPY --from=nginx_base /lib/ld-musl-*.so.1 /nginx/lib/
COPY --from=nginx_base /lib/libc.musl-*.so.1 /nginx/lib/
COPY --from=nginx_base /usr/lib/libpcre.so.* /nginx/lib/
COPY --from=nginx_base /usr/lib/libssl.so.* /nginx/lib/
COPY --from=nginx_base /usr/lib/libcrypto.so.* /nginx/lib/
COPY --from=nginx_base /lib/libz.so.* /nginx/lib/
