#!/bin/bash

# Standalone Deployment Builder
# This script creates a self-contained deployment package that doesn't require Docker on the target system
# It uses Dock<PERSON> during the build phase to ensure consistency across different development environments

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
BUILD_OUTPUT_DIR="$SCRIPT_DIR/build-output"
DEPLOY_PACKAGE_DIR="$SCRIPT_DIR/deploy-package"
NGINX_VERSION="1.25.3"
TEMP_CONTAINER_NAME="labwise-standalone-builder-$(date +%s)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up temporary containers..."
    docker rm -f "$TEMP_CONTAINER_NAME" 2>/dev/null || true
}

# Set trap for cleanup
trap cleanup EXIT

# Function to detect host architecture
detect_host_architecture() {
    local arch=$(uname -m)
    case $arch in
        x86_64)
            echo "amd64"
            ;;
        aarch64|arm64)
            echo "arm64"
            ;;
        *)
            echo "unknown"
            ;;
    esac
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker is required for the build process but not found"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi

    # Check if Docker supports buildx for multi-platform builds
    if ! docker buildx version &> /dev/null; then
        log_warning "Docker buildx not available. Multi-platform builds may not work."
        log_info "Consider updating Docker to a newer version that supports buildx."
    fi

    # Detect and report host architecture
    local host_arch=$(detect_host_architecture)
    local host_platform=$(uname -s | tr '[:upper:]' '[:lower:]')
    log_info "Host platform: $host_platform/$host_arch"
    log_info "Target platform: linux/amd64 (Rocky Linux 8.10 x86_64)"

    if [ "$host_arch" != "amd64" ]; then
        log_warning "Cross-platform build detected!"
        log_info "Building on $host_platform/$host_arch for linux/amd64 deployment"
        log_info "This ensures compatibility with Rocky Linux 8.10 x86_64 systems"
    fi

    log_success "Prerequisites check passed"
}

# Function to detect host architecture and warn about cross-platform builds
detect_host_architecture() {
    local host_arch=$(uname -m)
    local host_os=$(uname -s)

    log_info "Host system: $host_os $host_arch"

    case "$host_arch" in
        arm64|aarch64)
            log_warning "Building on ARM64 architecture"
            log_info "Will force linux/amd64 platform for Rocky Linux 8.10 compatibility"
            ;;
        x86_64|amd64)
            log_info "Building on x86_64 architecture"
            log_info "Will use linux/amd64 platform for Rocky Linux 8.10 compatibility"
            ;;
        *)
            log_warning "Unknown host architecture: $host_arch"
            log_info "Will attempt to use linux/amd64 platform for Rocky Linux 8.10 compatibility"
            ;;
    esac
}

# Function to detect environment
detect_environment() {
    local env_arg="$1"

    if [ -n "$env_arg" ]; then
        echo "$env_arg"
        return
    fi

    # Try to detect from UMI_ENV environment variable
    if [ -n "$UMI_ENV" ]; then
        echo "$UMI_ENV"
        return
    fi

    # Default to develop
    echo "develop"
}

# Function to build frontend assets
build_frontend() {
    local target_env="$1"

    log_info "Building frontend assets for environment: $target_env"
    log_info "Platform: linux/amd64 (for Rocky Linux 8.10 compatibility)"

    # Create a temporary Dockerfile for building
    cat > "$BUILD_OUTPUT_DIR/Dockerfile.build" << EOF
FROM --platform=linux/amd64 node:18-buster-slim AS builder

WORKDIR /app
COPY package.json yarn.lock ./
RUN yarn install --production=false

COPY . .
RUN NODE_ENV=production UMI_ENV=$target_env yarn build

FROM scratch AS export
COPY --from=builder /app/dist /dist
EOF

    # Build and extract the dist folder with explicit platform targeting
    log_info "Building frontend in Docker container (linux/amd64)..."

    # Use buildx if available for better cross-platform support
    if docker buildx version &> /dev/null; then
        log_info "Using Docker buildx for cross-platform build"
        docker buildx build \
            --platform linux/amd64 \
            -f "$BUILD_OUTPUT_DIR/Dockerfile.build" \
            --target export \
            --output "$BUILD_OUTPUT_DIR" \
            "$PROJECT_ROOT"
    else
        log_info "Using standard Docker build with platform specification"
        docker build \
            --platform linux/amd64 \
            -f "$BUILD_OUTPUT_DIR/Dockerfile.build" \
            --target export \
            --output "$BUILD_OUTPUT_DIR" \
            "$PROJECT_ROOT"
    fi

    if [ ! -d "$BUILD_OUTPUT_DIR/dist" ]; then
        log_error "Frontend build failed - dist directory not found"
        exit 1
    fi

    log_success "Frontend build completed"
}

# Function to extract nginx binary and dependencies
extract_nginx() {
    log_info "Extracting nginx binary and dependencies..."
    log_info "Platform: linux/amd64 (ensuring x86_64 compatibility)"

    # Create a Dockerfile to extract nginx with explicit platform targeting
    cat > "$BUILD_OUTPUT_DIR/Dockerfile.nginx" << EOF
FROM --platform=linux/amd64 nginx:$NGINX_VERSION-alpine AS nginx_base

# Install additional modules if needed
RUN apk add --no-cache nginx-mod-http-headers-more

# Verify architecture
RUN echo "Architecture check:" && uname -m && file /usr/sbin/nginx

FROM scratch AS export
COPY --from=nginx_base /usr/sbin/nginx /nginx/
COPY --from=nginx_base /usr/lib/nginx/ /nginx/modules/
COPY --from=nginx_base /etc/nginx/ /nginx/conf/
COPY --from=nginx_base /var/log/nginx/ /nginx/logs/
COPY --from=nginx_base /var/cache/nginx/ /nginx/cache/
COPY --from=nginx_base /usr/share/nginx/ /nginx/html/
COPY --from=nginx_base /lib/ld-musl-*.so.1 /nginx/lib/
COPY --from=nginx_base /lib/libc.musl-*.so.1 /nginx/lib/
COPY --from=nginx_base /usr/lib/libpcre.so.* /nginx/lib/
COPY --from=nginx_base /usr/lib/libssl.so.* /nginx/lib/
COPY --from=nginx_base /usr/lib/libcrypto.so.* /nginx/lib/
COPY --from=nginx_base /lib/libz.so.* /nginx/lib/
EOF

    # Extract nginx and its dependencies with explicit platform targeting
    log_info "Extracting nginx binary and dependencies (linux/amd64)..."

    # Use buildx if available for better cross-platform support
    if docker buildx version &> /dev/null; then
        log_info "Using Docker buildx for cross-platform nginx extraction"
        docker buildx build \
            --platform linux/amd64 \
            -f "$BUILD_OUTPUT_DIR/Dockerfile.nginx" \
            --target export \
            --output "$BUILD_OUTPUT_DIR" \
            "$PROJECT_ROOT"
    else
        log_info "Using standard Docker build with platform specification"
        docker build \
            --platform linux/amd64 \
            -f "$BUILD_OUTPUT_DIR/Dockerfile.nginx" \
            --target export \
            --output "$BUILD_OUTPUT_DIR" \
            "$PROJECT_ROOT"
    fi

    if [ ! -f "$BUILD_OUTPUT_DIR/nginx/nginx" ]; then
        log_error "Nginx extraction failed - nginx binary not found"
        exit 1
    fi

    # Verify the extracted binary architecture
    log_info "Verifying nginx binary architecture..."
    if command -v file &> /dev/null; then
        local binary_info=$(file "$BUILD_OUTPUT_DIR/nginx/nginx")
        log_info "Nginx binary info: $binary_info"

        if echo "$binary_info" | grep -q "x86-64\|x86_64"; then
            log_success "✓ Nginx binary is x86_64 compatible"
        elif echo "$binary_info" | grep -q "aarch64\|ARM64"; then
            log_error "✗ Nginx binary is ARM64 - this will not work on Rocky Linux 8.10 x86_64"
            log_error "The Docker platform targeting may have failed"
            exit 1
        else
            log_warning "Could not verify nginx binary architecture"
            log_info "Binary info: $binary_info"
        fi
    else
        log_warning "file command not available - cannot verify binary architecture"
    fi

    log_success "Nginx extraction completed"
}

# Function to create deployment package
create_deployment_package() {
    local target_env="$1"
    local package_name="labwise-web-standalone-$target_env-$(date +%Y%m%d-%H%M%S)"
    local package_dir="$DEPLOY_PACKAGE_DIR/$package_name"
    
    log_info "Creating deployment package: $package_name"
    
    # Clean and create package directory
    rm -rf "$package_dir"
    mkdir -p "$package_dir"
    
    # Copy nginx binary and dependencies
    cp -r "$BUILD_OUTPUT_DIR/nginx" "$package_dir/"
    chmod +x "$package_dir/nginx/nginx"
    
    # Copy frontend assets
    mkdir -p "$package_dir/html"
    cp -r "$BUILD_OUTPUT_DIR/dist"/* "$package_dir/html/"
    
    # Copy and adapt nginx configuration
    mkdir -p "$package_dir/conf"
    cp "$PROJECT_ROOT/deploy/nginx.conf.standalone" "$package_dir/conf/nginx.conf"
    cp "$PROJECT_ROOT/deploy/mime.types" "$package_dir/conf/"
    
    # Create logs and cache directories
    mkdir -p "$package_dir/logs" "$package_dir/cache" "$package_dir/run"
    
    # Copy deployment scripts
    cp "$SCRIPT_DIR/deploy.sh" "$package_dir/"
    cp "$SCRIPT_DIR/start.sh" "$package_dir/"
    cp "$SCRIPT_DIR/stop.sh" "$package_dir/"
    cp "$SCRIPT_DIR/status.sh" "$package_dir/"
    chmod +x "$package_dir"/*.sh
    
    # Create package info file with architecture details
    local host_arch=$(detect_host_architecture)
    local host_platform=$(uname -s | tr '[:upper:]' '[:lower:]')

    cat > "$package_dir/package-info.txt" << EOF
Labwise Web Standalone Deployment Package
=========================================

Package: $package_name
Environment: $target_env
Build Date: $(date)
Build Host: $(hostname)
Build Platform: $host_platform/$host_arch
Target Platform: linux/amd64 (Rocky Linux 8.10 x86_64)
Nginx Version: $NGINX_VERSION

Architecture Compatibility:
- Built for: linux/amd64 (x86_64)
- Compatible with: Rocky Linux 8.10, CentOS 8, RHEL 8, Ubuntu x86_64
- NOT compatible with: ARM64/aarch64 systems

This package contains:
- Nginx binary and dependencies (linux/amd64)
- Frontend static assets
- Configuration files
- Deployment scripts

For deployment instructions, see README.md
EOF

    # Create README
    cp "$SCRIPT_DIR/README.md" "$package_dir/"
    
    # Create tarball
    log_info "Creating deployment tarball..."
    cd "$DEPLOY_PACKAGE_DIR"
    tar -czf "$package_name.tar.gz" "$package_name"
    
    log_success "Deployment package created: $DEPLOY_PACKAGE_DIR/$package_name.tar.gz"
    log_info "Package size: $(du -h "$package_name.tar.gz" | cut -f1)"
    
    echo "$package_name.tar.gz"
}

# Main function
main() {
    local target_env
    target_env=$(detect_environment "$1")
    
    log_info "Starting standalone deployment build for environment: $target_env"
    
    # Clean and create build directories
    rm -rf "$BUILD_OUTPUT_DIR" "$DEPLOY_PACKAGE_DIR"
    mkdir -p "$BUILD_OUTPUT_DIR" "$DEPLOY_PACKAGE_DIR"
    
    # Check prerequisites
    check_prerequisites
    
    # Build frontend
    build_frontend "$target_env"
    
    # Extract nginx
    extract_nginx
    
    # Create deployment package
    local package_file
    package_file=$(create_deployment_package "$target_env")
    
    log_success "Standalone deployment build completed successfully!"
    log_info "Deployment package: $DEPLOY_PACKAGE_DIR/$package_file"
    log_info ""

    # Architecture compatibility reminder
    local host_arch=$(detect_host_architecture)
    if [ "$host_arch" != "amd64" ]; then
        log_success "✓ Cross-platform build completed successfully!"
        log_info "Built on: $(uname -s | tr '[:upper:]' '[:lower:]')/$host_arch"
        log_info "Target: linux/amd64 (Rocky Linux 8.10 x86_64)"
        log_info ""
    fi

    log_info "Package Architecture: linux/amd64 (x86_64)"
    log_info "Compatible with: Rocky Linux 8.10, CentOS 8, RHEL 8, Ubuntu x86_64"
    log_warning "NOT compatible with ARM64/aarch64 systems"
    log_info ""

    log_info "Next steps:"
    log_info "1. Transfer the package to your target Rocky Linux 8.10 x86_64 system"
    log_info "2. Extract: tar -xzf $package_file"
    log_info "3. Run: ./deploy.sh"
    log_info ""
    log_info "Troubleshooting:"
    log_info "- If you get 'Exec format error', verify target system is x86_64"
    log_info "- Check architecture: uname -m (should show x86_64)"
    log_info ""
}

# Show usage
show_usage() {
    echo "Usage: $0 [environment]"
    echo ""
    echo "Environments:"
    echo "  develop    - Development environment (default)"
    echo "  test       - Test environment"
    echo "  product    - Production environment"
    echo "  demo       - Demo environment"
    echo ""
    echo "Examples:"
    echo "  $0                 # Build for develop environment"
    echo "  $0 product         # Build for production environment"
    echo "  UMI_ENV=test $0    # Build for test environment using env var"
}

# Parse command line arguments
case "${1:-}" in
    -h|--help)
        show_usage
        exit 0
        ;;
    *)
        main "$1"
        ;;
esac
