@import '@/style/variables.less';
@font-face {
  font-family: 'PinFang-Medium';
  src: local('PingFang SC'), local('Hiragino Sans GB'), local('Microsoft Yahei'),
    local(sans-serif);
}

@font-face {
  font-family: 'NotoSans-Medium';
  src: local('Arial'), local('Helvetica'), local(sans-serif);
}

.labWiseAvatar {
  width: 25px;
  height: 25px;
  overflow: hidden;
  border: 1px solid @color-design-a;
  border-radius: 50%;
  img {
    width: 25px;
    height: 25px;
  }
}

* {
  box-sizing: border-box !important;
  max-width: 100vw;
  margin: 0;
  padding: 0;
  font-family: 'PinFang-Medium' !important;
  /* font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji' !important; */
  // outline-color: #32c5ff !important;
  // scrollbar-color: #66ccff rgb(239, 239, 239);
  scrollbar-width: thin;
  img {
    image-rendering: -moz-crisp-edges;
    image-rendering: -o-crisp-edges;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    -ms-interpolation-mode: nearest-neighbor;
  }
}

html {
  color: #000;
  font-size: 12px;
}

html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
}

.colorWeak {
  filter: invert(80%);
}

.ant-btn-default {
  border-radius: 2px;
}

.ant-page-header-breadcrumb {
  padding-block-start: unset !important;
}

.ant-btn-primary {
  background: @fill-bg-primary-button;
  border-radius: 2px;
}

.ant-layout {
  min-height: 100vh;
  &-sider {
    background-color: #fff;
  }
}

.ant-float-btn {
  right: 5px;
  bottom: 10px;
}

.ant-pro-layout-container {
  min-width: calc(100vw - @siderWidth) !important;
  min-height: 100vh !important;
  overflow-x: hidden;
}

.ant-pro-card-body {
  padding-inline: 12px !important;
}

.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
  background-color: #fff;
}

.ant-layout-content {
  padding: @main-layout-padding_top @layout-horizontal-padding 0 !important;
  overflow: hidden;
}

canvas {
  display: block;
}

body {
  width: 100%;
  // user-select: none; // 禁止复制网页文本
  height: 100vh;
  overflow-x: hidden !important;
  // min-width: 1366px !important;
  // overflow-x: scroll !important;
  overflow-y: auto;
  overflow-y: overlay;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overscroll-behavior-y: none;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

/* 自定义滚动条样式 */
body::-moz-scrollbar {
  width: 6px;
  height: 6px;
  border-radius: 5px !important;
}

/* 滚动条宽 */
body::-webkit-scrollbar {
  width: @scrollbar-width !important;
  height: 10px !important;
  border-radius: 3px !important;
}

/* 滚动条 拖动条 */
body::-webkit-scrollbar-thumb {
  background: #66ccff !important;
  border-radius: 6px !important;
}

/* 滚动条背景槽 */
body::-webkit-scrollbar-track {
  background: rgb(239, 239, 239) !important;
  border-radius: 3px !important;
}

body {
  margin: 0;
  padding: 0;
  ul,
  ol,
  dl,
  dd,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  figure,
  form,
  fieldset,
  legend,
  input,
  textarea,
  button,
  p,
  blockquote,
  th,
  td,
  pre,
  xmp {
    margin: 0;
    padding: 0;
  }
}

body,
input,
textarea,
button,
select,
pre,
xmp,
tt,
code,
kbd,
samp {
  line-height: 1.5;
}

h1,
h2,
h3,
h4,
h5,
h6,
small,
big,
input,
textarea,
button,
select {
  font-size: 100%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
}

textarea {
  overflow: auto;
  resize: vertical;
}

input,
textarea,
button,
select,
a {
  outline: 0 none;
}

.display-flex {
  display: flex;
}

.flex-align-items-center {
  .display-flex;
  align-items: center;
}

.display-flex-center {
  .display-flex;
  justify-content: center;
}

.flex-justify-content-start {
  .display-flex;
  justify-content: flex-start;
}

.flex-justfy-content-end {
  .display-flex;
  justify-content: flex-end;
}

.flex-align-items-start {
  .display-flex;
  align-items: flex-start;
}

.flex-justify-space-between {
  .display-flex;
  justify-content: space-between;
}

.flex-auto {
  flex: 1;
}

.flex-align-space-between {
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
}

.flex-center {
  .display-flex;
  align-items: center;
  justify-content: center;
}

.loadingPage {
  height: 100%;
  .flex-center;
}

// -------------

/* 1400宽页面 */
.mainContent {
  width: 1400px;
  height: auto;
  margin: 0 auto;
}

.stickyHeader {
  position: -webkit-sticky;
  position: sticky;
  top: 0px;
}

.enabledUserSelect {
  user-select: text !important;
}

.searchForm {
  margin: 10px 0;
  padding: 15px 15px;
  background-color: #fff;

  :global {
    .ant-form-item {
      padding: 0 10px;
    }
  }
}

/* Custom Style */
// .ant-modal-content antd面板内容零边距
.paddingZeroModal {
  // user-select: none !important;
  :global {
    .ant-modal-header {
      border-radius: 8px;
    }
    .ant-modal-content {
      border-radius: 9px;
    }
    .ant-modal-body {
      padding: 20px 0px 0px 0px !important;
      font-size: 14px;
      line-height: 1.5715;
      word-wrap: break-word;
    }
    .ant-table-pagination.ant-paginatgion {
      margin: 16px 0px;
      padding-right: 16px;
    }
  }
}

.query-button {
  display: flex !important;
  flex: 1;
  align-items: flex-start !important;
  justify-content: flex-end !important;
}

.none {
  display: none !important;
}

.hidden {
  visibility: hidden;
}

/* 超长缩略隐藏 */
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
}

/* 多行省略号(2行) */
.multi-ellipsis-l2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* tooltip样式 */
.globa1Too1Tip {
  .ant-tooltip-content {
    margin-top: 2px;
    .ant-tooltip-arrow-content {
      width: 9px;
      height: 9px;
      background-color: white;
    }
    .ant-tooltip-inner {
      padding: 10px;
      color: rgba(102, 102, 102, 1) !important;
      font-size: 12px !important;
      background-color: #fff !important;
      border-radius: 6px;
    }
  }
}

/* 红色字体 */
.red {
  color: red;
}

.importColor {
  color: #29b7b7;
  font-weight: bold;
}
.enablePointer {
  color: @fill-cursor;
  cursor: pointer !important;
}

.cursorPointer:hover {
  .enablePointer;
}

.ant-layout-footer {
  height: @footer-height;
  padding: 0 !important;
  background: #f5f5f5;
}

/* custom layout style */
.ant-pro-grid-content {
  padding: 0px !important;
  .ant-pro-page-container-children-container {
    padding: 0px !important;
    overflow-x: hidden;
  }
}

.commonContainer {
  width: 100% !important;
  overflow-x: hidden !important;
}

.ant-pro-page-container {
  .commonContainer;
  .ant-page-header {
    padding: 10px 0 10px 10px !important;
    .ant-page-header-heading {
      display: none; // 页面title不展示，由面包屑替代
    }
  }
}

/* 已操作/已阅 */
.complete {
  color: @fill-purple;
  svg {
    fill: @fill-purple;
  }
}

.hiddenElement {
  position: absolute;
  top: -9999px;
  left: -9999px;
}

.layoutRighButtons {
  position: absolute;
  right: 0px;
  display: flex;
  justify-content: end;
  margin-bottom: 10px;
  button {
    .flex-align-items-center;
  }
  button:hover {
    svg {
      fill: @color-design-e;
      path {
        stroke: @color-design-e;
      }
    }
  }
}

.ant-form {
  background: #fff;
  border-radius: 8px;
}

.fullLayoutContent {
  min-height: @main-content-height_hasCrumbs;
}

.disabledTip {
  color: @fill-disabled !important;
  cursor: not-allowed !important;
}

.ant-pro-sider-actions {
  z-index: @sider-extra-ZIndex;
}

.commonStructure {
  width: 160px;
  max-width: 240px;
  height: auto;
  border: 1px solid @border-color-base;
}

.smilesItem {
  .commonStructure;
  height: 68px !important;
  max-height: 68px;
}

.mt8 {
  margin-top: 8px;
}

.mt10 {
  margin-top: 10px;
}

.mt20 {
  margin-top: 20px;
}

.mb16 {
  margin-bottom: 16px;
}

.mb10 {
  margin-bottom: 10px;
}

.subTitle {
  font-weight: 650;
  font-size: 16px;
  line-height: 20px;
  letter-spacing: 0em;
}

.noPaddingCard {
  .ant-card-body {
    padding: 0px;
  }
}

/* custom Plyr video style */
.plyr--pip-supported [data-plyr='pip'],
.plyr--full-ui.plyr--video .plyr__control--overlaid {
  display: none !important;
}

.plyr__volume,
.plyr__menu {
  display: none !important;
}
