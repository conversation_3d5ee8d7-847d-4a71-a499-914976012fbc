import {
  DEL_QUOTE_NO,
  EXEPERIMENT_ANALYSIS_REPORT,
  EXEPERIMENT_CHECK_OPERATORS,
  EXEPERIMENT_CHECK_SEARCH,
  EXEPERIMENT_DESIGNS_MATERIAL,
  EXEPERIMENT_PLAN_NO,
  EXPERIMENT_PLANS_PLAN,
  LOGIN,
  MS_USER_INFO,
  UPLOAD_ANALYSIS_REPORT,
  USER_INFO
} from '@/constants/urls'
import { createApi } from '@/services/request'
import { CreateRequest } from '@/types/ApiType'
export const apiLogin: CreateRequest<any> = createApi({
  path: LOGIN
})

export const currentUser: CreateRequest<any> = createApi({
  path: USER_INFO,
  defaultMethod: 'GET'
})

export const currentMicrosoftUser: CreateRequest<any> = createApi({
  path: MS_USER_INFO,
  defaultMethod: 'GET'
})

/**
 * 删除重复报价
 */
export const apiDelByQuoteNo: CreateRequest = createApi({
  path: DEL_QUOTE_NO,
  defaultMethod: 'DELETE'
})

/**
 * 实验设计更新物料表
 */
export const apiUpdateExperimentDesignsMaterial: CreateRequest = createApi({
  path: EXEPERIMENT_DESIGNS_MATERIAL
})

/**
 * 查询检测记录
 */
export const apiSearchExperimentCheck: CreateRequest = createApi({
  path: EXEPERIMENT_CHECK_SEARCH
})

export const apiExperimentPlanNo: CreateRequest = createApi({
  path: EXEPERIMENT_PLAN_NO,
  defaultMethod: 'GET'
})

export const apiGetExperimentPlanNos: CreateRequest = createApi({
  path: EXPERIMENT_PLANS_PLAN,
  defaultMethod: 'GET'
})
export const apiExperimentChekOperators: CreateRequest = createApi({
  path: EXEPERIMENT_CHECK_OPERATORS,
  defaultMethod: 'GET'
})

export const apiExperimentAnalysisReport: CreateRequest = createApi({
  path: EXEPERIMENT_ANALYSIS_REPORT,
  defaultMethod: 'GET'
})

export const apiUploadAnalysisReport: CreateRequest = createApi({
  path: UPLOAD_ANALYSIS_REPORT
})
