import { MaterialTable } from '../brain'

/**
 * ExperimentConclusionDetailResponse
 */
export interface ExperimentConclusionDetailResponse {
  /**
   * 实验分析记录
   */
  analysis_records: ExperimentAnalysis[]
  /**
   * 实验设计Id
   */
  experiment_design_id: number
  /**
   * 审核者
   */
  auditor?: string
  /**
   * 实验结论
   */
  conclusion?: ExperimentConclusion
  /**
   * 实验结果, success | failed
   */
  conclusion_result?: string
  /**
   * 实验实际结束时间
   */
  end_time?: string
  /**
   * 预估收率,[0.0, 100.0]
   */
  estimate_yield?: number
  /**
   * 实验编号
   */
  experiment_no: string
  /**
   * 实验负责人
   */
  experiment_owner?: string
  /**
   * 实验操作记录
   */
  operation_records: ExperimentOperation[]
  /**
   * 实验反应式
   */
  rxn_smiles: string
  /**
   * 实验实际开始时间
   */
  start_time?: string
  /**
   * 实验状态
   */
  status: ExperimentStatus
  materials: MaterialTable[]
}

/**
 * ExperimentAnalysis
 */
export interface ExperimentAnalysis {
  /**
   * 检测方式
   */
  check_method: CheckMethod
  /**
   * 检测样品ID
   */
  check_no: string
  /**
   * 检测类型
   */
  check_type: CheckType
  /**
   * 是否参考报告
   */
  is_reference?: boolean
  /**
   * 送样时间
   */
  send_time?: string
  /**
   * 检测状态
   */
  status?: CheckStatus
  /**
   * 检测报告链接
   */
  report_url?: string
}

export interface ExperimentAnalysisCreate extends Partial<ExperimentAnalysis> {
  experiment_no: string
  start_from: 'web'
  check_type: CheckType
  check_method: CheckMethod
  checker: string
  solvents?: SolventCheck[]
}

export interface ExperimentAnalysisUpdate extends Partial<ExperimentAnalysis> {
  check_no: string
  start_from: 'web'
}

export interface SolventCheck {
  /**
   * 配比，ratio
   */
  ratio?: number
  /**
   * 展开剂，Solvent
   */
  solvent_name: string
}

/**
 * 实验结论
 *
 * ExperimentConclusion
 */
export interface ExperimentConclusion {
  /**
   * 颜色
   */
  color?: string
  /**
   * 备注
   */
  comment?: string
  /**
   * 收率，百分比数值[0.0, 100.0]
   */
  experiment_yield?: number
  /**
   * 其他描述
   */
  other?: string
  /**
   * 固体或液体
   */
  phase?: string
  /**
   * 纯度,百分比数值[0.0, 100.0]
   */
  purity?: number
}

/**
 * ExperimentOperation
 */
export interface ExperimentOperation {
  /**
   * 任务结束时间
   */
  end_time?: string
  /**
   * 异常次数
   */
  error_count?: number
  /**
   * 实验编号
   */
  experiment_no: string
  /**
   * 是否需要补填
   */
  need_amend?: boolean
  /**
   * 任务开始时间
   */
  start_time?: string
  /**
   * 任务描述
   */
  task_desc: string
  /**
   * 任务id
   */
  task_id: number
  /**
   * 任务名
   */
  task_name: string
  /**
   * 任务编号
   */
  task_no: string
  /**
   * 任务状态
   */
  task_status?: string
}

/**
 * 实验状态
 *
 * ExperimentStatus，An enumeration.
 */
export type ExperimentStatus =
  | 'running'
  | 'hold'
  | 'failed'
  | 'completed'
  | 'success'
  | 'canceled'

/**
 * 实验结论
 */
export type ExperimentConclusionResult = 'failed' | 'success'

/**
 * ExperimentConclusionRequestBody
 */
export interface ExperimentConclusionRequestBody {
  /**
   * 实验结论
   */
  conclusion: ExperimentConclusion
  /**
   * 实验编号
   */
  experiment_no: string
}

/**
 * ExperimentConclusionResultRequestBody
 */
export interface ExperimentConclusionResultRequestBody {
  /**
   * 实验最终结论
   */
  conclusion_result: ExperimentConclusionResult
  /**
   * 实验编号
   */
  experiment_no: string
}

/**
 * ExperimentAuditorRequestBody
 */
export interface ExperimentAuditorRequestBody {
  /**
   * 审核者
   */
  auditor: string
  /**
   * 实验编号
   */
  experiment_no: string
}

/**
 * value can be TLC, LCMS
 *
 * CheckMethod，An enumeration.
 */
export type CheckMethod = 'TLC' | 'LCMS' | 'NMR'

/**
 * 中控的状态，running, completed
 *
 * CheckStatus，An enumeration.
 */
export type CheckStatus = 'todo' | 'checking' | 'canceled' | 'finished'

/**
 * 检测类型，检测类型
 *
 * CheckType，An enumeration.
 */
export type CheckType = 'M' | 'F'
