import {
  EXPERIMENT_ANALYSIS_CHECK,
  EXPERIMENT_AUDITOR,
  EXPERIMENT_CONCLUSION,
  EXPERIMENT_CONCLUTION_RESULT,
  EXPERIMENT_RESULT,
  LAB_SOLVENTS
} from '@/constants'
import { CreateRequest, Result } from '@/types/ApiType'
import { createApi } from '../request'
import {
  ExperimentAnalysis,
  ExperimentAnalysisCreate,
  ExperimentAnalysisUpdate,
  ExperimentAuditorRequestBody,
  ExperimentConclusionRequestBody,
  ExperimentConclusionResultRequestBody,
  type ExperimentConclusionDetailResponse
} from './index.d'

export const apiExperimentConclusionDetail: CreateRequest<
  null,
  null,
  Result<ExperimentConclusionDetailResponse>
> = createApi({
  path: EXPERIMENT_RESULT,
  defaultMethod: 'GET'
})

export const apiUpdateExperimentConclusion: CreateRequest<
  null,
  ExperimentConclusionRequestBody,
  Result<ExperimentConclusionDetailResponse>
> = createApi({
  path: EXPERIMENT_CONCLUSION,
  defaultMethod: 'PUT'
})

export const apiUpdateExperimentConclusionResult: CreateRequest<
  null,
  ExperimentConclusionResultRequestBody,
  Result<ExperimentConclusionDetailResponse>
> = createApi({
  path: EXPERIMENT_CONCLUTION_RESULT,
  defaultMethod: 'PUT'
})

export const apiUpdateExperimentAuditor: CreateRequest<
  null,
  ExperimentAuditorRequestBody,
  Result<ExperimentConclusionDetailResponse>
> = createApi({
  path: EXPERIMENT_AUDITOR,
  defaultMethod: 'PUT'
})

export const apiCreateExperimentAnalysis: CreateRequest<
  null,
  ExperimentAnalysisCreate,
  Result<ExperimentAnalysis>
> = createApi({
  path: EXPERIMENT_ANALYSIS_CHECK,
  defaultMethod: 'POST'
})

export const apiUpdateExperimentAnalysis: CreateRequest<
  null,
  ExperimentAnalysisUpdate,
  Result<ExperimentAnalysis>
> = createApi({
  path: EXPERIMENT_ANALYSIS_CHECK,
  defaultMethod: 'POST'
})

export const apiGetSolvents: CreateRequest<
  null,
  null,
  Result<{ name: string }[]>
> = createApi({
  path: LAB_SOLVENTS,
  defaultMethod: 'GET'
})
