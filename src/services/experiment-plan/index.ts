import {
  EXPERIMENT_PLANS,
  EXPERIMENT_PLANS_LIST,
  SMILES_WEIGHT
} from '@/constants'
import { createApi } from '@/services/request'
import type { CreateRequest, Result } from '@/types/ApiType'
import type { ExperimentPlanModel } from '@types'

/* 获取具体需求时 路径参数作为id `/experiment-plans/{id}` */
export const apiExperimentPlansList: CreateRequest<any> = createApi({
  path: EXPERIMENT_PLANS_LIST
})

export const apiCreateExperimentPlan: CreateRequest<ExperimentPlanModel> =
  createApi({
    path: EXPERIMENT_PLANS
  })

export const apiUpdateExperimentPlan: CreateRequest<ExperimentPlanModel> =
  createApi({
    path: EXPERIMENT_PLANS,
    defaultMethod: 'PUT'
  })

export const apiExperimentPlanDetail: CreateRequest<any> = createApi({
  path: EXPERIMENT_PLANS,
  defaultMethod: 'GET'
})

export const apiGetSmilesWeight: CreateRequest<
  null,
  string[],
  Result<Record<string, number>>
> = createApi({
  path: SMILES_WEIGHT,
  defaultMethod: 'POST'
})
