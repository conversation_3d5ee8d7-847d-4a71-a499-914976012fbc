import { ROUTE_DETAIL, TARGET_MOLECULES } from '@/constants/urls'
import { createApi } from '@/services/request'
import { CreateRequest } from '@/types/ApiType'
/**
 * 分子列表（查询）
 * TODO [filters](https://docs.strapi.io/dev-docs/api/rest/filters-locale-publication)
 * filters:{
 *    projectNo: string 项目编号
 *    moleculeNo 分子编号
 *    status 分子状态  value: designing experimenting done cancel
 * }
 * page
 * pageSize
 * ---------
 * （响应）
 * smiles 结构式
 * 已应用路线、已编辑路线 遍历判断routes中status
 */
export const apiMoleculesList: CreateRequest<any> = createApi({
  path: TARGET_MOLECULES
})

/**
 * 添加目标分子（创建分子）
 */
export const apiCreateMolecule: CreateRequest<any> = createApi({
  path: TARGET_MOLECULES,
  defaultMethod: 'POST'
})

/**
 * 点击调整路线设计，获取 路线设计列表（AI合成路线、已编辑路线、已应用路线列数据）
 */
export const apiRouteListDetail: CreateRequest<any> = createApi({
  path: ROUTE_DETAIL
})

/* TODO
请求：
最大步长(步)：
原料最大成本(元/g)：
路线数量(条)：
排除（子结构、原料、单步反应）：
page
pageSize
*/

/**
 * 编辑路线（详情获取） /routes/{id}
 */
export const apiRouteDetail: CreateRequest<any> = createApi({
  path: ROUTE_DETAIL,
  defaultMethod: 'GET' // TODO 请求参数：id传值取 /routes响应字段routeNo
})
/* TODO procedures 内字段不完整 目标分子用哪个字段？ */

/**
 * 编辑路线（保存） /routes/{id} PUT
 * TODO 值传递 routeTree
 */
export const apiEditRoute: CreateRequest<any> = createApi({
  path: ROUTE_DETAIL,
  defaultMethod: 'PUT'
})

/**
 * 应用路线 /routes/{id} PUT
 * TODO 请求参数： 仅传递 routeNo status 两个字段
 */
export const apiApplyRoute: CreateRequest<any> = createApi({
  path: ROUTE_DETAIL,
  defaultMethod: 'PUT'
})
