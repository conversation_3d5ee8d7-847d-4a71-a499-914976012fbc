import {
  ENVIRONMENT_CONFIG,
  LOGIN_LOCAL,
  NEW_USER_INFO,
  ROBOT_TASKS
} from '@/constants/urls'
import { createApi } from '@/services/request'
import { CreateRequest } from '@/types/ApiType'
import { ILoginLocal, IUpdateInfo } from '@/types/RestfulService'
import { set } from '@/utils/storage'
import { query } from './brain'

// e.g. 用户信息设置
export const UpdateInfo: CreateRequest<IUpdateInfo> = createApi({
  path: `user/update/info`
})

export const apiLoginLocal: CreateRequest<ILoginLocal> = createApi({
  path: LOGIN_LOCAL
})

export const apiGetUserInfo: CreateRequest<any> = createApi({
  path: NEW_USER_INFO,
  defaultMethod: 'GET'
})

/* 获取机器人任务list */
export const apiRobotTasks: CreateRequest<any> = createApi({
  path: ROBOT_TASKS,
  defaultMethod: 'GET'
})

export const updateEnvConfig = async () => {
  const { data } = await query<any>(ENVIRONMENT_CONFIG).get()
  set('environmentConfig', data || {})
}
