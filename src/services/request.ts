import { getLocaleTag } from '@/components/BpmnEditor/utils'
import {
  DEL_QUOTE_NO,
  LOGIN_LOCAL,
  MS_USER_INFO,
  NEW_USER_INFO
} from '@/constants'
import type {
  ApiCreatorParams,
  CreateRequest,
  RequestCreatorParams
} from '@/types/ApiType'
import {
  detectIsNoPermission,
  getEnvConfig,
  getToken,
  toLoginPage
} from '@/utils'
import Logger from '@/utils/Logger'
import { message, notification } from 'antd'
import originAxios, { AxiosError } from 'axios'
import { isNil } from 'lodash'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import qs from 'query-string'
/**
 * 一、功能：
 * 1. 统一拦截http错误请求码；
 * 2. 统一拦截业务错误代码；
 * 3. 统一设置请求前缀；
 * 4. 配置异步请求过渡状态：显示蓝色加载条表示正在请求中，避免给用户页面假死的不好体验。
 * 5. 增加一个自定义实例，指定为请求超出时间为20s
 */
const axios = originAxios.create({
  timeout: 300 * 1000 // 指定为请求超出时间为30s
})
// TODO 前端多语言，收集所有文案中英文，和后端约定request header语言标识 配置 headers 相关
// TODO better 登录鉴权  jwt

axios.defaults.headers.post['Content-Type'] =
  'application/x-www-form-urlencoded'
axios.defaults.withCredentials = false // true 设置全局参数，如响应超时时间，请求前缀等 true;
// 服务端在 response 的 header 中配置"Access-Control-Allow-Credentials", "true"

// 状态码错误信息
const codeMessage = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
  599: '执行出错' /* 公司自定义错误状态码 */
}

// 添加一个请求拦截器，用于设置请求过渡状态
axios.interceptors.request.use(
  (config) => {
    const token = getToken()
    if (!isNil(token)) config.headers.Authorization = `Bearer ${token}`
    config.headers.locale = getLocaleTag()
    // 请求开始，蓝色过渡滚动条开始出现
    NProgress.start()
    /*  if (config?.headers?.canLoading !== false) showLoading() */
    // 判断当前请求是否设置了不显示Loading
    return config
  },
  (error) => {
    /*  if (error?.config?.headers?.canLoading !== false) hideLoading() // 关闭loading */
    NProgress.done()
    return Promise.reject(error)
  }
)

// 添加一个返回拦截器
axios.interceptors.response.use(
  (config) => {
    /* if (config?.headers?.canLoading !== false) hideLoading() */
    NProgress.done() // 请求结束，蓝色过渡滚动条消失
    return config
  },
  (error) => {
    /* if (error?.config?.headers?.canLoading !== false) hideLoading() */
    const { config, message: msg } = error
    if (!config || !config.retry) {
      NProgress.done() // 请求结束，蓝色过渡滚动条消失
      return Promise.reject(error)
    }
    // TODO test retry while Network timeout or Network Error
    if (!(msg.includes('timeout') || msg.includes('Network Error'))) {
      return Promise.reject(error)
    }
    config.retry -= 1
    const delayRetryRequest = new Promise((resolve) => {
      setTimeout(() => {
        console.log('retry the request', config, config.url, config.retryDelay)
        resolve()
      }, config.retryDelay || 1000)
    })
    return delayRetryRequest.then(() => axios(config))
  }
)

/* TODO refactor 错误检测 */
export function parseResponseResult(res: any) {
  return {
    ok: Object.is(res?.status, 200), // 始终通过http code码判断，200时为成功
    msg: res?.detail || '服务异常' // 错误时，响应字段 detail 会有值
  }
}

const redirectEvent = async (status: number, errorMessage?: string) => {
  detectIsNoPermission(status, errorMessage)
  if (status === 401) {
    if (window.location.href.includes('/?access_token=')) return
    else {
      toLoginPage()
      return message.error(errorMessage || '暂无权限，请联系管理员或重新登录~')
    }
  }
}

/* TODO: refactor request fn */
export default function request(opt) {
  const { params, url, data, ...restOpt } = opt
  const composedOpt = {
    ...restOpt,
    url: params ? url : `${url}?${qs.stringify(params)}`,
    params: params,
    data: data,
    headers: {
      token: getToken() || undefined,
      lang: getLocaleTag()
    }
  }
  // 调用 axios api，统一拦截
  return axios(composedOpt)
    .then((response: any) => {
      Logger.prettySuccess(
        `${opt.method} ${opt.url}，响应数据：`,
        response?.data
      )
      if ([403, 401].includes(response?.data?.code)) {
        return redirectEvent(
          response?.data?.code,
          response?.data?.error?.message
        )
      }
      if (!parseResponseResult(response).ok)
        return message.error(parseResponseResult(response).msg)
      return response
    })
    .catch((error: AxiosError) => {
      if (!error.response) return console.log('Error', error.message)
      const status: number = error?.response?.status // 响应时状态码处理
      if ([401, 403].includes(status))
        return redirectEvent(status, error?.response?.data?.error?.message)
      /* TODO 422 detail格式有误，待后端修复 */
      const errortext =
        status !== 422
          ? error?.response?.data?.detail
          : codeMessage[status] || status || '请求出错，请联系管理员～'
      notification.error({
        message: status === 599 ? codeMessage[status] : `请求错误 ${status}`,
        description: errortext
      })
      return { code: status, message: errortext } // 存在请求，但是服务器的返回一个状态码，它们都在2xx之外
    })
}

const basePath = `${getEnvConfig().apiBase}/api/run`
const getRequestUrl = (url: string, routeParams?: string, params?: any) => {
  if (routeParams) return `${url}/${routeParams}`
  else if (params?.query) return `${url}?${params?.query}`
  else return url
}

export const createApi = (
  {
    path = '',
    defaultMethod = 'POST'
  }: ApiCreatorParams = {} as ApiCreatorParams
): CreateRequest => {
  return (
    {
      method = defaultMethod,
      url = path.includes(DEL_QUOTE_NO) ||
      path.includes('auth/keycloak/') ||
      path.includes(NEW_USER_INFO) ||
      path.includes(MS_USER_INFO) ||
      path.includes(LOGIN_LOCAL)
        ? `${getEnvConfig().apiBase}/api${path}`
        : `${basePath}${path}`,
      params = {},
      data = {},
      routeParams
    } = {} as RequestCreatorParams
  ) => {
    return request({
      method,
      url: getRequestUrl(url, routeParams, params),
      params,
      data
    })
  }
}
