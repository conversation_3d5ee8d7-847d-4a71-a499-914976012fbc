/**
 * ExperimentTaskExceptionResponse
 */
export interface ExperimentTaskExceptionResponse {
  /**
   * 任务操作异常列表
   */
  exceptions?: TaskExceptionItem[]
}

/**
 * TaskExceptionItem
 */
export interface TaskExceptionItem {
  /**
   * 任务异常编号
   */
  exception_id: string
  /**
   * 异常内容
   */
  task_exception?: TaskException
  /**
   * 任务参数
   */
  task_params?: TaskParam[]
}

/**
 * 异常内容
 *
 * TaskException
 */
export interface TaskException {
  /**
   * 异常编码，前端根据对应编码设置对应lang的描述
   */
  exception_code?: string
  /**
   * 异常发生时间
   */
  exception_time?: string
  /**
   * 异常处理类型
   */
  exception_type?: string
  /**
   * 异常原因
   */
  reason?: string
  /**
   * 异常原因类型
   */
  reason_type?: string
}

/**
 * TaskParam
 */
export interface TaskParam {
  /**
   * 参数修正值
   */
  amend_value: string
  /**
   * 参数描述
   */
  name: string
  /**
   * 参数值
   */
  value: string
}

/**
 * Response Get Exception Handle Method Api Exception Handle Method Get
 */
export interface ExceptionHandleMethod {
  attension: null | string
  category: string
  fillUpEnable?: boolean | null
  handlerMethodName: string
  handlerMethodNo: string
  handlerSchema: null
  steps: string
}
