import {
  ADD_REACTION_LIB,
  EXPERIMENT_EXCEPTION,
  EXPERIMENT_EXCEPTION_METHOD,
  EXPERIMENT_EXCEPTION_REASON_TYPE,
  UPDATE_WORKFLOW_PARAM
} from '@/constants'
import { CreateRequest, Result } from '@/types/ApiType'
import { createApi } from '../request'
import {
  ExceptionHandleMethod,
  TaskExceptionItem,
  type ExperimentTaskExceptionResponse
} from './index.d'

export const apiExperimentException: CreateRequest<
  null,
  null,
  Result<ExperimentTaskExceptionResponse>
> = createApi({
  path: EXPERIMENT_EXCEPTION,
  defaultMethod: 'GET'
})

export const apiUpdateExperimentException: CreateRequest<
  null,
  TaskExceptionItem,
  Result<ExperimentTaskExceptionResponse>
> = createApi({
  path: EXPERIMENT_EXCEPTION,
  defaultMethod: 'PUT'
})

export const apiUpdateWorkflowParam: CreateRequest<any> = createApi({
  path: UPDATE_WORKFLOW_PARAM,
  defaultMethod: 'PUT'
})

export const apiAddReactionLib: CreateRequest<any> = createApi({
  path: ADD_REACTION_LIB
})

export const apiListExceptionMethod: CreateRequest<
  { locale?: string },
  null,
  Result<ExceptionHandleMethod[]>
> = createApi({
  path: EXPERIMENT_EXCEPTION_METHOD,
  defaultMethod: 'GET'
})

export const apiListExceptionReasonType: CreateRequest<
  null,
  null,
  Result<Record<string, string>>
> = createApi({
  path: EXPERIMENT_EXCEPTION_REASON_TYPE,
  defaultMethod: 'GET'
})
