import {
  EXPERIMENT_DESIGNS_TASK,
  EXPERIMENT_DESIGNS_TASK_LIST,
  MIX_DETAIL,
  SAVE_MIX_DETAIL
} from '@/constants/urls'
import { createApi } from '@/services/request'
import { CreateRequest } from '@/types/apiType'
import {
  ExperimentDesignGetMixDetailRequest,
  ExperimentDesignUpdateMixDetailRequest,
  ILogin
} from '@types'

export const apiGetTaskList: CreateRequest<ILogin> = createApi({
  path: EXPERIMENT_DESIGNS_TASK_LIST,
  defaultMethod: 'GET'
})

/* 获取task数据 */
export const apiGetTaskInfo: CreateRequest<ILogin> = createApi({
  path: EXPERIMENT_DESIGNS_TASK,
  defaultMethod: 'GET'
})

/* 保存task的数据 */
export const apiSaveTaskInfo: CreateRequest<ILogin> = createApi({
  path: EXPERIMENT_DESIGNS_TASK
})

export const apiGetMixDetail: CreateRequest<ExperimentDesignGetMixDetailRequest> =
  createApi({
    path: MIX_DETAIL,
    defaultMethod: 'GET'
  })

export const apiSaveMixDetail: CreateRequest<ExperimentDesignUpdateMixDetailRequest> =
  createApi({
    path: SAVE_MIX_DETAIL
  })
