import {
  COPY_EXPERIMENT_DESIGNS,
  EXPERIMENT_DESIGNS,
  EXPERIMENT_DESIGNS_LIST,
  EXPERIMENT_DESIGNS_PUBLISH,
  EXPERIMENT_DESIGN_RESULTS,
  EXPERIMENT_PLANS_FEED,
  FROM_PROCEDURE,
  GENERATE_PROCEDURE,
  OPERATION_STATUS,
  PROCEDURES,
  SIMULATE_EXPERIMENT
} from '@/constants'
import { createApi } from '@/services/request'
import { CreateRequest, Result } from '@/types/ApiType'
import type {
  ExperimentDesignFromProcedureRequest,
  ExperimentDesignModel
} from '@types'
import { ExperimentResult } from './index.d'
export const apiCopyExperimentDesign: CreateRequest<any> = createApi({
  path: COPY_EXPERIMENT_DESIGNS
})

export const apiExperimentDesignDetail: CreateRequest<any> = createApi({
  path: EXPERIMENT_DESIGNS,
  defaultMethod: 'GET'
})

export const apiProceduresList: CreateRequest<any> = createApi({
  path: PROCEDURES,
  defaultMethod: 'GET'
})

export const apiExperimentDesigns: CreateRequest<ExperimentDesignModel> =
  createApi({
    path: EXPERIMENT_DESIGNS_LIST
  })

export const apiCreateExperimentDesigns: CreateRequest<ExperimentDesignModel> =
  createApi({
    path: EXPERIMENT_DESIGNS
  })

/**
 * TODO 实验设计（编辑）-更新编辑内容详情  /experiment-designs/{id} PUT
 * 包含了暂存、发布，更改status控制
 * 请求：variables 传啥？ status 是否要传？ procedure对应为文本？
 */
export const apiUpdateExperimentDesigns: CreateRequest<any> = createApi({
  path: EXPERIMENT_DESIGNS,
  defaultMethod: 'PUT'
})

// `/experiment-designs/{id}`  删除 设计
export const apiDeleteExperimentDesigns: CreateRequest<any> = createApi({
  path: EXPERIMENT_DESIGNS,
  defaultMethod: 'DELETE'
})

export const apiPublishExperimentDesigns: CreateRequest = createApi({
  path: EXPERIMENT_DESIGNS_PUBLISH
})

export const apiSimulateDesigns: CreateRequest = createApi({
  path: SIMULATE_EXPERIMENT
})

export const apiGenerateProcedure: CreateRequest =
  createApi<ExperimentDesignFromProcedureRequest>({
    path: GENERATE_PROCEDURE
  })

export const apiGenerateWorkflow: CreateRequest = createApi({
  path: FROM_PROCEDURE
})

export const apiTaskStatus: CreateRequest = createApi({
  path: OPERATION_STATUS,
  defaultMethod: 'GET'
})

export const apiExperimentFeed: CreateRequest = createApi({
  path: EXPERIMENT_PLANS_FEED,
  defaultMethod: 'POST'
})

export const apiGetExperimentDesignResults: CreateRequest<
  null,
  null,
  Result<{ experiment_results: ExperimentResult[] }>
> = createApi({
  path: EXPERIMENT_DESIGN_RESULTS,
  defaultMethod: 'GET'
})
