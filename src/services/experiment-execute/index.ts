import {
  EXPERIMENT_DETAIL,
  EXPERIMENT_REACTION_SEARCH,
  EXPERIMENT_SEARCH,
  EXPERIMENT_STAUTS
} from '@/constants'
import { createApi } from '@/services/request'
import type { CreateRequest } from '@/types/ApiType'
import type { ExperimentStatusRequest } from '@types'

export const apiExperimentList: CreateRequest<any> = createApi({
  path: EXPERIMENT_SEARCH
})

export const apiExperimentDetail: CreateRequest<any> = createApi({
  path: EXPERIMENT_DETAIL,
  defaultMethod: 'GET'
})

export const apiUpdateExperimentStatus: CreateRequest<ExperimentStatusRequest> =
  createApi({
    path: EXPERIMENT_STAUTS,
    defaultMethod: 'PUT'
  })

export const apiExperimentReactionSearch: CreateRequest = createApi({
  path: EXPERIMENT_REACTION_SEARCH
})
