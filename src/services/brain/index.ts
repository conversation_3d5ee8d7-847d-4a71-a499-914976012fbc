import { getLocaleTag } from '@/components/BpmnEditor/utils'
import {
  detectIsNoPermission,
  getEnvConfig,
  getToken,
  toLoginPage
} from '@/utils'
import { StrapiClientOptions, createClient } from '@kmariappan/strapi-client-js'
import type { AxiosError } from 'axios'
import { AxiosRequestConfig, Method } from 'axios'
import { stringify } from 'qs'

interface BrainOption extends StrapiClientOptions {
  customQuery?: string
  method?: Method
  data?: Record<string, any>
  params?: Record<string, any>
}

const defaultOptions: BrainOption = {
  url: `${getEnvConfig().apiBase}/api`,
  apiToken: `${process.env.token}`,
  normalizeData: true,
  headers: {},
  persistSession: false
}

export const strapiClient = createClient(defaultOptions)

export const service = <T>(
  api: ApiName,
  op?: Partial<BrainOption>
): StrapiQueryBuilder<T> => {
  const options = { ...defaultOptions, ...op }
  const service = createClient(options).from<T>(api)
  service['httpClient'].interceptors.request.handlers = []
  service['httpClient'].interceptors.request.use(
    (config: AxiosRequestConfig): AxiosRequestConfig => {
      if (op?.customQuery) {
        if (!config?.url?.includes('?')) {
          config.url += `?${op.customQuery}`
        } else {
          config.url += `&${op.customQuery}`
        }
      }
      const re = {
        ...config,
        method: options.method || config.method,
        data: options.data || config.data,
        params: options.params || config.params,
        paramsSerializer: (params: any) =>
          stringify(params, { arrayFormat: 'brackets' }),
        headers: {
          ...config.headers,
          ...options.headers
        }
      }

      const token = getToken()
      if (token) re.headers.Authorization = `Bearer ${token}`
      re.headers.locale = getLocaleTag()
      return re
    }
  )
  service['httpClient'].interceptors.response.use(
    (config: AxiosRequestConfig) => {
      return config
    },
    (error: AxiosError) => {
      detectIsNoPermission(
        error?.response?.status as number,
        error?.response?.data?.error?.message as string
      )
      if (error?.response?.status === 401) {
        if (sessionStorage.getItem('redirectHref') === window.location.href)
          window.location.href = '/'
        else toLoginPage()
      }
      return Promise.reject(error)
    }
  )
  return service
}

export const query = <T>(
  api: ApiName,
  op?: Partial<BrainOption>,
  fields?: (keyof T)[]
): StrapiFilterBuilder<T> => service<T>(api, op).select(fields)

export const queryWithDefaultOrder = <T>(
  api: ApiName,
  op?: Partial<BrainOption>,
  fields?: (keyof T)[]
): StrapiFilterBuilder<T> =>
  service<T>(api, op)
    .select(fields)
    .sortBy([
      { field: 'createdAt' as keyof T, order: 'desc' },
      { field: 'updatedAt' as keyof T, order: 'desc' }
    ])

export const defaultPageSize = 10

export type ApiName =
  | 'commonts'
  | 'compounds'
  | 'projects'
  | 'project-members'
  | 'project-roles'
  | 'project-status-audits'
  | 'project-compounds'
  | 'project-compound-status-audits'
  | 'retro-processes'
  | 'retro-backbones'
  | 'project-routes'
  | 'retro-reactions'
  | 'quotes'
  | 'project-reactions'
  | 'retro-param-configs'
  | 'job-notifications'
  | 'material-items'
  | 'material-libs'
  | 'material-tags'
  | 'material-black-lists'
  | 'special-materials'
  | 'user-settings'
  | 'material-display-by-names'
  | 'retro-preference-configs'
  | 'batch-retros'
  | (string & Record<never, never>)

export * from './types/index'

// create class to get return type of from method with generic type info
class Wrapper<T> {
  wrap(api: string) {
    return strapiClient.from<T>(api)
  }
}
export type StrapiQueryBuilder<T> = ReturnType<Wrapper<T>['wrap']>
export type StrapiFilterBuilder<T> = ReturnType<StrapiQueryBuilder<T>['select']>
export type StrapiApiResponse<T> = ReturnType<StrapiFilterBuilder<T>['get']>

export interface StrapiPagination {
  page: number
  pageSize: number
  pageCount: number
  total: number
}
export interface StrapiMeta {
  pagination: StrapiPagination
}
export interface StrapiApiError {
  message: string | null
  status: number | null
  name: string | null
  details: any | null
}
export interface StrapiResponse<T> {
  data: T | null
  meta?: StrapiMeta
  error?: StrapiApiError
}
