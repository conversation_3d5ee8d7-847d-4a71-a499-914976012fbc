export enum EventLevel {
  Experiment = 'experiment',
  Global = 'global',
  Project = 'project'
}
export enum MessageLevel {
  I = 'I',
  Ii = 'II',
  Iii = 'III'
}

export interface MessageReaders {
  createdAt?: Date
  createdBy?: string
  event_data?: any
  event_level?: EventLevel
  event_type?: string
  experiment_no?: string
  message?: string
  message_level?: MessageLevel
  project_no?: string
  publishedAt?: Date
  read_items?: any
  updatedAt?: Date
  updatedBy?: any
  readed?: boolean
}

export interface MessageReader {
  createdAt?: Date
  publishedAt?: Date
  readed?: boolean
  system_message?: MessageReaders
  updatedAt?: Date
  user_id?: string
}
