import type { Dayjs } from 'dayjs'

export interface CommonFields {
  id: number
  createdAt?: Dayjs | Date
  publishedAt?: Dayjs | Date
  updatedAt?: Dayjs | Date
}

export interface Paginate {
  page: number
  pageSize: number
}

export * from './batch-retro'
export * from './comment'
export * from './compound'
export * from './login'
export * from './material-black-list'
export * from './material-item'
export * from './material-lib'
export * from './material-tag'
export * from './materials'
export * from './message-readers'
export * from './procedure-create'
export * from './procedure-match'
export * from './procedure-role'
export * from './procedure-rxn-match'
export * from './project'
export * from './project-compound'
export * from './project-compound-status-audit'
export * from './project-member'
export * from './project-reaction'
export * from './project-role'
export * from './project-route'
export * from './project-status-audit'
export * from './quotes'
export * from './retro-backbone'
export * from './retro-param-config'
export * from './retro-process'
export * from './retro-processes'
export * from './retro-reaction'
export * from './retro-reference-config'
export * from './route-search'
export * from './special-material'
export * from './upload-file'
