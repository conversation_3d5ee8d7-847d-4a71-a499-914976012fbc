import { CommonFields } from '.'
import { CollectedRetroBackbone } from './collected-retro-backbone'
import { RetroProcess } from './retro-process'

export interface RetroBackbone extends CommonFields {
  no: string
  /**
   * 算法智能评价
   */
  score?: number

  originScore?: number

  /**
   * 已知反应占比
   */
  known_reaction_rate?: number
  backbone: string[]
  content_count: number
  main_trees: MainTree[]
  full_trees: MainTree[]
  group_conditions: Record<RetroBackboneGroup, number>
  group_info: Record<RetroBackboneGroup, string>
  retro_process: RetroProcess
  comment_id: string
  collection_id?: number
  collected_retro_backbones?: CollectedRetroBackbone[]
  updated_at?: Date
  min_n_main_tree_steps?: number
  safety_score?: number
  novelty_score?: number
  price_score?: number
  process_feasibility_score?: number
}

export type RetroBackboneGroup = 'start_material' | 'cluster'

export interface MainTree {
  value: string
  children?: MainTree[]
}
