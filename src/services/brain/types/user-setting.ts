import { CommonFields } from '.'
import { RetroParamsConfig } from './retro-process'

/**
 * UserSetting
 */
export interface UserSetting extends CommonFields {
  setting_label?: UserSettingLabel
  setting_value?: UserQuotationSetting | UserRetroSetting
}

type UserSettingLabel = 'quotation' | 'retro_params'

export interface UserQuotationSetting {
  ratio: number
  yields: number
  FTE_rate: number
  labor_logic: LaborLogic
}

export interface UserRetroSetting extends RetroParamsConfig {
  max_search_time: number
  route_show_yields: boolean
  route_detail_show_policy: RetroShowPolicy
  auto_analyze_route_project_ids: number[]
  has_procedure_only: boolean
  material_lib: number[]
  display_feasibility_layout?: boolean
}

type LaborLogic = 'by_leyan_reaction_difficulty' | 'by_procedure'
type RetroShowPolicy = 'only_key_reactions' | 'all_route'
