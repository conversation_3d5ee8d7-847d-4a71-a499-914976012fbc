import { CommonFields, UserInfo } from '.'
import { UploadFile } from './upload-file'

export type BatchRetroStatus = 'running' | 'finished' | 'canceled'

/**
 * BatchRetro
 */
export interface BatchRetro extends CommonFields {
  total_count: number
  completed_route_count?: number
  partial_route_count?: number
  no_route_count?: number

  status?: BatchRetroStatus
  create_user: UserInfo
  start_time?: Date
  finished_time?: Date

  origin_file?: UploadFile
  result_file?: UploadFile
}
