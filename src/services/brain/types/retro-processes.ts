import type { CommonFields } from './index'
export type SearchStatus =
  | 'completed'
  | 'running'
  | 'failed'
  | 'pending'
  | 'canceled'
  | 'limited'

export interface SearchLog {
  event_msg: string
  event_time: number
}
export interface SearchHsitory extends CommonFields {
  /**
   * 路线弹窗内信息
   */
  params: any
  id: number
  /**
   * 搜索人
   */
  retro_id: string
  status: SearchStatus
  response: null
  creator_id: string
  retro_backbones: any[] // TO ts type
  /* 小红点提示（新数据） */
  newMsgDot?: boolean
  count?: number
  offset?: number
  search_log: SearchLog[]

  /**
   * 搜索开始时间
   */
  search_start_time: Date

  /**
   * 搜索结束时间
   */
  search_end_time: Date

  /**
   * 预计搜索开始时间
   */
  predict_start_time?: Date

  /**
   *排队任务数
   */
  queue_count: number
}

export interface RetroProcesses {
  /**
   * 卡片ID
   */
  project_compound?: string | number

  /**
   * 弹窗内容
   */
  params: any
  // retro_id?: string
  // status?: 'running' | 'completed' | 'failed' | 'canceled'
  creator_id: string
}
