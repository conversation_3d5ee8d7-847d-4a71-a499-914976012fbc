import { MaterialTable } from './procedure-create'

export interface ProcedureRxnMatchParams {
  /**
   * 单步反应smiles
   */
  simple_rxn: string
  /**
   * 生成反应smiles
   */
  rxns: string[]
  /**
   * 相似反应的返回最好的K条
   */
  similar_topk: number
}

export interface ProcedureRxnMatchResult {
  id: number
  material_table?: null | MaterialTable[]
  created_by: null | string
  last_update_time: null | string
  query: null | string
  /**
   * 收率 number
   */
  yields: null | number
  /**
   * 专利颁发机构
   */
  assignees: null | string
  /**
   * 作者
   */
  authors: null | string
  /**
   * 发表时间
   */
  date: null | string
  /**
   * 原始Procedure
   */
  experimental_procedure: null | string
  /**
   * 专利号
   */
  patent_id: null | string
  /**
   * formated procedure
   */
  procedure: null | string
  /**
   * 排名
   */
  rank: number
  /**
   * 参考类型
   */
  reference_type: ProcedureRxnMatchReferenceType
  /**
   * 反应表达式
   */
  rxn: string
  /**
   * 收率 string
   */
  rxn_yields: null | string
  /**
   * 相识度
   */
  similarity: number
  /**
   * 标题
   */
  title: null | string
  /**
   * 反应分类
   */
  transformation: null | string
  /**
   * 引用标识
   */
  reference_text?: string | null
  /**
   * 是否可放大
   */
  is_scalable?: boolean
}

export interface ProcedureRxnBoneMatchResult {
  matched_reference: Record<string, ProcedureRxnMatchResult[]>
  reason: string
  ts: number
}

export interface ProcedureRxnMatchResponse {
  same_procedures: ProcedureRxnMatchResult[]
  similar_procedures: ProcedureRxnMatchResult[]
}

/**
 * 参考类型
 */
export type ProcedureRxnMatchReferenceType = 'Patent' | 'JOURNAL'
