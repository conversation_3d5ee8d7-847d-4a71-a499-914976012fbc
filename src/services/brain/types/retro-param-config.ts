import { CommonFields } from '.'

/**
 * RetroParamConfig
 */
export interface RetroParamConfig extends CommonFields {
  field: string
  label: string
  order: number
  enums?: any[]
  default_value?: any
  professional: boolean
  required: boolean
  type: RetroParamType
  config?: {
    preconfirm?: boolean
    confirm_label?: string
    default_confirm?: boolean
    not_confirm_value?: number
    not_update_when_change_mode?: boolean
  }
}

export type RetroParamType =
  | 'int'
  | 'float'
  | 'enum'
  | 'materials'
  | 'rxns'
  | 'money'
  | 'boolean'
  | 'select'
  | 'material_libs'
