import { BatchR<PERSON><PERSON>, <PERSON>Fields } from '.'

export interface JobNotification extends CommonFields {
  access_url?: string
  status: JobNotificationStatus
  readed?: boolean
  job_id?: string
  job_type?: string
  job_param?: any
  queue_count?: number
  predict_start_time?: Date
  queue_time?: string
  name?: string
}

export type JobNotificationStatus =
  | 'limited'
  | 'pending'
  | 'running'
  | 'success'
  | 'failed'
  | 'canceled'

export interface BatchRetroNotification {
  type: 'batch-task'
  status: 'success' | 'failed'
  data: BatchRetro
  message?: string
}
export interface TempMaterialUpdateNotification {
  type: 'temp-material-update'
  status: 'success' | 'failed'
  data: {
    published_temp_materials_file_id: number
    result: 'success' | 'failed'
    success_count: number
    failed_count: number
    failed_file_id?: number
    message?: string
  }
  message?: string
}
