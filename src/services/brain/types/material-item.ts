import { CommonFields } from '.'
import { MaterialLib } from './material-lib'
import { MaterialTag } from './material-tag'
export interface MaterialSearchParams {
  material_tags?: MaterialTag[]
  material_lib?: MaterialLib
  /**
   * 结构式
   */
  inchified_smiles: string
  purity?: string
  materialLibID?: string
}

export interface MaterialItem extends CommonFields, MaterialSearchParams {
  pubchem_safety_link: string
  codes?: string[]
  /**
   *CAS
   */
  canonical_smiles?: string
  cas_no?: string
  extension?: any
  in_stock?: boolean
  inchified_smiles: string
  min_unit_price: number
  material_id: string
  material_lib_id?: string
  max_delivery_days?: number
  min_delivery_days?: number
  unit_range?: number[]
  name_en?: string
  name_zh?: string
  price?: number
  quantity: number
  source?: string
  source_link?: string
  unit: 'mg' | 'ml' | 'g' | 'l' | 'kg'
  unified_unit: 'g' | 'ml'
  unit_price?: number
}

export interface MaterialItemSearchResponse {
  missed: string[]
  found: MaterialItem[]
}
