export interface ProcedureMatchParams {
  /**
   * 单步反应smiles
   */
  query: string
  /**
   * 返回最好的K条
   */
  topk: number

  fuzzy: boolean
  radius: number
  rc: boolean
  recommend_condition: boolean
  rxnfp: boolean
}

export interface ProcedureMatchResponse {
  /**
   * 专利颁发机构
   */
  assignees: null | string
  /**
   * 作者
   */
  authors: null | string
  /**
   * 发表时间
   */
  date: null | string
  /**
   * 原始Procedure
   */
  experimental_procedure: null | string
  /**
   * 专利号
   */
  patent_id: null | string
  /**
   * formated procedure
   */
  procedure: null | string
  /**
   * 排名
   */
  rank: number
  /**
   * 参考类型
   */
  reference_type: ReferenceType
  /**
   * 反应表达式
   */
  rxn: string
  /**
   * 收率
   */
  rxn_yields: null | string
  /**
   * 相识度
   */
  similarity: number
  /**
   * 标题
   */
  title: null | string
  /**
   * 反应分类
   */
  transformation: null | string
}

/**
 * 参考类型
 */
export type ReferenceType = 'Patent' | 'JOURNAL'
