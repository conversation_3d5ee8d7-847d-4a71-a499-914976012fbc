import { CommonFields } from './index'
export interface CommentConf extends CommonFields {
  collection_class?: ICollectionClass
  content_point?: number
  content_template?: string
}

/**
 * 评论对象的类型
 * retro-backbone 路线主干
 * route-main-tree 路线主树
 * project-route 路线
 * reaction 反应
 */
export type ICollectionClass =
  | 'retro-backbone'
  | 'route-main-tree'
  | 'project-route'
  | 'reaction'
  | 'retro-reaction'

export interface ProfileInfo {
  _commendSuject?: any // TODO set ts interface
  collection_class?: ICollectionClass

  isPlayground?: boolean

  /**
   * 要评论对应的 id
   */
  collection_id?: number

  /**
   *反应编号
   */
  reaction_step_no?: string
  isRobotChat?: boolean

  robot_id?: number
  robot_name?: string
}
export interface CommentStart extends ProfileInfo, CommonFields {
  /**
   * 要评论对象的描述
   */
  collection_subject: string
}

export interface Comment extends CommentStart, CommonFields {
  content_count: number
}

/**
 * CommentDetail
 */
export interface CommentDetail extends CommonFields {
  /**
   * 用commend/start返回的id
   */
  comment_id: string
  commentor: string
  /**
   * point, 如果点“顶” 1，否则是0
   */
  point?: number
  value: string
}

/**
 * -1为踩
 * 0为默认
 * 1为点赞
 */
export type CommendPoint = -1 | 0 | 1

export interface AddCommend {
  commentor: string
  point: CommendPoint
  value: string
  comment_uri: string
}
