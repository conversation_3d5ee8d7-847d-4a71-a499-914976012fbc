import { MainTreeForRoute } from '@/pages/route/util'
import { <PERSON>Fields, ProjectCompound, RetroBackbone } from './index'

/**
 * ProjectRoute
 */
export interface ProjectRoute extends CommonFields {
  name?: string
  main_tree: MainTreeForRoute
  status: ProjectRouteStatus
  retro_backbone?: RetroBackbone
  project_compound?: ProjectCompound
  content_count?: number | string
  expanding_material?: ProjectCompound
  updated_at?: string
}

export type ProjectRouteStatus =
  | 'editing'
  | 'confirmed'
  | 'finished'
  | 'canceled'
