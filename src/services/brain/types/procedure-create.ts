import { AiProcedure } from '@/types/Procedure'
import { ReactionRole } from './procedure-role'

export interface ProcedureCreateParams {
  procedure: Procedure
  searchable: boolean
}

export interface MaterialTable {
  smiles: string
  role: ReactionRole
  name?: string
  no: string
  equivalent: number
  unit?: MaterialUnit
  value?: number
  real_value?: string
}

export interface Procedure {
  id?: number
  yields?: number
  smiles: string
  procedure: string
  material_table: MaterialTable[]
  reference_type: string
  reference_smiles?: string
  created_by: string
  last_update_time?: string
  origin: AiProcedure
}

export interface ProcedureForCreate extends Procedure {
  rxn_yields?: string
}

export type MaterialUnit = 'w/w' | 'eq'
