import { ROUTE_DETAIL } from '@/constants/urls'
import { createApi } from '@/services/request'
import { CreateRequest } from '@/types/ApiType'
import { MaterialLib } from '.'
import { ReactionRole } from './procedure-role'
export const apiApplyRoute: CreateRequest<any> = createApi({
  path: ROUTE_DETAIL,
  defaultMethod: 'PUT'
})

export type MoleculeStatus = 'confirmed' | 'canceled' | 'editing' | 'draft'

export interface SupplierDetail {
  canonical_smiles?: string
  cas_no?: string
  count?: number
  createdAt?: string
  extension?: string
  id: number
  in_stock?: boolean
  inchified_smiles?: string
  material_id?: string
  material_lib_id?: string
  max_delivery_days?: number
  min_delivery_days?: number
  name_en?: string
  name_zh?: string
  price?: number
  purity?: string
  quantity?: number
  source?: string
  source_link?: string
  unit?: string
  unit_price?: number
  unit_quantity?: number
  updatedAt?: string
  gPerMol?: number
  material_lib?: MaterialLib
}

export interface Material {
  no: string
  smi?: string
  cost?: number
  set_cost?: number | string
  cas_no?: string
  detail?: SupplierDetail[]
  name_zh?: string
  name_en?: string
  equivalent?: number
  required_quantity?: number
  step_no?: string
  step_id?: string
  role?: ReactionRole
  unit?: 'w/w' | 'eq'
  gPerMol?: number
  cost_modified?: boolean
}

export interface Procedure {
  id: number
  rxn: string
  date?: any
  rank: number
  query?: any
  title?: any
  yields: number
  authors?: any
  assignees?: any
  patent_id?: any
  procedure: string
  created_by: string
  rxn_yields: string
  similarity?: any
  reference_type: string
  transformation?: any
  last_update_time: string
  experimental_procedure?: any
}

export interface Step_info {
  rxn: string
  index: number
  step_id: number
  step_no: string
  route_id: number
  procedure: Procedure
  reaction_ids: number[]
}

export interface Cost_detail {
  labor: number
  step_id: string
  materials: Material[]
  step_info: Step_info
}

export interface ConfigDicts {
  code?: string
  name?: string
}

export interface Cost_summary {
  name: string
  RMB: number
  USD: number
}

export interface Children {
  id: string
  value: string
  parent: string
  children: any[]
}

export interface Main_tree {
  id: string
  value: string
  children: Children[]
}

export interface Quote_route {
  quote_id: number
  route_id: string
}

export interface IQuote {
  id: number
  status: 'draft' | 'editing' | 'confirmed'
  confirmer?: any
  ratio: number
  purity: number
  createdAt: string
  updatedAt: string
  confirm_time?: any
  project_route_id: string
  target_weight: number
  project_compound_id: string
  fTE_unit_price: number
  other_costs: any[]
  cost_detail: Cost_detail[]
  quote_request_no: string
  project_id: string
  material_cost: number
  delivery_time: number
  labor_cost: number
  cost_summary: Cost_summary[]
  quotation_summary: string
  main_tree: Main_tree
  quote_route: Quote_route[]
  route_index: number
  target_unit?: 'mg' | 'g'
}
