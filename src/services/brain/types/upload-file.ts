import { CommonFields } from '.'

export interface UploadFile extends CommonFields {
  name?: string
  hash?: string
  ext?: string
  mime?: string
  size?: number
  url: string
  previewUrl?: string
  provider?: string

  alternativeText?: string
  caption?: string
  folderPath?: string
  height?: number
  width?: number
}

export interface FileParseResult {
  success_count: number
  error_count: number
  error_file_id?: number
}

export interface BatchRetroTaskResponse {
  total_count: number
  estimated_completion_in_minutes: number
}

export interface StaticFile {
  batch_retro_template?: UploadFile
  temp_materials_template?: UploadFile
  current_temp_material_file?: UploadFile
  updating_temp_material_file?: UploadFile
}
