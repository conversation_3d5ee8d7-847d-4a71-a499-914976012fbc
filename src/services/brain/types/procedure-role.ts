export interface ProcedureRoleParams {
  /**
   * 反应smiles
   */
  reaction_smiles?: string
}

export interface ProcedureRolesParams {
  reaction_smiles: string[]
}

export interface ProcedureRoleResponse {
  role: Record<string, ReactionRole>
}

export interface ProcedureRolesResponse {
  role: Record<string, ReactionRole>[]
}

export type ReactionRole =
  | 'product'
  | 'main_reactant'
  | 'reactant'
  | 'other_reagent'
  | 'solvent'
  | 'catalyst'
