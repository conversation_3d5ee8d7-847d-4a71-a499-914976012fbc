import { CommonFields, Project, ProjectRoute, RetroProcess } from '.'
import { Compound } from './compound'
import { ProjectCompoundStatusAudit } from './project-compound-status-audit'

/**
 * ProjectCompound
 */
export interface UserInfo {
  username: string
}
export interface ProjectCompound extends CommonFields {
  no: string
  input_smiles: string
  type: ProjectCompoundType
  priority: Priority
  status: ProjectCompoundStatus
  director_id?: string
  searching?: boolean
  project?: Project
  compound?: Compound
  project_routes?: ProjectRoute[]
  retro_processes?: RetroProcess[]
  retro_backbones_number?: number
  project_routes_number?: number
  status_update_note?: string
  project_compound_status_audit?: ProjectCompoundStatusAudit
  default_route?: ProjectRoute
  user_info: UserInfo
}

export type Priority = 'P0' | 'P1' | 'P2'

export type ProjectCompoundStatus =
  | 'created'
  | 'designing'
  | 'synthesizing'
  | 'finished'
  | 'canceled'

export type ProjectCompoundType = 'target' | 'building_block' | 'temp_block'
