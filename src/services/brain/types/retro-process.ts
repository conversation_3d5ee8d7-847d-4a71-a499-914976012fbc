import { <PERSON>Fields, ProjectCompound, SearchStatus } from '.'
import { RetroBackbone } from './retro-backbone'
export interface RetroProcessSockets {
  retro_id: string
  status: SearchStatus
  offset: number
}
/**
 * RetroProcess
 */
export interface RetroProcess extends CommonFields, RetroProcessSockets {
  params?: any
  compound_id: number
  response?: any
  project_compound?: ProjectCompound
  retro_backbones?: RetroBackbone[]
  creator_id?: string
  count?: number
  search_log?: any
  predict_start_time?: string
  search_start_time?: string
  search_end_time?: string
  queue_count?: number
  backbone_tree?: any
}

export interface RetroParamsConfig {
  safety_score?: number
  novelty_score?: number
  price_score?: number
}
