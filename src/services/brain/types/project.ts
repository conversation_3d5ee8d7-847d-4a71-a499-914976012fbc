import type { Priority, ProjectCompound } from './index'
import { CommonFields, ProjectCompoundStatus } from './index'
import { ProjectMember } from './project-member'
import { ProjectStatusAudit } from './project-status-audit'
/**
 * Project
 */
export interface Project extends CommonFields {
  no: string
  name?: string
  delivery_date?: Date
  customer?: string
  type?: ProjectType
  status: ProjectStatus
  start_datetime?: Date
  end_datetime?: Date
  project_members?: ProjectMember[]
  project_compounds?: ProjectCompound[]
  personal_owner?: any
  PM?: string
  project_status_audits?: ProjectStatusAudit[]
}

export interface ProjectSearchParams {
  no?: string
  name?: string
  delivery_date?: [string, string]
  customer?: string
  type?: ProjectType[]
  status?: ProjectStatus[]
  start_datetime?: [string, string]
  end_datetime?: [string, string]
  leaders?: string[]
  managers?: string[]
}

export interface ProjectStatusUpdateParams extends Project {
  status?: ProjectStatus
  status_update_note?: string
}

export interface MoleculeStatusUpdateParams {
  /* FIXME exclued id、no to fix ts error */
  // extends Omit<ProjectStatusUpdateParams, 'status'>
  status?: ProjectCompoundStatus
  status_update_note?: string
  priority?: Priority
  director_id?: string
}

export type ProjectStatus =
  | 'created'
  | 'started'
  | 'finished'
  | 'holding'
  | 'cancelled'

export type ProjectType = 'fte' | 'ffs' | 'personal'
