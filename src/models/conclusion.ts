import { parseResponseResult } from '@/services'
import { apiExperimentConclusionDetail } from '@/services/experiment-conclution'
import { ExperimentConclusionDetailResponse } from '@/services/experiment-conclution/index.d'
import { getWord } from '@/utils'
import { message } from 'antd'
import { useState } from 'react'

export interface Context {
  conclusion?: ExperimentConclusionDetailResponse
  refetch?: () => Promise<void>
  loading?: boolean
}

export default () => {
  const [conclusion, setConclusion] =
    useState<ExperimentConclusionDetailResponse | null>()
  const [loading, setLoading] = useState<boolean>(false)

  const fetchConclusion = async (experimentNo?: string) => {
    if (!experimentNo) return
    setConclusion(null)
    setLoading(true)
    const res = await apiExperimentConclusionDetail({
      routeParams: experimentNo
    })
    setLoading(false)
    if (parseResponseResult(res).ok) {
      setConclusion(res.data)
    } else {
      message.error(getWord('noticeIcon.empty'))
      setConclusion(null)
    }
  }

  return {
    loading,
    conclusion,
    fetchConclusion
  }
}
