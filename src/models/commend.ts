import type { SubmitValue } from '@/components/Launcher/types'
import type {
  CommentConf,
  CommentStart,
  ICollectionClass,
  ProfileInfo
} from '@/services/brain'
import { AddCommend, query, service } from '@/services/brain'
import { DialogQuestion } from '@/types/models'
import { formatYTSTime, toInt } from '@/utils'
import { useState } from 'react'
import { useModel } from 'umi'
export default () => {
  /* TODO refactor [ ] 列表中，评论数量获取，列表接口，请求头里传 comment: 'true' 获取 list 中每个 item 对应的评论数量
  -  { headers: { comment: 'true' } } */
  // TODO [comment request info](https://c12ai.atlassian.net/browse/LAB-534)
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const [reload, setReload] = useState<boolean>(false)
  const showLauncher = () => setIsOpen(!isOpen)
  const [curChatId, setChatId] = useState<number>()
  const [commendSuject, setCommendSuject] = useState<any>()
  const [reactionStepNo, setReactionStepNo] = useState<string>()
  const [commonExpression, setCommonExpression] = useState<any>()
  const [robotCommonExpression, setRobotCommonExpression] =
    useState<DialogQuestion[]>()
  const [messageList, setMessageList] = useState<SubmitValue[]>([])
  const { initialState } = useModel('@@initialState')

  const detectInputValue = (value: string, newMessageList: SubmitValue[]) => {
    function insertWord(customWord: string) {
      let curTime = new Date()
      setTimeout(() => {
        let newWord: SubmitValue = {
          commentor: 'C12助手',
          type: 'text',
          value: customWord,
          point: 0,
          createdAt: formatYTSTime(curTime)
        }
        setMessageList([...newMessageList, newWord])
      }, 2000)
    }
    if (value === '为什么上周实际交付分子数量没有达到计划要求？') {
      insertWord('分子C05202-5的最后一步未推进')
    } else if (value === '分子C05202-5的最后一步为什么未推进？') {
      insertWord(
        `中控结果显示未出现产物峰。点击<a href="https://brain-demo.labwise.cn/experiment-execute/detail/JTIyRTIwMjMwODAxXzAxJTIy")} target="_blank">E20230801_01</a>查看实验详情.`
      )
    } else if (value === '是否有改进方案？') {
      insertWord(
        `在数据库中找到类似的成功反应，点击反应<a href="https://web-dev.labwise.cn/reaction/209")} target="_blank">reaction_fdsgfshd</a>查看详情.是否授权给分子负责人？`
      )
    } else if (value === '授权') {
      insertWord(`已更新到对方反应库，已发送通知邮件。`)
    }
  }

  const startReload = () => setReload(true)
  const cacheCurReactionStepNo = (no: string) => setReactionStepNo(no)
  const finishedReload = () => setReload(false)

  /**
   * 新增评论 POST /comment-details
   */
  const addCommend = async (values: AddCommend) => {
    let newParams = {
      ...values,
      /**
       * 用commend/start返回的id
       */
      comment_id: curChatId,
      comment_uri: window.location.href
    }
    await service('comment-details').create(newParams)
    startReload()
  }

  /**
   * 获取常用语 (https://brain-dev.labwise.cn/admin 可自定义常用语)
   */
  const getCommonExpression = async (collection_class: ICollectionClass) => {
    const { data } = await query<CommentConf>('comment-confs')
      .equalTo('collection_class', collection_class)
      .get()
    setCommonExpression(data)
  }

  const getRobotCommonExpression = async () => {
    const { data } = await query<DialogQuestion>('dialog-questions').get()
    setRobotCommonExpression(data)
  }

  // TODO 查询评论详情，返回评论对象的具体评论内容
  // TODO 下拉翻页，虚拟列表
  /**
   * 查询评论详情，返回评论对象的具体评论内容
   * GET api/comment-details?comment_id=1
   */
  const getCommendDetail = async (newChatId) => {
    if (!newChatId) return
    const { data: commendDetail } = await query('comment-details')
      .equalTo('comment_id', String(newChatId))
      .get()
    setMessageList(commendDetail)
  }

  /** 查询或创建要评论对象的概要信息 GET start
   * 如果返回的 content_count 大于 0，才需要 comment-details 获取评论详情
   */
  const getProfileInfo = async ({
    _commendSuject,
    collection_class,
    isPlayground,
    reaction_step_no,
    isRobotChat,
    robot_id,
    robot_name
  }: ProfileInfo) => {
    if (isRobotChat) {
      // '/robot-dialogs'
      const { data, error } = await service('robot-dialogs').create({
        robot_id,
        robot_name
      })
      console.log('---commend data---', data)
      setChatId(data?.id)
      if (error) return
      showLauncher()
    } else {
      let commonPath = `comment/start?collection_id=${
        isPlayground ? _commendSuject?.collection_id : _commendSuject?.id
      }&collection_class=${collection_class}`
      let queryPath =
        collection_class === 'retro-reaction' && reaction_step_no
          ? `${commonPath}&collection_subject=${reaction_step_no}`
          : commonPath
      setCommendSuject(_commendSuject)
      const { data: profileInfo, error } = await query<CommentStart>(
        queryPath // &collection_subject=daadafds
      ).get()
      if (error) return
      setChatId(profileInfo?.id)
      if (toInt(profileInfo?.content_count) > 0) {
        await getCommendDetail(profileInfo?.id)
        showLauncher()
      } else {
        setMessageList([])
        showLauncher()
      }
    }
  }

  const sendRobotMessage = async (msg) => {
    await service(`dialog-records`, {
      method: 'post'
    }).create({
      dialog_session: curChatId,
      sender_id: initialState?.userInfo?.id,
      content: msg?.value
    })
    let newWord: SubmitValue = {
      commentor: initialState?.userInfo?.username as string,
      type: 'text',
      value: msg?.value
    }
    setMessageList([...messageList, newWord])
  }

  const sendMessage = (message: SubmitValue, isDemo?: boolean) => {
    const newMessageList = [
      ...messageList,
      { ...message, comment_uri: window.location.href }
    ]
    setMessageList(newMessageList)
    if (isDemo) {
      detectInputValue(message?.value, newMessageList)
    } else {
      addCommend(
        {
          userId: initialState?.userInfo?.id,
          commentor: initialState?.userInfo?.username,
          point: message?.point,
          value: message?.value
        },
        newMessageList
      )
    }
  }

  return {
    addCommend,
    messageList,
    commendSuject,
    isOpen,
    reload,
    commonExpression,
    robotCommonExpression,
    startReload,
    showLauncher,
    getCommonExpression,
    getRobotCommonExpression,
    getCommendDetail,
    sendMessage,
    finishedReload,
    reactionStepNo,
    cacheCurReactionStepNo,
    sendRobotMessage,
    getProfileInfo
  }
}
