import { allSearchStatus, defatulFilterInfo } from '@/constants'
import { MaterialOption } from '@/pages/compound/components/SearchParam/MaterialLabSelect'
import {
  MaterialLib,
  ProjectCompound,
  RetroBackbone,
  RetroParamsConfig,
  RetroProcess,
  RetroProcessSockets,
  SearchHsitory,
  SearchStatus,
  query,
  queryWithDefaultOrder,
  service
} from '@/services/brain'
import type { CollectedRetroBackbone } from '@/services/brain/types/collected-retro-backbone'
import { CollectedRetroBackbones } from '@/services/brain/types/retro-backbones'
import { Route, RouteGenerationResult } from '@/services/brainStrapi'
import { IPagination, RouteType } from '@/types/common'
import type { MaterialCode } from '@/types/models'
import { isValidArray, toInt } from '@/utils'
import { calcScoreOfRetroRoute } from '@/utils/calcScoreOfRetroRoute'
import message from '@/utils/message'
import { useModel } from '@umijs/max'
import { cloneDeep, isEmpty, isNil } from 'lodash'
import { useState } from 'react'
import type { GeneratedRequestType, PlaygroundDataRequestType } from './types'

export default () => {
  const { initialState } = useModel('@@initialState')
  const [pagenate, setPagenate] = useState<IPagination>({
    page: 1,
    pageSize: 10
  })
  const [mainTreeStepsRange, setMainTreeStepsRange] = useState<number[]>()
  const [backboneRange, setBackboneRange] = useState<number[]>()
  const [routeType, setRouteType] = useState<RouteType>('aiGenerated')
  const [searchHistoryData, setSearchHistoryData] = useState<SearchHsitory[]>(
    []
  )
  const [curAiRouteNum, setCurAiRouteNum] = useState<number>()
  const [curHistoryInfo, setCurHistoryInfo] = useState<SearchHsitory>([])
  const [retroParamsConfig, setRetroParamsConfig] =
    useState<RetroParamsConfig>()
  const [loading, setLoading] = useState<boolean>(false)
  const [myRoutesLoading, setMyRoutesLoading] = useState<boolean>(false)
  const [routes, setRoutes] = useState<RouteGenerationResult[] | Route[]>([])
  const [myRoutesData, setMyRoutesData] = useState<
    RouteGenerationResult[] | Route[]
  >([])
  const [curFilterInfo, setCurFilterInfo] =
    useState<RouteGenerationResult | null>(defatulFilterInfo)
  /* 默认值为按算法智能评分降序排序 */
  const [targetMolecule, setTargetMolecule] = useState<ProjectCompound>()
  const [smiles, setSmiles] = useState<string>('')
  const [myRoutesTotal, setMyRoutesTotal] = useState<number>(0)
  const [totalCount, setTotalCount] = useState<number>(0)
  const [filteredSearchHistory, setFilteredSearchHistory] = useState<
    SearchHsitory[]
  >([])

  const cacheFilterInfo = (
    item: RouteGenerationResult & GeneratedRequestType
  ) => {
    setCurFilterInfo((pre) => ({ ...defatulFilterInfo, ...pre, ...item }))
  }

  const compareTheBounds = (
    bound: [number, number],
    tag: 'backboneLengthRange' | 'mainTreeSteps'
  ) => {
    let newRange = null
    if (!isEmpty(bound)) {
      if (!isEmpty(curFilterInfo?.[tag])) {
        let leftBound = Math.max(curFilterInfo[tag][0], bound[0])
        let rightBound = Math.min(curFilterInfo[tag][1], bound[1])
        if (leftBound > rightBound) {
          if (curFilterInfo[tag][0] > bound[1]) {
            newRange = [curFilterInfo[tag][0], curFilterInfo[tag][0]]
          } else if (bound[0] > curFilterInfo[tag][1]) {
            newRange = [curFilterInfo[tag][1], curFilterInfo[tag][1]]
          }
        } else {
          newRange = [leftBound, rightBound] as [number, number]
        }
      } else {
        newRange = bound
      }
    }
    if (newRange) {
      cacheFilterInfo({ [tag]: newRange })
    }
  }

  const updateRetroParamsConfig = (_retroParamsConfig: RetroParamsConfig) =>
    setRetroParamsConfig(_retroParamsConfig)
  const updateBacoboneLengthRange = (bound: [number, number]) => {
    compareTheBounds(bound, 'backboneLengthRange')
    setBackboneRange(bound)
  }

  const updateMainTreeStepsRange = (bound: [number, number]) => {
    compareTheBounds(bound, 'mainTreeSteps')
    setMainTreeStepsRange(bound)
  }

  const updateCurHistoryInfo = (info: SearchHsitory) => {
    setCurHistoryInfo(info)
    setCurAiRouteNum(info?.retro_backbones?.length)
  }

  const resetRoutesData = () => {
    setRoutes([])
  }

  const resetCompoundData = () => {
    resetRoutesData()
    setCurAiRouteNum(0)
    setCurHistoryInfo([])
  }

  const resetFilterInfo = (resetAll: boolean = false) => {
    if (resetAll) {
      cacheFilterInfo({
        ...defatulFilterInfo,
        backboneLengthRange: undefined,
        mainTreeSteps: undefined
      })
      return
    }
    cacheFilterInfo({
      ...defatulFilterInfo,
      backboneLengthRange: backboneRange,
      mainTreeSteps: mainTreeStepsRange
    })
  }

  const filterSearchHistory = (
    originData: SearchHsitory[],
    targetStatus: SearchStatus[]
  ) => {
    let _originData = cloneDeep(originData)
    let filterResult: SearchHsitory[] = _originData.filter((e) =>
      targetStatus.includes(e.status)
    )
    return filterResult
  }

  const removeNewMsgDot = (originData: SearchHsitory[], retroId: string) => {
    let _orginData = cloneDeep(originData)
    const tragetIndex = _orginData?.findIndex((e) => e.retro_id === retroId)
    if (tragetIndex !== -1) _orginData[tragetIndex].newMsgDot = false
    setFilteredSearchHistory(_orginData)
  }

  const [retroProcessUpdates, setRetroProcessUpdates] = useState<
    RetroProcessSockets[]
  >([])

  const cacheRetroProcessUpdates = (updates: RetroProcessSockets[]) => {
    setRetroProcessUpdates(updates)
  }

  const getSearchLogs = async (moleculeId?: string | null, retro_id) => {
    const { data } = await queryWithDefaultOrder<RetroProcess>(
      'retro-processes',
      undefined,
      ['search_log']
    )
      .filterDeep('project_compound.id', 'eq', moleculeId)
      .filterDeep('retro_id', 'eq', retro_id)
      .paginate(pagenate?.page, 1000)
      .get()
    if (!data) return []
    return data[0]?.search_log
  }

  const getSearchHistory = async (moleculeId?: string | null) => {
    const { data } = await queryWithDefaultOrder<RetroProcess>(
      'retro-processes',
      undefined,
      [
        'id',
        'params',
        'retro_backbones',
        'retro_id',
        'status',
        'creator_id',
        'createdAt',
        'updatedAt',
        'search_log',
        'predict_start_time',
        'search_start_time',
        'search_end_time',
        'queue_count'
      ]
    )
      .filterDeep('project_compound.id', 'eq', moleculeId as string)
      .populateWith('retro_backbones', ['id'])
      .paginate(pagenate?.page, 1000)
      .get()
    if (!data) return []
    setSearchHistoryData(data)
    let _filterResult = filterSearchHistory(data, allSearchStatus)
    setFilteredSearchHistory(_filterResult)
    /* NOTE 如果搜索记录只有一项，则默认选中该项 */
    if (isEmpty(curHistoryInfo) && _filterResult?.length > 0) {
      setLoading(true)
      updateCurHistoryInfo(_filterResult[0] as SearchHsitory)
      setLoading(false)
    }
  }

  const getRequestPath = () => {
    switch (routeType) {
      case 'myRoutes':
        return 'project-route'
      case 'aiGenerated':
        return 'retro-backbone'
      case 'reaction':
        return 'retro-reaction'
      default:
        return null
    }
  }

  const curIsCollected = (curRoute: any[]) => {
    let isCollected =
      curRoute?.collected &&
      !isEmpty(
        curRoute?.collected_retro_backbones as CollectedRetroBackbone[]
      ) &&
      curRoute?.collected_retro_backbones.findIndex(
        (e) => toInt(e?.user_id) === initialState?.userInfo?.id
      ) !== -1
    return isCollected
  }

  const collectedEvent = (
    curId: string,
    newCollectedRetroBackbones: CollectedRetroBackbones[]
  ) => {
    let _routes = cloneDeep(routes)
    let targetNum = _routes.findIndex((e) => e?.id === curId)
    if (targetNum !== -1) {
      _routes[targetNum] = {
        ..._routes[targetNum],
        collected: !curIsCollected(_routes[targetNum]),
        collected_retro_backbones: newCollectedRetroBackbones
      }
    }
    setRoutes(_routes)
  }

  const getPlaygroundData = async (params: PlaygroundDataRequestType) => {
    resetRoutesData()
    let requestPath = getRequestPath()
    if (isNil(requestPath)) return
    setLoading(true)
    /* TODO PRD better:show counts when page load */
    const { pagenate } = params
    const { data, error, meta } = await query<any>(
      `comment/search/${requestPath}`
    )
      .paginate(pagenate.page, pagenate.pageSize)
      .sortBy([{ field: 'updatedAt', order: 'desc' }])
      .get()
    setLoading(false)
    if (!error && data) {
      setRoutes(data)
      setTotalCount(meta?.pagination.total || 0)
    }
  }

  const getMyRoutesData = async (moleculeId: number) => {
    resetRoutesData()
    setMyRoutesLoading(true)
    const { data, error, meta } = await query(`project-routes?comment=true`)
      .filterDeep('project_compound.id', 'eq', moleculeId)
      .sortBy([
        { field: 'createdAt', order: 'desc' },
        { field: 'updatedAt', order: 'desc' }
      ])
      .paginate(pagenate?.page, pagenate?.pageSize)
      .get()
    setMyRoutesLoading(false)
    if (!error && data) {
      setMyRoutesData(data)
      setMyRoutesTotal(meta?.pagination.total || 0)
    }
  }

  const getAIListData = async () => {
    if (!curHistoryInfo?.id) return
    resetRoutesData()
    const aiResultRequest = query<RetroBackbone>(
      'retro-backbones?comment=true',
      {
        params: {
          ...(curFilterInfo?.group && curFilterInfo?.group !== 'ungrouped'
            ? { group: curFilterInfo?.group }
            : {}), // 不分组则不传
          preference: retroParamsConfig // pass user selected value
        }
      },
      [
        'no',
        'score',
        'backbone',
        'main_trees',
        'group_conditions',
        'group_info',
        'known_reaction_rate',
        'min_n_main_tree_steps',
        'createdAt',
        'updatedAt',
        'safety_score',
        'novelty_score',
        'price_score',
        'collected_retro_backbones'
      ]
    )
      .filterDeep('retro_process.id', 'eq', curHistoryInfo?.id as number) // 查询某条搜索记录 3为搜索记录卡片对应ID
      .populateWith('collected_retro_backbones', ['id', 'user_id']) // 获取铺平数据 xxx 的id
    if (!isNil(curFilterInfo?.similarId)) {
      aiResultRequest.filterDeep(
        curFilterInfo?.group === 'start_material'
          ? 'group_conditions.start_material'
          : 'group_conditions.cluster',
        'eq',
        curFilterInfo?.similarId /* 不分组则隐藏相似路线筛选 */
      )
    }
    if (curFilterInfo?.collected) {
      aiResultRequest.equalTo('collected_by', initialState?.userInfo?.id) // user id
    }
    if (curFilterInfo?.sortFiled || curFilterInfo?.sortOrder) {
      aiResultRequest.sortBy([
        {
          field: curFilterInfo?.sortFiled,
          order: curFilterInfo?.sortOrder
        }
      ])
    }
    /* if (curFilterInfo?.backboneLengthRange) {
      aiResultRequest.between(
        'backbone_length',
        curFilterInfo?.backboneLengthRange
      ) // e.g. [1,100]
    } */
    /*  if (curFilterInfo?.mainTreeSteps) {
      aiResultRequest.between(
        'min_n_main_tree_steps',
        curFilterInfo?.mainTreeSteps
      ) // e.g. [1,100]
    } */
    if (curFilterInfo?.knownReactionRateRange) {
      aiResultRequest.between(
        'known_reaction_rate',
        curFilterInfo?.knownReactionRateRange
      ) // e.g. [1,100]
    }
    if (curFilterInfo?.scoreRange) {
      aiResultRequest.between('score', curFilterInfo?.scoreRange) // e.g. [1,100]
    }
    setLoading(true)
    const { data, error, meta } = await aiResultRequest
      .paginate(pagenate?.page, pagenate?.pageSize)
      .get()
    if (!error && data) {
      setCurAiRouteNum(meta?.pagination.total)
      if (isValidArray(data)) {
        data.forEach((e) => {
          e.collected = !isEmpty(e?.collected_retro_backbones)
          e.originScore = e.score
          e.score = calcScoreOfRetroRoute(e, retroParamsConfig || {}) * 100
        })
        setRoutes(data)
      }
      setTotalCount(meta?.pagination.total || 0)
      setLoading(false)
    } else {
      setLoading(false)
    }
  }

  const updatePagenate = (_pagenate: IPagination) => {
    setPagenate(_pagenate)
  }

  const updateRouteType = (key: RouteType) => {
    updatePagenate({ page: 1, pageSize: pagenate?.pageSize })
    setRouteType(key)
  }

  /**
   * 获取目标分子详情数据
   */
  const getTargetMolecule = async (id?: number | string) => {
    const idNum = typeof id !== 'number' ? Number.parseInt(id || '') : id
    if (isNaN(idNum)) return
    if (`${id}` !== `${targetMolecule?.id}`) setSmiles('')
    const { data: compoundData, error } = await service<ProjectCompound>(
      'project-compounds'
    )
      .selectManyByID([idNum])
      .populateWith('project_routes', ['id'])
      .populateWith('compound', ['id', 'smiles'])
      .populateDeep([
        {
          path: 'retro_processes',
          fields: ['id'],
          children: [{ key: 'retro_backbones', fields: ['id'] }]
        }
      ])
      .populateWith('default_route', ['id'])
      .get()
    if (error) {
      message.error({
        message: '数据获取失败，请稍后再试或联系管理员',
        description: error.message as string
      })
      setTargetMolecule(undefined)
    } else if (compoundData?.[0]) {
      const compound = compoundData[0]
      compound.project_routes_number = compound.project_routes?.length
      compound.retro_backbones_number = compound.retro_processes?.flatMap(
        (p) => p.retro_backbones
      ).length
      setTargetMolecule(compound)
      setSmiles(compound?.compound?.smiles || compound?.input_smiles || '')
    }
  }

  const getMaterialLibOptions = async (
    needVersion?: boolean
  ): Promise<MaterialOption[]> => {
    const { data } = await query<MaterialLib>('material-libs').get()
    if (data?.length) {
      const options = data.map((d) => ({
        value: d.id,
        label: needVersion
          ? d.name + (d.version ? ` (${d.version})` : '')
          : d.name,
        published: d.status === 'published'
      }))
      return options as MaterialOption[]
    }
    return []
  }

  const [materialCodeOptions, setMaterialCodeOptions] = useState()

  const getMaterialCodeOptions = async () => {
    const { data } = await query<MaterialCode>('material-code').get()
    if (!isEmpty(data?.dangerous_codes))
      return setMaterialCodeOptions(data?.dangerous_codes)
    return setMaterialCodeOptions([])
  }

  return {
    targetMolecule,
    materialCodeOptions,
    getMaterialCodeOptions,
    getMaterialLibOptions,
    smiles,
    loading,
    totalCount,
    getTargetMolecule,
    routes,
    myRoutesData,
    pagenate,
    updatePagenate,
    searchHistoryData,
    cacheFilterInfo,
    getSearchHistory,
    getSearchLogs,
    cacheRetroProcessUpdates,
    retroProcessUpdates,
    updateRouteType,
    routeType,
    curAiRouteNum,
    resetRoutesData,
    curIsCollected,
    updateCurHistoryInfo,
    filteredSearchHistory,
    curHistoryInfo,
    resetFilterInfo,
    curFilterInfo,
    getAIListData,
    getPlaygroundData,
    updateBacoboneLengthRange,
    getMyRoutesData,
    myRoutesLoading,
    collectedEvent,
    filterSearchHistory,
    resetCompoundData,
    updateMainTreeStepsRange,
    backboneRange,
    mainTreeStepsRange,
    updateRetroParamsConfig,
    retroParamsConfig,
    myRoutesTotal,
    removeNewMsgDot
  }
}
