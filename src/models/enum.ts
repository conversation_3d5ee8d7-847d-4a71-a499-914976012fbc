import { apiExperimentDesigns, parseResponseResult } from '@/services'
import { IOption } from '@/types/common'
import { changeKeyObjects } from '@/utils'
import { Effect, ImmerReducer } from 'umi'
export interface EnumModelState {
  experimentDesignList: IOption[]
}
export interface EnumModelsType {
  namespace: string
  state: EnumModelState
  effects: {
    queryExperimentDesignList: Effect
  }
  reducers: {
    changeState: ImmerReducer
    updateExperimentDesignList: ImmerReducer<IOption[]>
  }
}

const EnumModel: EnumModelsType = {
  state: {
    experimentDesignList: []
  },
  effects: {
    *queryExperimentDesignList(_, { call, put }) {
      const res = yield call(apiExperimentDesigns, {
        params: { page_no: 1, page_size: -1 } // 传 -1 获取实验流程名称的所有数据
      })
      if (parseResponseResult(res).ok) {
        const responseData = res?.data // TODO test isMockTime? mockExperimentDesignData:
        let options: IOption[] = changeKeyObjects(responseData?.data, {
          experiment_design_no: 'value',
          name: 'label'
        })
        yield put({ type: 'updateExperimentDesignList', payload: options })
      }
    }
  },
  reducers: {
    changeState(state: EnumModelState, { payload }) {
      return { ...state, ...payload }
    },
    updateExperimentDesignList(
      state: EnumModelState,
      { payload }: { payload: IOption[] }
    ) {
      experimentDesignList = payload
    }
  }
}
export default EnumModel
