import { CAMUNDA_PREFIX } from '@/components/BpmnEditor/bpmn/constant/bpmn'
import { ImmerReducer } from 'umi' // '@umijs/max' // '@@/plugin-dva'

export interface BpmnState {
  prefix: string // 流程引擎前缀
  processId: string | undefined
  processName: string | undefined
}

export interface GlobalModelsType {
  state: BpmnState
  reducers: {
    handleProcessId: ImmerReducer<any>
    handlePrefix: ImmerReducer<any>
    handleProcessName: ImmerReducer<any>
    changeState: ImmerReducer<any>
  }
}

const GlobalModel: GlobalModelsType = {
  state: {
    prefix: CAMUNDA_PREFIX,
    processId: undefined,
    processName: undefined
  },
  reducers: {
    changeState(state: GlobalModelState, { payload }) {
      return { ...state, ...payload }
    },
    handlePrefix: (state, { payload }) => {
      state.prefix = payload
    },
    handleProcessId: (state, { payload }) => {
      state.processId = payload || undefined
    },
    handleProcessName: (state, { payload }) => {
      state.processName = payload || undefined
    }
  }
}
export default GlobalModel
