/**
 * interfaces for AI Retron Synthetic Route generation
 */

export interface RetronSyntheticRoute {
  target: string
  rxn?: string
  children?: RetronSyntheticRoute[]
  score?: number
  step?: number
}

export interface RetronSyntheticRouteResponse {
  data: RetronSyntheticRoute[] | null
  status: SearchStatus
  target: string
}

export interface SearchStatus {
  /**
   * 当前搜索到的深度
   */
  current_step: number
  /**
   * 具体信息，比如出错时的堆栈信息
   */
  detail_message: null | string
  /**
   * 目标搜索深度
   */
  max_step: number
  /**
   * 已发现的路径数量
   */
  n_solve_path: number
  /**
   * 搜索是否完成
   */
  status: Status
}

type Status = 'completed' | 'running' | 'failed'
