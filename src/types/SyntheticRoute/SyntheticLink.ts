import {
  divideTreeChildrenByIfHasChildren,
  SyntheticTree
} from './SyntheticTree'

export interface SyntheticLink {
  value: string
  child?: SyntheticLink
  rxn?: string
  path?: string[]
}

const calTreeDeepth = ({ children = [] }: SyntheticTree): number => {
  if (!children.length) return 1
  return (
    children
      .map((child) => calTreeDeepth(child))
      .reduce((acc, cur) => Math.max(acc, cur)) + 1
  )
}

const getMainRoute = (
  children: SyntheticTree[],
  mainMaterial?: string
): [SyntheticTree | null, SyntheticTree[]] => {
  const [intermediates, materials] = divideTreeChildrenByIfHasChildren(children)
  if (intermediates.length === 0) {
    if (materials.length === 0) return [null, []]

    const longest = materials.reduce((acc, cur) =>
      acc.value.length > cur.value.length ? acc : cur
    )
    const mainMaterialTree = materials.find((c) => c.value === mainMaterial)
    const main = mainMaterialTree || longest
    const others = materials.filter((child) => child !== main)
    return [main, others]
  }

  const intermediateWithLongestPath = intermediates.reduce((acc, cur) =>
    calTreeDeepth(acc) >= calTreeDeepth(cur) ? acc : cur
  )
  const others = intermediates.filter(
    ({ value }) => value !== intermediateWithLongestPath.value
  )
  return [intermediateWithLongestPath, [...others, ...materials]]
}

export const syntheticTreeToLink = (
  { value, mainMaterial, children = [] }: SyntheticTree,
  path: string[] = []
): SyntheticLink => {
  const [mainRoute] = getMainRoute(children, mainMaterial)
  const currentPath = [...path, value]
  return {
    value,
    child: mainRoute ? syntheticTreeToLink(mainRoute, currentPath) : undefined,
    rxn: `${children.map((c) => c.value).join('.')}>${value}`,
    path: currentPath
  }
}

export const backboneToLink = (backbone: string[]): SyntheticLink => {
  return backbone.reduceRight<SyntheticLink>(
    (acc, cur, index) => {
      const currentPath = backbone.slice(0, index + 1)
      if (acc.value) {
        return { value: cur, child: acc, path: currentPath }
      } else {
        return { value: cur, path: currentPath }
      }
    },
    { value: '', path: [] }
  )
}
