import { MainTreeForRoute } from '@/pages/route/util'
import { groupBy } from 'lodash'
import { RetronSyntheticRoute } from './RetronSynthetic'

export interface SyntheticTree
  extends Omit<MainTreeForRoute, 'id' | 'children'> {
  children?: SyntheticTree[]
  parent?: string
}

export const routeTreeToSyntheticTree = (
  { value, children }: MainTreeForRoute,
  parent?: string
): SyntheticTree => {
  if (!children?.length) return { value, parent }
  return {
    value,
    children: children.map((c) => routeTreeToSyntheticTree(c, value)),
    parent
  }
}

export const retronToSyntheticTree = (
  { target, rxn, children }: RetronSyntheticRoute,
  parent?: string
): SyntheticTree & { rxn?: string } => {
  const allChildrenSmiles = rxn?.split('>>')[0]?.split('.')
  if (!allChildrenSmiles) return { value: target, parent }

  const childrenRoutesMapBySmiles = groupBy(children, ({ target }) => target)
  return {
    value: target,
    children: allChildrenSmiles.map((smiles) => {
      if (childrenRoutesMapBySmiles[smiles]?.length) {
        return retronToSyntheticTree(
          childrenRoutesMapBySmiles[smiles][0],
          target
        )
      }
      return { value: smiles, parent: target }
    }),
    parent,
    rxn
  }
}

export const divideTreeChildrenByIfHasChildren = (
  children: SyntheticTree[] = []
): [SyntheticTree[], SyntheticTree[]] =>
  children.reduce<[SyntheticTree[], SyntheticTree[]]>(
    (acc, cur) => {
      if (cur.children?.length) {
        acc[0].push(cur)
      } else {
        acc[1].push(cur)
      }
      return acc
    },
    [[], []]
  )

export const calSyntheticStep = (tree: SyntheticTree): number => {
  if (!tree.children?.length) return 0
  return tree.children.reduce((acc, cur) => acc + calSyntheticStep(cur), 0) + 1
}

export const calHasBranch = ({ children }: SyntheticTree): boolean => {
  if (!children?.length) return false
  const nOfChildrenWithChildren = children.reduce(
    (acc, cur) => acc + (cur.children?.length ? 1 : 0),
    0
  )
  if (nOfChildrenWithChildren >= 2) return true
  return children.reduce((acc, cur) => acc || calHasBranch(cur), false)
}
