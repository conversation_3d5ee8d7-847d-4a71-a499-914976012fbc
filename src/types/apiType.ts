export type Result<T = any> = {
  code: number
  message: string | null
  data: T
}

export type ApiCreatorParams = {
  path: string
  defaultPrefix?: string
  defaultApiPrefix?: string
  defaultMethod?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  routeParams?: string
  defaultIsOldApi?: boolean
}

export type RequestCreatorParams<ParamsShape = any, DataShape = any> = {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  params?: ParamsShape
  data?: DataShape
  url?: string
  routeParams?: string
}

export type CreateRequest<
  Params = any,
  Data = any,
  ResultShape extends Result = Result
> = {
  (arg: RequestCreatorParams<Params, Data>): Promise<ResultShape>
}
