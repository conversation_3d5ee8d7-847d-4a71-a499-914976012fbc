export type ITag = 'filter' | 'settings'

export const routeTypes = <const>['aiGenerated', 'myRoutes', 'reaction']
export type RouteType = (typeof routeTypes)[number]
export type LangType = 'zh-CN' | 'en-US'

export type RobotType = 'working' | 'holding' | 'error' | 'idle'
export type EnvType = 'partener' | 'demo' | 'develop' | 'product' | 'uat'
export interface LangData {
  value: string
  code: string
}
export interface ItemType {
  breadcrumbName: string
  linkPath: string
  title: string
}
export interface PageRequestType {
  page_no: number | null // 页码
  page_size: number | null // 每页最大条数
}

export interface IOption {
  label?: string
  value?: string | number
  disabled?: boolean
}

export type IStatus = 'activited' | 'running' | 'COMPLETED'

export interface IPagination {
  page: number
  pageSize: number
}

export interface IRoute {
  redirect?: string
  component?: string
  exact?: boolean
  name?: string
  path: string
}

export type IProcedureStatus = 'designing' | 'experimenting' | 'done' | 'cancel'

export type ExperimentExecuteStatus =
  | 'running'
  | 'hold'
  | 'canceled'
  | 'completed'
  | 'success'
  | 'incident'
  | 'failed'

export interface UserBasicInfo {
  id: number
  username: string
  email: string
}

export interface UserInfo extends UserBasicInfo {
  provider: string
  confirmed: boolean
  blocked: boolean
  createdAt: string
  updatedAt: string
}

export interface ILogin {
  jwt: string
  user: UserInfo
}
