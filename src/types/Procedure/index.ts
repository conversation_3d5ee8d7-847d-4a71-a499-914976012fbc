export interface IProcedureText {
  text: string
}
export interface AiProcedure extends IProcedureText {
  id: number
  rxn: string
  isSame?: boolean
  experimentalProcedure?: string
  transformation?: string
  similarity: number
  yieldString: string
  yieldNumber?: number
  query?: string
  reference: Reference
  scalable?: boolean
}

export interface Reference {
  type: 'journal' | 'patent'
  link?: string
  title?: string
  authors?: string
  date?: string
  no?: string
  assignees?: string
  reference_text?: string
}
