// tslint:disable
/**
 * labwise-run
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import { Ingredient } from './ingredient';

/**
 * Mix
 * @export
 * @interface Mix
 */
export interface Mix {
    /**
     * 任务名
     * @type {string}
     * @memberof Mix
     */
    task_name: string;
    /**
     * Operator
     * @type {string}
     * @memberof Mix
     */
    operator: string;
    /**
     * Internal Prev Workspace No
     * @type {string}
     * @memberof Mix
     */
    internal_prev_workspace_no: string;
    /**
     * Internal Next Workspace No
     * @type {string}
     * @memberof Mix
     */
    internal_next_workspace_no: string;
    /**
     * Internal Skip Operations
     * @type {string}
     * @memberof Mix
     */
    internal_skip_operations: string;
    /**
     * Internal Operation Schedules
     * @type {string}
     * @memberof Mix
     */
    internal_operation_schedules: string;
    /**
     * Internal Task No
     * @type {string}
     * @memberof Mix
     */
    internal_task_no: string;
    /**
     * Ingredients
     * @type {Array<Ingredient>}
     * @memberof Mix
     */
    ingredients?: Array<Ingredient>;
    /**
     * Temperature
     * @type {number}
     * @memberof Mix
     */
    temperature?: number;
}


