// tslint:disable
/**
 * labwise-run
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import { Batch } from './batch';
import { Dropwise } from './dropwise';
import { Normal } from './normal';
import { Substance } from './substance';

/**
 * Ingredient
 * @export
 * @interface Ingredient
 */
export interface Ingredient {
    /**
     * Ingredient Id
     * @type {string}
     * @memberof Ingredient
     */
    ingredient_id: string;
    /**
     * Ingredient Name
     * @type {string}
     * @memberof Ingredient
     */
    ingredient_name: string;
    /**
     * 
     * @type {Substance}
     * @memberof Ingredient
     */
    substance: Substance;
    /**
     * Scale At Add
     * @type {boolean}
     * @memberof Ingredient
     */
    scale_at_add?: boolean;
    /**
     * Add Method
     * @type {Dropwise | Batch | Normal}
     * @memberof Ingredient
     */
    add_method: Dropwise | Batch | Normal;
    /**
     * Error Range
     * @type {number}
     * @memberof Ingredient
     */
    error_range?: number;
    /**
     * Equivalent Weight
     * @type {number}
     * @memberof Ingredient
     */
    equivalent_weight?: number;
}


