/**
 * VideoListResponse
 */
export interface VideosResponse {
  data?: VideoListResponseDataItem[]
  meta?: Meta
  [property: string]: any
}

/**
 * VideoListResponseDataItem
 */
export interface VideoListResponseDataItem {
  attributes?: Video
  id?: number
  [property: string]: any
}

/**
 * Video
 */
export interface Video {
  camera?: PurpleCamera
  createdAt?: Date
  createdBy?: CreatedBy11
  name?: string
  publishedAt?: Date
  task_no?: string
  updatedAt?: Date
  updatedBy?: UpdatedBy11
  video_url: string
  [property: string]: any
}

export interface PurpleCamera {
  data?: PurpleData
  [property: string]: any
}

export interface PurpleData {
  attributes?: PurpleAttributes
  id?: number
  [property: string]: any
}

export interface PurpleAttributes {
  createdAt?: Date
  createdBy?: PurpleCreatedBy
  lab?: PurpleLab
  name?: string
  publishedAt?: Date
  robot?: Robot
  updatedAt?: Date
  updatedBy?: UpdatedBy9
  usage?: string
  video_live_url?: string
  videos?: Videos
  [property: string]: any
}

export interface PurpleCreatedBy {
  data?: FluffyData
  [property: string]: any
}

export interface FluffyData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface PurpleLab {
  data?: TentacledData
  [property: string]: any
}

export interface TentacledData {
  attributes?: FluffyAttributes
  id?: number
  [property: string]: any
}

export interface FluffyAttributes {
  cameras?: PurpleCameras
  createdAt?: Date
  createdBy?: FluffyCreatedBy
  lab_no?: string
  location?: string
  owner?: Owner
  publishedAt?: Date
  robot_agent_url?: string
  robots?: Robots
  site?: string
  updatedAt?: Date
  updatedBy?: UpdatedBy8
  [property: string]: any
}

export interface PurpleCameras {
  data?: PurpleDatum[]
  [property: string]: any
}

export interface PurpleDatum {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FluffyCreatedBy {
  data?: StickyData
  [property: string]: any
}

export interface StickyData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Owner {
  data?: OwnerData
  [property: string]: any
}

export interface OwnerData {
  attributes?: TentacledAttributes
  id?: number
  [property: string]: any
}

export interface TentacledAttributes {
  blocked?: boolean
  confirmationToken?: string
  confirmed?: boolean
  createdAt?: Date
  createdBy?: TentacledCreatedBy
  email?: string
  manager_id?: string
  personal_project?: PersonalProject
  provider?: string
  resetPasswordToken?: string
  role?: FluffyRole
  updatedAt?: Date
  updatedBy?: UpdatedBy3
  username?: string
  [property: string]: any
}

export interface TentacledCreatedBy {
  data?: IndigoData
  [property: string]: any
}

export interface IndigoData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface PersonalProject {
  data?: PersonalProjectData
  [property: string]: any
}

export interface PersonalProjectData {
  attributes?: StickyAttributes
  id?: number
  [property: string]: any
}

export interface StickyAttributes {
  createdAt?: Date
  createdBy?: StickyCreatedBy
  customer?: string
  delivery_date?: Date
  end_datetime?: Date
  name?: string
  no?: string
  personal_owner?: PersonalOwner
  project_compounds?: ProjectCompounds
  project_members?: ProjectMembers
  project_status_audits?: ProjectStatusAudits
  start_datetime?: Date
  status?: StickyStatus
  type?: FluffyType
  updatedAt?: Date
  updatedBy?: BraggadociousUpdatedBy
  [property: string]: any
}

export interface StickyCreatedBy {
  data?: IndecentData
  [property: string]: any
}

export interface IndecentData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface PersonalOwner {
  data?: PersonalOwnerData
  [property: string]: any
}

export interface PersonalOwnerData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface ProjectCompounds {
  data?: ProjectCompoundsDatum[]
  [property: string]: any
}

export interface ProjectCompoundsDatum {
  attributes?: IndigoAttributes
  id?: number
  [property: string]: any
}

export interface IndigoAttributes {
  compound?: Compound
  createdAt?: Date
  createdBy?: IndecentCreatedBy
  default_route?: DefaultRoute
  director_id?: string
  expanding_route?: ExpandingRoute
  input_smiles?: string
  no?: string
  priority?: Priority
  project?: PurpleProject
  project_compound_status_audits?: ProjectCompoundStatusAudits
  project_routes?: PurpleProjectRoutes
  retro_processes?: FluffyRetroProcesses
  searching?: boolean
  status?: TentacledStatus
  type?: PurpleType
  updatedAt?: Date
  updatedBy?: CunningUpdatedBy
  [property: string]: any
}

export interface Compound {
  data?: CompoundData
  [property: string]: any
}

export interface CompoundData {
  attributes?: IndecentAttributes
  id?: number
  [property: string]: any
}

export interface IndecentAttributes {
  createdAt?: Date
  createdBy?: IndigoCreatedBy
  smiles?: string
  updatedAt?: Date
  updatedBy?: PurpleUpdatedBy
  [property: string]: any
}

export interface IndigoCreatedBy {
  data?: HilariousData
  [property: string]: any
}

export interface HilariousData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface PurpleUpdatedBy {
  data?: AmbitiousData
  [property: string]: any
}

export interface AmbitiousData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface IndecentCreatedBy {
  data?: CunningData
  [property: string]: any
}

export interface CunningData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface DefaultRoute {
  data?: DefaultRouteData
  [property: string]: any
}

export interface DefaultRouteData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface ExpandingRoute {
  data?: ExpandingRouteData
  [property: string]: any
}

export interface ExpandingRouteData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export enum Priority {
  P0 = 'P0',
  P1 = 'P1',
  P2 = 'P2'
}

export interface PurpleProject {
  data?: MagentaData
  [property: string]: any
}

export interface MagentaData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface ProjectCompoundStatusAudits {
  data?: ProjectCompoundStatusAuditsDatum[]
  [property: string]: any
}

export interface ProjectCompoundStatusAuditsDatum {
  attributes?: HilariousAttributes
  id?: number
  [property: string]: any
}

export interface HilariousAttributes {
  createdAt?: Date
  createdBy?: HilariousCreatedBy
  from_status?: string
  note?: string
  project_compound?: PurpleProjectCompound
  to_status?: string
  updatedAt?: Date
  updatedBy?: FluffyUpdatedBy
  user_id?: string
  [property: string]: any
}

export interface HilariousCreatedBy {
  data?: FriskyData
  [property: string]: any
}

export interface FriskyData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface PurpleProjectCompound {
  data?: MischievousData
  [property: string]: any
}

export interface MischievousData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FluffyUpdatedBy {
  data?: BraggadociousData
  [property: string]: any
}

export interface BraggadociousData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface PurpleProjectRoutes {
  data?: FluffyDatum[]
  [property: string]: any
}

export interface FluffyDatum {
  attributes?: AmbitiousAttributes
  id?: number
  [property: string]: any
}

export interface AmbitiousAttributes {
  createdAt?: Date
  createdBy?: AmbitiousCreatedBy
  expanding_material?: ExpandingMaterial
  main_tree?: any
  name?: string
  project_compound?: FluffyProjectCompound
  project_reactions?: ProjectReactions
  retro_backbone?: PurpleRetroBackbone
  status?: FluffyStatus
  updatedAt?: Date
  updatedBy?: AmbitiousUpdatedBy
  [property: string]: any
}

export interface AmbitiousCreatedBy {
  data?: Data1
  [property: string]: any
}

export interface Data1 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface ExpandingMaterial {
  data?: ExpandingMaterialData
  [property: string]: any
}

export interface ExpandingMaterialData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FluffyProjectCompound {
  data?: Data2
  [property: string]: any
}

export interface Data2 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface ProjectReactions {
  data?: ProjectReactionsDatum[]
  [property: string]: any
}

export interface ProjectReactionsDatum {
  attributes?: CunningAttributes
  id?: number
  [property: string]: any
}

export interface CunningAttributes {
  createdAt?: Date
  createdBy?: CunningCreatedBy
  effective_procedures?: any
  history_procedures?: any
  project?: FluffyProject
  project_routes?: FluffyProjectRoutes
  reaction?: string
  updatedAt?: Date
  updatedBy?: TentacledUpdatedBy
  [property: string]: any
}

export interface CunningCreatedBy {
  data?: Data3
  [property: string]: any
}

export interface Data3 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FluffyProject {
  data?: Data4
  [property: string]: any
}

export interface Data4 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FluffyProjectRoutes {
  data?: TentacledDatum[]
  [property: string]: any
}

export interface TentacledDatum {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface TentacledUpdatedBy {
  data?: Data5
  [property: string]: any
}

export interface Data5 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface PurpleRetroBackbone {
  data?: Data6
  [property: string]: any
}

export interface Data6 {
  attributes?: MagentaAttributes
  id?: number
  [property: string]: any
}

export interface MagentaAttributes {
  backbone?: any
  collected_retro_backbones?: CollectedRetroBackbones
  createdAt?: Date
  createdBy?: FriskyCreatedBy
  full_trees?: any
  group_conditions?: any
  group_info?: any
  known_reaction_rate?: number
  main_trees?: any
  min_n_main_tree_steps?: number
  no?: string
  novelty_score?: number
  price_score?: number
  retro_process?: RetroProcess
  safety_score?: number
  score?: number
  updatedAt?: Date
  updatedBy?: HilariousUpdatedBy
  [property: string]: any
}

export interface CollectedRetroBackbones {
  data?: CollectedRetroBackbonesDatum[]
  [property: string]: any
}

export interface CollectedRetroBackbonesDatum {
  attributes?: FriskyAttributes
  id?: number
  [property: string]: any
}

export interface FriskyAttributes {
  createdAt?: Date
  createdBy?: MagentaCreatedBy
  retro_backbone?: FluffyRetroBackbone
  updatedAt?: Date
  updatedBy?: StickyUpdatedBy
  user_id?: string
  [property: string]: any
}

export interface MagentaCreatedBy {
  data?: Data7
  [property: string]: any
}

export interface Data7 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FluffyRetroBackbone {
  data?: Data8
  [property: string]: any
}

export interface Data8 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface StickyUpdatedBy {
  data?: Data9
  [property: string]: any
}

export interface Data9 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FriskyCreatedBy {
  data?: Data10
  [property: string]: any
}

export interface Data10 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface RetroProcess {
  data?: RetroProcessData
  [property: string]: any
}

export interface RetroProcessData {
  attributes?: MischievousAttributes
  id?: number
  [property: string]: any
}

export interface MischievousAttributes {
  createdAt?: Date
  createdBy?: MischievousCreatedBy
  creator_id?: string
  params?: any
  predict_start_time?: Date
  project_compound?: TentacledProjectCompound
  queue_count?: number
  response?: any
  retro_backbones?: RetroBackbones
  retro_id?: string
  retro_reactions?: RetroReactions
  search_end_time?: Date
  search_log?: any
  search_start_time?: Date
  status?: PurpleStatus
  updatedAt?: Date
  updatedBy?: IndecentUpdatedBy
  [property: string]: any
}

export interface MischievousCreatedBy {
  data?: Data11
  [property: string]: any
}

export interface Data11 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface TentacledProjectCompound {
  data?: Data12
  [property: string]: any
}

export interface Data12 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface RetroBackbones {
  data?: RetroBackbonesDatum[]
  [property: string]: any
}

export interface RetroBackbonesDatum {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface RetroReactions {
  data?: RetroReactionsDatum[]
  [property: string]: any
}

export interface RetroReactionsDatum {
  attributes?: BraggadociousAttributes
  id?: number
  [property: string]: any
}

export interface BraggadociousAttributes {
  createdAt?: Date
  createdBy?: BraggadociousCreatedBy
  hash?: string
  is_dangerous?: boolean
  is_known_reaction?: boolean
  is_selective_risk?: boolean
  product?: string
  reactants?: any
  reliability_score?: number
  retro_processes?: PurpleRetroProcesses
  updatedAt?: Date
  updatedBy?: IndigoUpdatedBy
  [property: string]: any
}

export interface BraggadociousCreatedBy {
  data?: Data13
  [property: string]: any
}

export interface Data13 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface PurpleRetroProcesses {
  data?: StickyDatum[]
  [property: string]: any
}

export interface StickyDatum {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface IndigoUpdatedBy {
  data?: Data14
  [property: string]: any
}

export interface Data14 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export enum PurpleStatus {
  Canceled = 'canceled',
  Completed = 'completed',
  Failed = 'failed',
  Limited = 'limited',
  Pending = 'pending',
  Running = 'running'
}

export interface IndecentUpdatedBy {
  data?: Data15
  [property: string]: any
}

export interface Data15 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface HilariousUpdatedBy {
  data?: Data16
  [property: string]: any
}

export interface Data16 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export enum FluffyStatus {
  Canceled = 'canceled',
  Confirmed = 'confirmed',
  Editing = 'editing',
  Finished = 'finished'
}

export interface AmbitiousUpdatedBy {
  data?: Data17
  [property: string]: any
}

export interface Data17 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FluffyRetroProcesses {
  data?: IndigoDatum[]
  [property: string]: any
}

export interface IndigoDatum {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export enum TentacledStatus {
  Canceled = 'canceled',
  Created = 'created',
  Designing = 'designing',
  Finished = 'finished',
  Synthesizing = 'synthesizing'
}

export enum PurpleType {
  BuildingBlock = 'building_block',
  Target = 'target',
  TempBlock = 'temp_block'
}

export interface CunningUpdatedBy {
  data?: Data18
  [property: string]: any
}

export interface Data18 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface ProjectMembers {
  data?: ProjectMembersDatum[]
  [property: string]: any
}

export interface ProjectMembersDatum {
  attributes?: Attributes1
  id?: number
  [property: string]: any
}

export interface Attributes1 {
  createdAt?: Date
  createdBy?: CreatedBy1
  project?: TentacledProject
  role?: PurpleRole
  updatedAt?: Date
  updatedBy?: FriskyUpdatedBy
  user_id?: string
  [property: string]: any
}

export interface CreatedBy1 {
  data?: Data19
  [property: string]: any
}

export interface Data19 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface TentacledProject {
  data?: Data20
  [property: string]: any
}

export interface Data20 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface PurpleRole {
  data?: Data21
  [property: string]: any
}

export interface Data21 {
  attributes?: Attributes2
  id?: number
  [property: string]: any
}

export interface Attributes2 {
  code?: string
  createdAt?: Date
  createdBy?: CreatedBy2
  locale?: string
  localizations?: Localizations
  name_zh?: string
  updatedAt?: Date
  updatedBy?: MagentaUpdatedBy
  [property: string]: any
}

export interface CreatedBy2 {
  data?: Data22
  [property: string]: any
}

export interface Data22 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Localizations {
  data?: any[]
  [property: string]: any
}

export interface MagentaUpdatedBy {
  data?: Data23
  [property: string]: any
}

export interface Data23 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FriskyUpdatedBy {
  data?: Data24
  [property: string]: any
}

export interface Data24 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface ProjectStatusAudits {
  data?: ProjectStatusAuditsDatum[]
  [property: string]: any
}

export interface ProjectStatusAuditsDatum {
  attributes?: Attributes3
  id?: number
  [property: string]: any
}

export interface Attributes3 {
  createdAt?: Date
  createdBy?: CreatedBy3
  from_status?: string
  note?: string
  project?: StickyProject
  to_status?: string
  updatedAt?: Date
  updatedBy?: MischievousUpdatedBy
  user_id?: string
  [property: string]: any
}

export interface CreatedBy3 {
  data?: Data25
  [property: string]: any
}

export interface Data25 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface StickyProject {
  data?: Data26
  [property: string]: any
}

export interface Data26 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface MischievousUpdatedBy {
  data?: Data27
  [property: string]: any
}

export interface Data27 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export enum StickyStatus {
  Cancelled = 'cancelled',
  Created = 'created',
  Finished = 'finished',
  Holding = 'holding',
  Started = 'started'
}

export enum FluffyType {
  Ffs = 'ffs',
  Fte = 'fte',
  Personal = 'personal'
}

export interface BraggadociousUpdatedBy {
  data?: Data28
  [property: string]: any
}

export interface Data28 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FluffyRole {
  data?: Data29
  [property: string]: any
}

export interface Data29 {
  attributes?: Attributes4
  id?: number
  [property: string]: any
}

export interface Attributes4 {
  createdAt?: Date
  createdBy?: CreatedBy4
  description?: string
  name?: string
  permissions?: PurplePermissions
  type?: string
  updatedAt?: Date
  updatedBy?: UpdatedBy2
  users?: PurpleUsers
  [property: string]: any
}

export interface CreatedBy4 {
  data?: Data30
  [property: string]: any
}

export interface Data30 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface PurplePermissions {
  data?: IndecentDatum[]
  [property: string]: any
}

export interface IndecentDatum {
  attributes?: Attributes5
  id?: number
  [property: string]: any
}

export interface Attributes5 {
  action?: string
  createdAt?: Date
  createdBy?: CreatedBy5
  role?: TentacledRole
  updatedAt?: Date
  updatedBy?: UpdatedBy1
  [property: string]: any
}

export interface CreatedBy5 {
  data?: Data31
  [property: string]: any
}

export interface Data31 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface TentacledRole {
  data?: Data32
  [property: string]: any
}

export interface Data32 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface UpdatedBy1 {
  data?: Data33
  [property: string]: any
}

export interface Data33 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface UpdatedBy2 {
  data?: Data34
  [property: string]: any
}

export interface Data34 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface PurpleUsers {
  data?: HilariousDatum[]
  [property: string]: any
}

export interface HilariousDatum {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface UpdatedBy3 {
  data?: Data35
  [property: string]: any
}

export interface Data35 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Robots {
  data?: RobotsDatum[]
  [property: string]: any
}

export interface RobotsDatum {
  attributes?: Attributes6
  id?: number
  [property: string]: any
}

export interface Attributes6 {
  cameras?: FluffyCameras
  createdAt?: Date
  createdBy?: CreatedBy6
  lab?: FluffyLab
  name?: string
  publishedAt?: Date
  robot_type?: RobotType
  status?: IndigoStatus
  updatedAt?: Date
  updatedBy?: UpdatedBy7
  [property: string]: any
}

export interface FluffyCameras {
  data?: AmbitiousDatum[]
  [property: string]: any
}

export interface AmbitiousDatum {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface CreatedBy6 {
  data?: Data36
  [property: string]: any
}

export interface Data36 {
  attributes?: Attributes7
  id?: number
  [property: string]: any
}

export interface Attributes7 {
  blocked?: boolean
  createdAt?: Date
  createdBy?: CreatedBy7
  email?: string
  firstname?: string
  isActive?: boolean
  lastname?: string
  preferedLanguage?: string
  registrationToken?: string
  resetPasswordToken?: string
  roles?: Roles
  updatedAt?: Date
  updatedBy?: UpdatedBy6
  username?: string
  [property: string]: any
}

export interface CreatedBy7 {
  data?: Data37
  [property: string]: any
}

export interface Data37 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Roles {
  data?: RolesDatum[]
  [property: string]: any
}

export interface RolesDatum {
  attributes?: Attributes8
  id?: number
  [property: string]: any
}

export interface Attributes8 {
  code?: string
  createdAt?: Date
  createdBy?: CreatedBy8
  description?: string
  name?: string
  permissions?: FluffyPermissions
  updatedAt?: Date
  updatedBy?: UpdatedBy5
  users?: FluffyUsers
  [property: string]: any
}

export interface CreatedBy8 {
  data?: Data38
  [property: string]: any
}

export interface Data38 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FluffyPermissions {
  data?: CunningDatum[]
  [property: string]: any
}

export interface CunningDatum {
  attributes?: Attributes9
  id?: number
  [property: string]: any
}

export interface Attributes9 {
  action?: string
  conditions?: any
  createdAt?: Date
  createdBy?: CreatedBy9
  properties?: any
  role?: StickyRole
  subject?: string
  updatedAt?: Date
  updatedBy?: UpdatedBy4
  [property: string]: any
}

export interface CreatedBy9 {
  data?: Data39
  [property: string]: any
}

export interface Data39 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface StickyRole {
  data?: Data40
  [property: string]: any
}

export interface Data40 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface UpdatedBy4 {
  data?: Data41
  [property: string]: any
}

export interface Data41 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface UpdatedBy5 {
  data?: Data42
  [property: string]: any
}

export interface Data42 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FluffyUsers {
  data?: MagentaDatum[]
  [property: string]: any
}

export interface MagentaDatum {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface UpdatedBy6 {
  data?: Data43
  [property: string]: any
}

export interface Data43 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FluffyLab {
  data?: Data44
  [property: string]: any
}

export interface Data44 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export enum RobotType {
  Aloha = 'aloha',
  Sd01 = 'sd-01'
}

export enum IndigoStatus {
  Error = 'error',
  Holding = 'holding',
  Idle = 'idle',
  Working = 'working'
}

export interface UpdatedBy7 {
  data?: Data45
  [property: string]: any
}

export interface Data45 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface UpdatedBy8 {
  data?: Data46
  [property: string]: any
}

export interface Data46 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Robot {
  data?: RobotData
  [property: string]: any
}

export interface RobotData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface UpdatedBy9 {
  data?: Data47
  [property: string]: any
}

export interface Data47 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Videos {
  data?: VideosDatum[]
  [property: string]: any
}

export interface VideosDatum {
  attributes?: Attributes10
  id?: number
  [property: string]: any
}

export interface Attributes10 {
  camera?: FluffyCamera
  createdAt?: Date
  createdBy?: CreatedBy10
  name?: string
  publishedAt?: Date
  task_no?: string
  updatedAt?: Date
  updatedBy?: UpdatedBy10
  video_url?: string
  [property: string]: any
}

export interface FluffyCamera {
  data?: Data48
  [property: string]: any
}

export interface Data48 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface CreatedBy10 {
  data?: Data49
  [property: string]: any
}

export interface Data49 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface UpdatedBy10 {
  data?: Data50
  [property: string]: any
}

export interface Data50 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface CreatedBy11 {
  data?: Data51
  [property: string]: any
}

export interface Data51 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface UpdatedBy11 {
  data?: Data52
  [property: string]: any
}

export interface Data52 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Meta {
  pagination?: Pagination
  [property: string]: any
}

export interface Pagination {
  page?: number
  pageCount?: number
  pageSize?: number
  total?: number
  [property: string]: any
}
