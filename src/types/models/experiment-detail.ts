/* tslint:disable */
/* eslint-disable */
/**
 * labwise run api
 * lab agent of devices
 *
 * OpenAPI spec version: 0.0.1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { ExperimentStatus } from './experiment-status';
/**
 * 
 * @export
 * @interface ExperimentDetail
 */
export interface ExperimentDetail {
    /**
     * 
     * @type {string}
     * @memberof ExperimentDetail
     */
    experiment_no: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDetail
     */
    project_no: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDetail
     */
    experiment_design_no: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDetail
     */
    design_name?: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDetail
     */
    experiment_name: string;
    /**
     * 
     * @type {ExperimentStatus}
     * @memberof ExperimentDetail
     */
    status: ExperimentStatus;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDetail
     */
    rxn_no: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDetail
     */
    rxn: string;
    /**
     * 
     * @type {Date}
     * @memberof ExperimentDetail
     */
    start_time: Date;
    /**
     * 
     * @type {Date}
     * @memberof ExperimentDetail
     */
    end_time: Date;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDetail
     */
    owner?: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDetail
     */
    experiment_type?: string;
    /**
     * 
     * @type {Date}
     * @memberof ExperimentDetail
     */
    predict_end_date?: Date;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDetail
     */
    progress?: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDetail
     */
    predict_yield?: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDetail
     */
    flow_data?: string;
}
