/* tslint:disable */
/* eslint-disable */
/**
 * labwise run api
 * lab agent of devices
 *
 * OpenAPI spec version: 0.0.1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { ExperimentDesignModel } from './experiment-design-model';
import { Material } from './material';
import { Status } from './status';
/**
 * 实验计划
 * @export
 * @interface ExperimentPlanWithDesign
 */
export interface ExperimentPlanWithDesign {
    /**
     * 
     * @type {number}
     * @memberof ExperimentPlanWithDesign
     */
    id?: number;
    /**
     * 
     * @type {string}
     * @memberof ExperimentPlanWithDesign
     */
    experiment_plan_no: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentPlanWithDesign
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentPlanWithDesign
     */
    rxn: string;
    /**
     * 
     * @type {Status}
     * @memberof ExperimentPlanWithDesign
     */
    status: Status;
    /**
     * 
     * @type {Array<Material>}
     * @memberof ExperimentPlanWithDesign
     */
    materials?: Array<Material>;
    /**
     * 
     * @type {Date}
     * @memberof ExperimentPlanWithDesign
     */
    earliest_start_time?: Date;
    /**
     * 
     * @type {Date}
     * @memberof ExperimentPlanWithDesign
     */
    latest_start_time?: Date;
    /**
     * 
     * @type {string}
     * @memberof ExperimentPlanWithDesign
     */
    cancel_reason?: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentPlanWithDesign
     */
    project_no?: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentPlanWithDesign
     */
    experiment_design_no?: string;
    /**
     * 
     * @type {Date}
     * @memberof ExperimentPlanWithDesign
     */
    created_date?: Date;
    /**
     * 
     * @type {ExperimentDesignModel}
     * @memberof ExperimentPlanWithDesign
     */
    experiment_design?: ExperimentDesignModel;
}
