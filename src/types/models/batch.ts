// tslint:disable
/**
 * labwise-run
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import { Solvent } from './solvent';

/**
 * Batch
 * @export
 * @interface Batch
 */
export interface Batch {
    /**
     * Method Name
     * @type {string}
     * @memberof Batch
     */
    method_name: string;
    /**
     * Solvents
     * @type {Array<Solvent>}
     * @memberof Batch
     */
    solvents: Array<Solvent>;
    /**
     * 加料温度，加料开始时，必须把温度调整到这个温度
     * @type {number}
     * @memberof Batch
     */
    temperature?: number;
    /**
     * 需要控制的温度，加料时，需要把温度控制在这个值以下
     * @type {number}
     * @memberof Batch
     */
    temperature_control?: number;
    /**
     * 固体加料时候分几批
     * @type {number}
     * @memberof Batch
     */
    batch_count?: number;
}


