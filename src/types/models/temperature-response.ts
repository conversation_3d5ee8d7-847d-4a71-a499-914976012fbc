/* tslint:disable */
/* eslint-disable */
/**
 * labwise run api
 * lab agent of devices
 *
 * OpenAPI spec version: 0.0.1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { TemperaturePoint } from './temperature-point';
/**
 * 
 * @export
 * @interface TemperatureResponse
 */
export interface TemperatureResponse {
    /**
     * 
     * @type {number}
     * @memberof TemperatureResponse
     */
    current_value?: number;
    /**
     * 
     * @type {number}
     * @memberof TemperatureResponse
     */
    target_value?: number;
    /**
     * 
     * @type {number}
     * @memberof TemperatureResponse
     */
    predict_time?: number;
    /**
     * 
     * @type {string}
     * @memberof TemperatureResponse
     */
    temperature_method?: string;
    /**
     * 
     * @type {Array<TemperaturePoint>}
     * @memberof TemperatureResponse
     */
    history?: Array<TemperaturePoint>;
}
