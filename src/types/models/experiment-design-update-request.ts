/* tslint:disable */
/* eslint-disable */
/**
 * labwise run api
 * lab agent of devices
 *
 * OpenAPI spec version: 0.0.1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { DesignStatus } from './design-status';
import { Material } from './material';
/**
 * 
 * @export
 * @interface ExperimentDesignUpdateRequest
 */
export interface ExperimentDesignUpdateRequest {
    /**
     * 
     * @type {number}
     * @memberof ExperimentDesignUpdateRequest
     */
    id: number;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDesignUpdateRequest
     */
    experiment_design_no?: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDesignUpdateRequest
     */
    name?: string;
    /**
     * 
     * @type {DesignStatus}
     * @memberof ExperimentDesignUpdateRequest
     */
    status?: DesignStatus;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDesignUpdateRequest
     */
    rxn?: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDesignUpdateRequest
     */
    reference_text?: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDesignUpdateRequest
     */
    formatted_text?: string;
    /**
     * 
     * @type {Array<Material>}
     * @memberof ExperimentDesignUpdateRequest
     */
    materials?: Array<Material>;
    /**
     * 
     * @type {string}
     * @memberof ExperimentDesignUpdateRequest
     */
    workflow?: string;
}
