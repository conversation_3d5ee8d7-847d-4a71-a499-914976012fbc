export interface OperationCheckRequest {
  /**
   * value can be TLC, LCMS
   */
  checkMethod?: CheckMethod
  /**
   * Checkno
   */
  checkNo?: string
  /**
   * 中控的状态
   */
  checkStatus?: CheckStatus
  /**
   * 检测类型，检测类型
   */
  checkType?: CheckType
  /**
   * Experimentno
   */
  experimentNo?: string
  /**
   * Operator
   */
  operator?: string
  [property: string]: any
}

/**
 * value can be TLC, LCMS
 *
 * CheckMethod，An enumeration.
 */
export enum CheckMethod {
  Lcms = 'LCMS',
  Nmr = 'NMR',
  Tlc = 'TLC'
}

/**
 * 中控的状态
 *
 * CheckStatus，An enumeration.
 */
export enum CheckStatus {
  Canceled = 'canceled',
  Checking = 'checking',
  Finished = 'finished',
  Todo = 'todo'
}

/**
 * 检测类型，检测类型
 *
 * CheckType，An enumeration.
 */
export enum CheckType {
  F = 'F',
  M = 'M'
}

/**
 * Response Search Experiment Checks Api Experiment Check Search Post
 *
 * OperationCheckVo
 */
export interface OperationCheckResponse {
  /**
   * value can be TLC, LCMS
   */
  checkMethod?: CheckMethod
  /**
   * Checkno
   */
  checkNo?: string
  /**
   * 中控的状态，running, completed
   */
  checkStatus?: CheckStatus
  /**
   * 检测类型，检测类型
   */
  checkType?: CheckType
  /**
   * 完成时间
   */
  endTime?: Date
  /**
   * Experimentno
   */
  experimentNo?: string
  /**
   * Operator
   */
  operator?: string
  /**
   * 发起时间
   */
  startTime?: Date
  [property: string]: any
}
