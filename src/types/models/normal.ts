// tslint:disable
/**
 * labwise-run
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import { Solvent } from './solvent';

/**
 * Normal
 * @export
 * @interface Normal
 */
export interface Normal {
    /**
     * Method Name
     * @type {string}
     * @memberof Normal
     */
    method_name: string;
    /**
     * Solvents
     * @type {Array<Solvent>}
     * @memberof Normal
     */
    solvents: Array<Solvent>;
    /**
     * 加料温度，加料开始时，必须把温度调整到这个温度
     * @type {number}
     * @memberof Normal
     */
    temperature?: number;
}


