/**
 * MessageReaderListResponse
 */
export interface MessageReadersResponse {
  data?: MessageReaderListResponseDataItem[]
  meta?: Meta
  [property: string]: any
}

/**
 * MessageReaderListResponseDataItem
 */
export interface MessageReaderListResponseDataItem {
  attributes?: MessageReader
  id?: number
  [property: string]: any
}

/**
 * MessageReader
 */
export interface MessageReader {
  createdAt?: Date
  createdBy?: PurpleCreatedBy
  publishedAt?: Date
  readed?: boolean
  system_message?: PurpleSystemMessage
  updatedAt?: Date
  updatedBy?: IndecentUpdatedBy
  user_id?: string
  [property: string]: any
}

export interface PurpleCreatedBy {
  data?: PurpleData
  [property: string]: any
}

export interface PurpleData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface PurpleSystemMessage {
  data?: FluffyData
  [property: string]: any
}

export interface FluffyData {
  attributes?: PurpleAttributes
  id?: number
  [property: string]: any
}

export interface PurpleAttributes {
  createdAt?: Date
  createdBy?: MessageReadersFluffyCreatedBy
  event_data?: any
  event_level?: EventLevel
  event_type?: string
  experiment_no?: string
  message?: string
  message_level?: MessageLevel
  project_no?: string
  publishedAt?: Date
  read_items?: ReadItems
  updatedAt?: Date
  updatedBy?: IndigoUpdatedBy
  [property: string]: any
}

export interface MessageReadersFluffyCreatedBy {
  data?: TentacledData
  [property: string]: any
}

export interface TentacledData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export enum EventLevel {
  Experiment = 'experiment',
  Global = 'global',
  Project = 'project'
}

export enum MessageLevel {
  I = 'I',
  Ii = 'II',
  Iii = 'III'
}

export interface ReadItems {
  data?: ReadItemsDatum[]
  [property: string]: any
}

export interface ReadItemsDatum {
  attributes?: MessageReadersFluffyAttributes
  id?: number
  [property: string]: any
}

export interface MessageReadersFluffyAttributes {
  createdAt?: Date
  createdBy?: TentacledCreatedBy
  publishedAt?: Date
  readed?: boolean
  system_message?: FluffySystemMessage
  updatedAt?: Date
  updatedBy?: StickyUpdatedBy
  user_id?: string
  [property: string]: any
}

export interface TentacledCreatedBy {
  data?: StickyData
  [property: string]: any
}

export interface StickyData {
  attributes?: TentacledAttributes
  id?: number
  [property: string]: any
}

export interface TentacledAttributes {
  blocked?: boolean
  createdAt?: Date
  createdBy?: StickyCreatedBy
  email?: string
  firstname?: string
  isActive?: boolean
  lastname?: string
  preferedLanguage?: string
  registrationToken?: string
  resetPasswordToken?: string
  roles?: Roles
  updatedAt?: Date
  updatedBy?: TentacledUpdatedBy
  username?: string
  [property: string]: any
}

export interface StickyCreatedBy {
  data?: IndigoData
  [property: string]: any
}

export interface IndigoData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Roles {
  data?: RolesDatum[]
  [property: string]: any
}

export interface RolesDatum {
  attributes?: StickyAttributes
  id?: number
  [property: string]: any
}

export interface StickyAttributes {
  code?: string
  createdAt?: Date
  createdBy?: IndigoCreatedBy
  description?: string
  name?: string
  permissions?: Permissions
  updatedAt?: Date
  updatedBy?: FluffyUpdatedBy
  users?: Users
  [property: string]: any
}

export interface IndigoCreatedBy {
  data?: IndecentData
  [property: string]: any
}

export interface IndecentData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Permissions {
  data?: PermissionsDatum[]
  [property: string]: any
}

export interface PermissionsDatum {
  attributes?: IndigoAttributes
  id?: number
  [property: string]: any
}

export interface IndigoAttributes {
  action?: string
  conditions?: any
  createdAt?: Date
  createdBy?: IndecentCreatedBy
  properties?: any
  role?: Role
  subject?: string
  updatedAt?: Date
  updatedBy?: PurpleUpdatedBy
  [property: string]: any
}

export interface IndecentCreatedBy {
  data?: HilariousData
  [property: string]: any
}

export interface HilariousData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Role {
  data?: RoleData
  [property: string]: any
}

export interface RoleData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface PurpleUpdatedBy {
  data?: AmbitiousData
  [property: string]: any
}

export interface AmbitiousData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FluffyUpdatedBy {
  data?: CunningData
  [property: string]: any
}

export interface CunningData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Users {
  data?: UsersDatum[]
  [property: string]: any
}

export interface UsersDatum {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface TentacledUpdatedBy {
  data?: MagentaData
  [property: string]: any
}

export interface MagentaData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FluffySystemMessage {
  data?: FriskyData
  [property: string]: any
}

export interface FriskyData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface StickyUpdatedBy {
  data?: MischievousData
  [property: string]: any
}

export interface MischievousData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface IndecentUpdatedBy {
  data?: MessageReadersData1
  [property: string]: any
}

export interface MessageReadersData1 {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Meta {
  pagination?: Pagination
  [property: string]: any
}

export interface Pagination {
  page?: number
  pageCount?: number
  pageSize?: number
  total?: number
  [property: string]: any
}
