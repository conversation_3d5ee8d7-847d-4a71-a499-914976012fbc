/**
 * Response Get Task By Operator Api Robot Tasks  Robot Name  Get
 *
 * RobotTask
 */
export interface RobotTasksResponse {
  /**
   * 任务的结束时间
   */
  endTime?: Date
  /**
   * 实验编号
   */
  experimentNo: string
  /**
   * 操作详情
   */
  operations?: RobotOperation[]
  /**
   * 操作者
   */
  operator: string
  /**
   * 任务的开始时间
   */
  startTime?: Date
  /**
   * 任务状态
   */
  status: Status | 'completed' | 'running' | 'working'
  /**
   * 任务名称
   */
  taskName: string
  /**
   * 任务编号
   */
  taskNo: string
  [property: string]: any

  project_no: string
  project_reaction_id: string
}

/**
 * RobotOperation
 */
export interface RobotOperation {
  /**
   * 操作描述
   */
  operation_description: string
  /**
   * 结束时间
   */
  operation_end_time?: Date
  /**
   * 操作名称
   */
  operation_name: string
  /**
   * 开始时间
   */
  operation_start_time?: Date
  [property: string]: any
}

/**
 * 任务状态
 *
 * TaskStatusEnum，An enumeration.
 */
export enum TaskStatus {
  Canceled = 'canceled',
  Completed = 'completed',
  Running = 'running',
  Todo = 'todo'
}
