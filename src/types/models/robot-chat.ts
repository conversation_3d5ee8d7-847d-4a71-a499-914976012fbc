/**
 * DialogQuestionListResponse
 */
export interface RobotQuestionResponse {
  data?: DialogQuestionListResponseDataItem[]
  meta?: Meta
  [property: string]: any
}

/**
 * DialogQuestionListResponseDataItem
 */
export interface DialogQuestionListResponseDataItem {
  attributes?: DialogQuestion
  id?: number
  [property: string]: any
}

export interface Localizations {
  data?: DialogQuestion[]
  [property: string]: any
}

/**
 * DialogQuestion
 */
export interface DialogQuestion {
  code: string
  createdAt?: Date
  createdBy?: DatumCreatedBy
  locale?: string
  localizations?: Localizations
  publishedAt?: Date
  question: string
  updatedAt?: Date
  updatedBy?: DatumUpdatedBy
  [property: string]: any
}

export interface DatumCreatedBy {
  data?: PurpleData
  [property: string]: any
}

export interface PurpleData {
  attributes?: DataAttributes
  id?: number
  [property: string]: any
}

export interface DataAttributes {
  blocked?: boolean
  createdAt?: Date
  createdBy?: PurpleCreatedBy
  email?: string
  firstname?: string
  isActive?: boolean
  lastname?: string
  preferedLanguage?: string
  registrationToken?: string
  resetPasswordToken?: string
  roles?: Roles
  updatedAt?: Date
  updatedBy?: TentacledUpdatedBy
  username?: string
  [property: string]: any
}

export interface PurpleCreatedBy {
  data?: FluffyData
  [property: string]: any
}

export interface FluffyData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Roles {
  data?: RolesDatum[]
  [property: string]: any
}

export interface RolesDatum {
  attributes?: PurpleAttributes
  id?: number
  [property: string]: any
}

export interface PurpleAttributes {
  code?: string
  createdAt?: Date
  createdBy?: FluffyCreatedBy
  description?: string
  name?: string
  permissions?: Permissions
  updatedAt?: Date
  updatedBy?: FluffyUpdatedBy
  users?: Users
  [property: string]: any
}

export interface FluffyCreatedBy {
  data?: TentacledData
  [property: string]: any
}

export interface TentacledData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Permissions {
  data?: PermissionsDatum[]
  [property: string]: any
}

export interface PermissionsDatum {
  attributes?: FluffyAttributes
  id?: number
  [property: string]: any
}

export interface FluffyAttributes {
  action?: string
  conditions?: any
  createdAt?: Date
  createdBy?: TentacledCreatedBy
  properties?: any
  role?: Role
  subject?: string
  updatedAt?: Date
  updatedBy?: PurpleUpdatedBy
  [property: string]: any
}

export interface TentacledCreatedBy {
  data?: StickyData
  [property: string]: any
}

export interface StickyData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Role {
  data?: RoleData
  [property: string]: any
}

export interface RoleData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface PurpleUpdatedBy {
  data?: IndigoData
  [property: string]: any
}

export interface IndigoData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FluffyUpdatedBy {
  data?: IndecentData
  [property: string]: any
}

export interface IndecentData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Users {
  data?: UsersDatum[]
  [property: string]: any
}

export interface UsersDatum {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface TentacledUpdatedBy {
  data?: HilariousData
  [property: string]: any
}

export interface HilariousData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface DatumUpdatedBy {
  data?: AmbitiousData
  [property: string]: any
}

export interface AmbitiousData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Meta {
  pagination?: Pagination
  [property: string]: any
}

export interface Pagination {
  page?: number
  pageCount?: number
  pageSize?: number
  total?: number
  [property: string]: any
}
