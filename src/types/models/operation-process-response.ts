// tslint:disable
/**
 * run-web
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * OperationProcessResponse
 * @export
 * @interface OperationProcessResponse
 */
export interface OperationProcessResponse {
    /**
     * Operation Exe Id
     * @type {number}
     * @memberof OperationProcessResponse
     */
    operation_exe_id: number;
    /**
     * Process Instance Id
     * @type {number}
     * @memberof OperationProcessResponse
     */
    process_instance_id: number;
    /**
     * Experiment Instance
     * @type {number}
     * @memberof OperationProcessResponse
     */
    experiment_instance: number;
    /**
     * Operation No
     * @type {string}
     * @memberof OperationProcessResponse
     */
    operation_no: string;
    /**
     * Task Name
     * @type {string}
     * @memberof OperationProcessResponse
     */
    task_name: string;
    /**
     * Task Id
     * @type {string}
     * @memberof OperationProcessResponse
     */
    task_id: string;
    /**
     * Operator
     * @type {string}
     * @memberof OperationProcessResponse
     */
    operator: string;
    /**
     * Start Time
     * @type {string}
     * @memberof OperationProcessResponse
     */
    start_time?: string;
    /**
     * End Time
     * @type {string}
     * @memberof OperationProcessResponse
     */
    end_time?: string;
    /**
     * Operation Status
     * @type {string}
     * @memberof OperationProcessResponse
     */
    operation_status: string;
    /**
     * Error Code
     * @type {string}
     * @memberof OperationProcessResponse
     */
    error_code?: string;
    /**
     * Error Cause
     * @type {string}
     * @memberof OperationProcessResponse
     */
    error_cause?: string;
    /**
     * Rpc Params
     * @type {object}
     * @memberof OperationProcessResponse
     */
    rpc_params: object;
    /**
     * Operation Uuid
     * @type {string}
     * @memberof OperationProcessResponse
     */
    operation_uuid: string;
}


