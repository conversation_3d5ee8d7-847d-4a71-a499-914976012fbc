export interface ReportReference {
  patent_id: string
  procedure: string
  reference_type: 'Patent' | 'JOURNAL' // 期刊
  reference_text: string
  rxn: string
}

export interface ReportResponse {
  /**
   * Check Desc
   */
  check_desc?: string
  /**
   * Check End Time
   */
  check_end_time?: Date
  /**
   * Check Method
   */
  check_method: string
  /**
   * Check No
   */
  check_no?: string
  /**
   * Check Params
   */
  check_params: { [key: string]: any }[]
  /**
   * Check Process Id
   */
  check_process_id?: number
  /**
   * Check Start Time
   */
  check_start_time?: Date
  /**
   * Check Status
   */
  check_status?: string
  /**
   * Check Type
   */
  check_type: string
  /**
   * Created Date
   */
  created_date?: Date
  /**
   * Experiment No
   */
  experiment_no: string
  /**
   * Is Reference
   */
  is_reference?: boolean
  /**
   * Modified Date
   */
  modified_date?: Date
  /**
   * Operation Exe Id
   */
  operation_exe_id: number
  /**
   * Operator
   */
  operator?: string
  /**
   * Report Analysis Result
   */
  report_analysis_result: { [key: string]: any }
  /**
   * Report Url
   */
  report_url?: string
  /**
   * Start From
   */
  start_from: string
  [property: string]: any
}
