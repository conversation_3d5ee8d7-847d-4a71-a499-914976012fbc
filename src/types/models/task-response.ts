/* tslint:disable */
/* eslint-disable */
/**
 * labwise run api
 * lab agent of devices
 *
 * OpenAPI spec version: 0.0.1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 
 * @export
 * @interface TaskResponse
 */
export interface TaskResponse {
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    task_no: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    experiment_name?: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    experiment_no: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    workspace_no: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    operator: string;
    /**
     * 
     * @type {boolean}
     * @memberof TaskResponse
     */
    active?: boolean;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    status: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    operation_name: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    operation_summary?: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    operation_detail?: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    stage: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    task_name: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    task_icon?: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    task_type?: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    plan_start_time?: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    plan_end_time?: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    start_time?: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    operation_icon?: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    end_time?: string;
    /**
     * 
     * @type {number}
     * @memberof TaskResponse
     */
    predict_time?: number;
    /**
     * 
     * @type {any}
     * @memberof TaskResponse
     */
    extension?: any;
    /**
     * 
     * @type {number}
     * @memberof TaskResponse
     */
    error_code?: number;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    error_message?: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    procedure_no?: string;
    /**
     * 
     * @type {string}
     * @memberof TaskResponse
     */
    component_type?: string;
}
