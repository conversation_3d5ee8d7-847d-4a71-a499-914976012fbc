/* tslint:disable */
/* eslint-disable */
/**
 * labwise run api
 * lab agent of devices
 *
 * OpenAPI spec version: 0.0.1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { AddMethod } from './add-method';
import { Role } from './role';
import { Unit } from './unit';
/**
 * 当量
 * @export
 * @interface Material
 */
export interface Material {
    /**
     * 
     * @type {number}
     * @memberof Material
     */
    equivalent: number;
    /**
     * 
     * @type {string}
     * @memberof Material
     */
    smiles: string;
    /**
     * 
     * @type {AddMethod}
     * @memberof Material
     */
    add_method?: AddMethod;
    /**
     * 
     * @type {number}
     * @memberof Material
     */
    molecular_weight?: number;
    /**
     * 
     * @type {string}
     * @memberof Material
     */
    name?: string;
    /**
     * 
     * @type {boolean}
     * @memberof Material
     */
    ready?: boolean;
    /**
     * 
     * @type {Role}
     * @memberof Material
     */
    role?: Role;
    /**
     * 
     * @type {Unit}
     * @memberof Material
     */
    unit?: Unit;
    /**
     * 
     * @type {number}
     * @memberof Material
     */
    value?: number;
    /**
     * 
     * @type {number}
     * @memberof Material
     */
    volume_ratio?: number;
}
