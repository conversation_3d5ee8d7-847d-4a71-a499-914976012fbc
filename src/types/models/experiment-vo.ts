/* tslint:disable */
/* eslint-disable */
/**
 * labwise run api
 * lab agent of devices
 *
 * OpenAPI spec version: 0.0.1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { ExperimentStatus } from './experiment-status';
/**
 * 
 * @export
 * @interface ExperimentVo
 */
export interface ExperimentVo {
    /**
     * 
     * @type {string}
     * @memberof ExperimentVo
     */
    experiment_no: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentVo
     */
    project_no: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentVo
     */
    experiment_design_no: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentVo
     */
    design_name?: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentVo
     */
    experiment_name: string;
    /**
     * 
     * @type {ExperimentStatus}
     * @memberof ExperimentVo
     */
    status: ExperimentStatus;
    /**
     * 
     * @type {string}
     * @memberof ExperimentVo
     */
    rxn_no: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentVo
     */
    rxn: string;
    /**
     * 
     * @type {Date}
     * @memberof ExperimentVo
     */
    start_time: Date;
    /**
     * 
     * @type {Date}
     * @memberof ExperimentVo
     */
    end_time: Date;
    /**
     * 
     * @type {string}
     * @memberof ExperimentVo
     */
    owner?: string;
    /**
     * 
     * @type {string}
     * @memberof ExperimentVo
     */
    experiment_type?: string;
}
