/**
 * UserSettingListResponseDataItem
 */
export interface UserSettingListResponseDataItem {
  attributes?: UserSetting
  id?: number
  [property: string]: any
}

/**
 * UserSetting
 */
export interface Setting_Value {
  FTE_rate: number
  labor_logic: 'by_procedure' | 'by_leyan_reaction_difficulty'
  ratio: number
  yields: number
  material_lib: number[]
}

export type SettingLabel = 'quotation' | 'retro_params' | 'procedure'
export interface UserSetting {
  id?: number
  setting_label: SettingLabel
  setting_value: Setting_Value
}

export interface PurpleCreatedBy {
  data?: PurpleData
  [property: string]: any
}

export interface PurpleData {
  attributes?: DataAttributes
  id?: number
  [property: string]: any
}

export interface DataAttributes {
  blocked?: boolean
  createdAt?: Date
  createdBy?: FluffyCreatedBy
  email?: string
  firstname?: string
  isActive?: boolean
  lastname?: string
  preferedLanguage?: string
  registrationToken?: string
  resetPasswordToken?: string
  roles?: Roles
  updatedAt?: Date
  updatedBy?: TentacledUpdatedBy
  username?: string
  [property: string]: any
}

export interface FluffyCreatedBy {
  data?: FluffyData
  [property: string]: any
}

export interface FluffyData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Roles {
  data?: RolesDatum[]
  [property: string]: any
}

export interface RolesDatum {
  attributes?: PurpleAttributes
  id?: number
  [property: string]: any
}

export interface PurpleAttributes {
  code?: string
  createdAt?: Date
  createdBy?: TentacledCreatedBy
  description?: string
  name?: string
  permissions?: Permissions
  updatedAt?: Date
  updatedBy?: FluffyUpdatedBy
  users?: Users
  [property: string]: any
}

export interface TentacledCreatedBy {
  data?: TentacledData
  [property: string]: any
}

export interface TentacledData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Permissions {
  data?: PermissionsDatum[]
  [property: string]: any
}

export interface PermissionsDatum {
  attributes?: FluffyAttributes
  id?: number
  [property: string]: any
}

export interface FluffyAttributes {
  action?: string
  conditions?: any
  createdAt?: Date
  createdBy?: StickyCreatedBy
  properties?: any
  role?: Role
  subject?: string
  updatedAt?: Date
  updatedBy?: PurpleUpdatedBy
  [property: string]: any
}

export interface StickyCreatedBy {
  data?: StickyData
  [property: string]: any
}

export interface StickyData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Role {
  data?: RoleData
  [property: string]: any
}

export interface RoleData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface PurpleUpdatedBy {
  data?: IndigoData
  [property: string]: any
}

export interface IndigoData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface FluffyUpdatedBy {
  data?: IndecentData
  [property: string]: any
}

export interface IndecentData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface Users {
  data?: UsersDatum[]
  [property: string]: any
}

export interface UsersDatum {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface TentacledUpdatedBy {
  data?: HilariousData
  [property: string]: any
}

export interface HilariousData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}

export interface StickyUpdatedBy {
  data?: AmbitiousData
  [property: string]: any
}

export interface AmbitiousData {
  attributes?: { [key: string]: any }
  id?: number
  [property: string]: any
}
