/* tslint:disable */
/* eslint-disable */
/**
 * labwise run api
 * lab agent of devices
 *
 * OpenAPI spec version: 0.0.1
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { DesignStatus } from './design-status'
import { ExperimentInDesign } from './experiment-in-design'
import { Material } from './material'
/**
 *
 * @export
 * @interface ExperimentDesignResponseData
 */
export interface ExperimentDesignResponseData {
  /**
   *
   * @type {number}
   * @memberof ExperimentDesignResponseData
   */
  id?: number
  project_reaction_id?: number
  /**
   *
   * @type {string}
   * @memberof ExperimentDesignResponseData
   */
  experiment_design_no: string
  project_no: string
  /**
   *
   * @type {string}
   * @memberof ExperimentDesignResponseData
   */
  name: string
  /**
   *
   * @type {DesignStatus}
   * @memberof ExperimentDesignResponseData
   */
  status: DesignStatus
  /**
   *
   * @type {string}
   * @memberof ExperimentDesignResponseData
   */
  rxn?: string
  /**
   *
   * @type {string}
   * @memberof ExperimentDesignResponseData
   */
  creator?: string
  /**
   *
   * @type {string}
   * @memberof ExperimentDesignResponseData
   */
  reference_text?: string
  /**
   *
   * @type {string}
   * @memberof ExperimentDesignResponseData
   */
  formatted_text?: string
  /**
   *
   * @type {Array<Material>}
   * @memberof ExperimentDesignResponseData
   */
  materials?: Array<Material>
  /**
   *
   * @type {string}
   * @memberof ExperimentDesignResponseData
   */
  workflow?: string
  /**
   *
   * @type {Array<ExperimentInDesign>}
   * @memberof ExperimentDesignResponseData
   */
  experiments?: Array<ExperimentInDesign>
}
