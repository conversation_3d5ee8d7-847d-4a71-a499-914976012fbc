/* tslint:disable */
/* eslint-disable */
/**
 * labwise run api
 * lab agent of devices
 *
 * OpenAPI spec version: 0.0.1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { SimulateConfig } from './simulate-config';
/**
 * 
 * @export
 * @interface OperationPutRequest
 */
export interface OperationPutRequest {
    /**
     * 
     * @type {number}
     * @memberof OperationPutRequest
     */
    operation_exe_id: number;
    /**
     * 单位为秒
     * @type {number}
     * @memberof OperationPutRequest
     */
    duration?: number;
    /**
     * 单位摄氏度，记录到温度表
     * @type {number}
     * @memberof OperationPutRequest
     */
    current_temperature?: number;
    /**
     * 页面上输入设备上设置的温度值
     * @type {number}
     * @memberof OperationPutRequest
     */
    temperature_value?: number;
    /**
     * 
     * @type {SimulateConfig}
     * @memberof OperationPutRequest
     */
    simulate_config?: SimulateConfig;
}
