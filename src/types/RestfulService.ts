/**
 * rest服务响应结果
 */
export interface RestResponseHeader {
  /**
   * 000000-成功，50X-失败
   */
  code: string | null

  /**
   * 响应信息
   */
  message: string | null

  /**
   * 响应扩展信息
   */
  // rext: Map<string, string> | null
}

// 接口响应结果
export interface ResponseType {
  code: number | null
  message: string | null
}

/* 登录响应参数 */
export interface LoginRequestType extends ResponseType {
  data: string
}

export interface AuthMenuItem {
  id: number
  attributes: {
    createdAt: string
    updatedAt: string
    title: string
    slug: string
  }
}

export interface AuthMenu {
  data: AuthMenuItem
  meta: unknown
}

export interface Role {
  id: number
  name: string
  description: string
  type: string // 'pm'
  createdAt: string
  updatedAt: string
}

export interface IUpdateInfo {
  id: number
  username: string
  email: string
  provider: string
  createdAt: string
  updatedAt: string
  manager_id: string
  role: Role
  menu: AuthMenu
}

export interface ILoginLocal {
  identifier: string
  password: string
}
