import { history } from 'umi'
import { isValidArray } from './detect'
export function encodeString(paramString: string) {
  return btoa(encodeURIComponent(paramString))
}

export function toExperimentDetail(params: string) {
  history.push(
    `/experiment/experiment-execute/detail/${encodeString(
      JSON.stringify(params)
    )}`
  )
}

export function toRobotDetail(robotId: string) {
  return window.open(`/lab-manage/robot-detail/${robotId}`)
}

export function decodeUrl(paramString: string) {
  return decodeURIComponent(atob(paramString))
}

/* 修改数组指定key */
export const changeKeyObjects = (arr, replaceKeys) => {
  let newKeys = arr
  if (isValidArray(newKeys)) {
    newKeys.map((item, index) => {
      arr[index] = Object.keys(item).reduce(
        (acc, key) => ({
          ...acc,
          ...{ [replaceKeys[key] || key]: item[key] }
        }),
        {}
      )
    })
  }
  return newKeys
}

// Form.Item基本样式配置 ->可以模块化到common文件中
let date = new Date() // date.getFullYear() + '-' + month + '-' + Day + '-' + Hour + '-' + Min
let month = date.getMonth() + 1
// let Day = date.getDate()
// let Hour = date.getHours()
// let Min = date.getMinutes()
const CurrentMonth = date.getFullYear() + '-' + month
const YearStart = date.getFullYear() + '-1'
const YearEnd = date.getFullYear() + '-12'
const SixDayBefore = new Date(date.getTime() - 24 * 60 * 60 * 6000) // date.getFullYear() + '-' + month + '-' + LogDay + '-' + Hour + '-' + Min
const CurrentDetailTime = date

export {
  CurrentDetailTime,
  CurrentMonth,
  SixDayBefore,
  YearEnd,
  YearStart,
  date
}

// 针对整数金额，每3位用‘,’分隔
export function segmentation(parameter) {
  let num = ''
  if (Number(parameter).toString().length > 3) {
    num = Number(parameter)
      .toString()
      .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
    return num
  } else {
    return parameter
  }
}

/* PDF/图片预览 */
export function winOpen(url: string) {
  return window.open(
    url,
    '_blank',
    'scrollbars=yes,resizable=yes,menubar=no,modal=no,titlebar=no,alawaysRaised=no,location=no'
  )
}

// 字符串去空格操作
export function deleteSpace(str: string) {
  return str.replace(/\s/g, '')
}

export function reloadEvent() {
  window.location.reload()
}

export const toInt = <T>(
  s?: string,
  defaultValue?: T
): number | T | undefined => {
  const num = Number.parseInt(s || '')
  if (Number.isNaN(num)) {
    return defaultValue
  }
  return num
}

export const base64Encode = (str: string) => {
  // Convert the Unicode string to a UTF-8 byte sequence
  const utf8Bytes = encodeURIComponent(str).replace(
    /%([0-9A-F]{2})/g,
    (_, p1) => String.fromCharCode(parseInt(p1, 16))
  )
  return btoa(utf8Bytes)
}
