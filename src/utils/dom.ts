/**
 * 获取第一个表格的可视化高度
 * @param {number} extraHeight 额外的高度(表格底部的内容高度 Number类型,默认为74)
 * @param {reactRef} ref Table所在的组件的ref
 */
export function getTableScroll({ extraHeight, ref } = {}) {
  if (typeof extraHeight == 'undefined') extraHeight = 110 //  默认底部分页64 + 边距10 + Footer 36
  let tHeader = null
  if (ref && ref.current)
    tHeader = ref.current.getElementsByClassName('ant-table-thead')[0]
  else tHeader = document.getElementsByClassName('ant-table-thead')[0]

  let tHeaderBottom = 0 //表格内容距离顶部的距离
  if (tHeader) tHeaderBottom = tHeader.getBoundingClientRect().bottom

  // 窗体高度-表格内容顶部的高度-表格内容底部的高度
  // let height = `calc(100vh - ${tHeaderBottom + extraHeight}px)`
  let height = document?.body?.clientHeight - tHeaderBottom - extraHeight
  // 空数据时表格高度保持不变,暂无数据提示文本图片居中
  if (ref && ref.current) {
    let placeholder = ref.current.getElementsByClassName(
      'ant-table-placeholder'
    )[0]
    if (placeholder) {
      placeholder.style.height = height
    }
  }
  return height
}

// 表格容器布局配置  ->可以模块化到common文件中
export const defaultContainerCol = {
  lg: { span: 24 },
  md: { span: 24 }
}

// 表格子列布局 ->可以模块化到common文件中
export const defaultItemCol = {
  lg: { span: 4 },
  md: { span: 12 },
  sm: { span: 24 }
}

// Form.Item基本样式配置 ->可以模块化到common文件中
export const defaultFormItemConfig = {
  colon: false, // 是否显示冒号
  labelCol: {
    // label标签布局
    sm: { span: 4 },
    md: { span: 8 }
  },
  wrapperCol: {
    // 输入控件布局
    sm: { span: 4 },
    md: { span: 14 }
  }
}

/* 图片预加载：将图片的加载写成一个`Promise`，一旦加载完成，`Promise`的状态就发生变化 */
export function preloadImage(url: string) {
  return new Promise((resolve, reject) => {
    const image = new Image()
    image.onload = resolve
    image.onerror = reject
    image.src = url
  })
}

export function preloadImages(images) {
  for (let i = 0; i < images.length; i++) {
    images[i] = images[i]
  }
  return images
}

export function scrollToTop(behaviorType: string) {
  window.scrollTo({ top: 0, behavior: behaviorType || 'smooth' })
}
