import { initRDKit } from '@/components/MoleculeStructure/util'
import { MaterialItem, SupplierDetail, query } from '@/services/brain'
import { inchifySmiles } from './smiles'

export const calcGPerMol = async (smiles: string): Promise<number> => {
  const rdkit = await initRDKit()
  const mol = rdkit.get_mol(smiles)
  return JSON.parse(mol?.get_descriptors() || 'null')?.amw || 0
}

export const fetchSuppliers = async (
  smiles: string
): Promise<SupplierDetail[]> => {
  const [inchifiedSmiles] = await inchifySmiles([smiles])
  const gPerMol = await calcGPerMol(smiles)
  const { data, error } = await query<MaterialItem>('material-items')
    .paginate(1, 1000)
    .populateWith('material_lib')
    .equalTo('inchified_smiles', inchifiedSmiles.replace(/\+/g, '%2B'))
    .get()
  if (!data?.length || error) return []
  return data.map((d) => ({
    ...d,
    gPerMol,
    createdAt: d.createdAt as unknown as string,
    updatedAt: d.createdAt as unknown as string
  }))
}
