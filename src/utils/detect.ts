import type {
  MoleculeStatus,
  ProjectCompoundStatus,
  ProjectStatus
} from '@/services/brain'
import { cloneDeep, isArray, isEmpty } from 'lodash'
export function isReadonlyMolecule(
  status?: ProjectStatus,
  compoundStatus?: ProjectCompoundStatus
): boolean {
  const readOnlyProject =
    !!status && ['canceled', 'cancelled', 'finished'].includes(status)
  const readOnlyCompound =
    !!compoundStatus && ['canceled', 'finished'].includes(compoundStatus)
  return readOnlyProject || readOnlyCompound
}

export function isConfirmedMolecule(status?: MoleculeStatus) {
  return status === 'confirmed'
}

export function setSessionValue(keyName: string, value: string) {
  sessionStorage.setItem(keyName, value)
}

export function getSessionValue(keyName: string) {
  let newValue = sessionStorage.getItem(keyName)
  return newValue
}

export function delSessionValue(keyName: string) {
  sessionStorage.removeItem(keyName)
}

export function setLocalValue(keyName: string, value: string) {
  localStorage.setItem(keyName, value)
}

export function getLocalValue(keyName: string) {
  let newValue = localStorage.getItem(keyName)
  return newValue
}

export function detectDeviceInfo() {
  let winHeight, winWidth
  if (document?.documentElement?.clientHeight)
    winHeight = document.documentElement.clientHeight
  if (document?.documentElement?.clientWidth)
    winWidth = document.documentElement.clientWidth
  return { winWidth: Number(winWidth), winHeight: Number(winHeight) }
}

export function isValidArray(data: any[]) {
  return isArray(data) && !isEmpty(data)
}

export function unique(originData: any[], compareKey: string) {
  let newData = cloneDeep(originData),
    obj = {}
  newData = newData.reduce(function (item, next) {
    obj[next[compareKey]]
      ? ''
      : (obj[next[compareKey]] = true && item.push(next))
    return item
  }, [])
  return newData
}

export function isReactionDetail() {
  return window.location.href.includes('/reaction/')
}
