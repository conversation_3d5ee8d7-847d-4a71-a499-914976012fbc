import { notification } from 'antd'

interface MessageProps {
  /**
   * 错误信息
   */
  message?: string

  /**
   *错误详情
   */
  description: string
  duration?: number
}

export default {
  success: function successHandler(props: MessageProps) {
    notification.success({
      message: props?.message,
      className: 'ant-notification-notice-success',
      description: props.description
    })
  },
  info: function infoHandler(props: MessageProps) {
    notification.info({
      message: props?.message,
      className: 'ant-notification-notice-info',
      description: props.description
    })
  },
  warn: function warnHandler(props: MessageProps) {
    notification.warning({
      message: props?.message,
      className: 'ant-notification-notice-warning',
      description: props.description
    })
  },
  error: function errorHandler(props: MessageProps) {
    notification.error({
      message: props?.message,
      className: 'ant-notification-notice-error',
      description: props?.description,
      duration: props?.duration
    })
  }
}
