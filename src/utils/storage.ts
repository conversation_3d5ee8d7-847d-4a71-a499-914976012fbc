export const set = (key: string, value: string | number | Record<any, any>) => {
  localStorage.setItem(key, JSON.stringify(value))
}

export const get = (key: string) => {
  const value = localStorage.getItem(key)
  if (!value || value === 'undefined') return null
  return JSON.parse(value)
}

export function removeLocalValue(keyName: string) {
  localStorage.removeItem(keyName)
}

export interface FormStorageConfig {
  prefix?: string
  suffix?: string
}

const getFormStorageKey = (config?: FormStorageConfig) => {
  let key = `formValues`
  if (config?.prefix) key = `${config.prefix}:${key}`
  if (config?.suffix) key = `${key}:${config.suffix}`
  return key
}

export const getFormStorage = (config?: FormStorageConfig) => ({
  set: (values: Record<string, any>) => set(getFormStorageKey(config), values),
  get: () => get(getFormStorageKey(config)) || {}
})
