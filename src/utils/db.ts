import <PERSON><PERSON>, { type EntityTable } from 'dexie'

interface Smiles {
  smiles: string
  inchi: string
}

const db = new <PERSON><PERSON>('BrainDataBase') as <PERSON><PERSON> & {
  smiles: EntityTable<
    Smiles,
    'smiles' // primary key "id" (for the typings only)
  >
}

// Schema declaration:
db.version(1).stores({
  smiles: '++smiles, inchi' // primary key "id" (for the runtime!)
})

export { db }
export type { Smiles }
