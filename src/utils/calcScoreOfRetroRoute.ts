import { RetroBackbone, RetroParamsConfig } from '@/services/brain'
import { round, sum } from 'lodash'

/**
 * 计算权重可能为负数的加权平均
 *
 * \frac{\sum_{i=0}^{n} ((1-sign_{i}){\div}2 + sign_{i}*score_{i}) * weight_{i}}{\sum_{i=0}^{n} weight_{i}}
 */
export const calcScoreOfRetroRoute = (
  {
    price_score = 1,
    safety_score = 1,
    novelty_score = 1,
    score = 1
  }: RetroBackbone,
  {
    price_score: priceWeight = 1,
    safety_score: safetyWeight = 1,
    novelty_score: noveltyWeight = 1
  }: RetroParamsConfig
): number => {
  const originScores = [price_score, safety_score, novelty_score]
  const originWeights = [priceWeight, safetyWeight, noveltyWeight]
  const base = score / 100
  if (originWeights.every((w) => w === 0)) return base

  const signs = originWeights.map((o) => (o >= 0 ? 1 : -1))
  const weights = originWeights.map((o) => Math.abs(o))
  const scores = originScores.map((score, i) => {
    const sign = signs[i]
    return (1 - sign) / 2 + sign * score
  })
  const scoreWeightedSum = sum(scores.map((score, i) => score * weights[i]))
  const weightSum = sum(weights)
  return base * round(scoreWeightedSum / weightSum, 4)
}
