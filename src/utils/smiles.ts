import { service } from '@/services/brain'
import { groupBy } from 'lodash'
import { db, Smiles } from './db'

const addSmilesToDb = async (smiles: Smiles[]): Promise<undefined> => {
  try {
    await db.smiles.bulkAdd(smiles).catch()
  } catch {}
}

const getSmilesFromDb = async (smiles: string[]): Promise<Smiles[]> => {
  const data = await db.smiles.bulkGet(smiles)
  return data.filter((d) => !!d)
}

export const inchifySmiles = async (smiles: string[]): Promise<string[]> => {
  let cached: Record<string, Smiles[]> = {}
  const inchied = await getSmilesFromDb(smiles)
  if (inchied.length > 0) {
    cached = groupBy(inchied, (i) => i.smiles)
  }

  if (inchied.length === smiles.length) {
    return smiles.map((s) => cached[s]?.[0].inchi)
  }

  const misseds = smiles.filter((s) => !cached[s]?.[0]?.inchi)

  const res = await service('smiles/inchi', {
    method: 'post',
    normalizeData: false,
    data: { smiles: misseds }
  })
    .select()
    .get()
  const inchiedCalced = (res as unknown as { smiles: string[] }).smiles
  const toStore: Smiles[] = misseds.map((s, i) => ({
    smiles: s,
    inchi: inchiedCalced[i]
  }))
  await addSmilesToDb(toStore)

  const calced = groupBy(toStore, (s) => s.smiles)
  return smiles.map((s) => cached[s]?.[0].inchi || calced[s]?.[0].inchi)
}

export const kekulizeSmiles = async (smiles: string[]): Promise<string[]> => {
  const res = await service('smiles/kekulize', {
    method: 'post',
    normalizeData: false,
    data: { smiles }
  })
    .select()
    .get()
  return (res as unknown as { smiles: string[] }).smiles
}
