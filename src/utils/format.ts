import { isEmpty, isNil, toNumber } from 'lodash'

/* e.g. 格式化银行卡账号（每4位进行分割，只允许输入字母和数字，字母自动格式化位大写） */
export function formatAccountNumber(word: string) {
  return !isNil(word) && !isEmpty(word)
    ? word
        .trim()
        .replace(/[\W]/g, '')
        .replace(/(.{4})/g, '$1 ')
        .toUpperCase()
    : ''
}

/**
 *保留两位小数的正数
 */
export function roundToTwoDecimalPlaces(word: number) {
  return word && !isNaN(word) && word > 0 ? toNumber(word).toFixed(2) : ''
}

/**
 *保留1位小数的正数
 */
export function roundToOneDecimalPlaces(word: number) {
  return word && !isNaN(word) && word > 0
    ? word % 1 === 0
      ? word.toFixed(0)
      : word.toFixed(1)
    : 0
}

/**
 *保留2位小数的百分数
 */
export function convertToPercentage(decimal: number) {
  let percentage = (decimal * 100).toFixed(2)
  return percentage + '%'
}
