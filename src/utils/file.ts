import { getEnvConfig, getToken } from '@/utils'
import _ from 'lodash'

export const commaSum = (arr: any) => {
  if (_.isEmpty(arr)) return undefined
  return arr.reduce((x: any, y: any) => {
    return `${x.url},${y.url}`
  })
}

export function getFileBase64(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = (error) => reject(error)
  })
}

interface IUploadProps {
  accept: string
  maxLength?: number
}

export function getUploadProps(props: IUploadProps) {
  return {
    listType: 'picture-card',
    action: `${getEnvConfig().apiBase}common/file/upload`,
    maxLength: 1,
    multiple: false,
    accept: props.accept,
    headers: {
      token: getToken() || undefined
    }
  }
}

export const normFile = (e) => {
  console.log('Upload event:', e)
  return Array.isArray(e) ? e : e && e.fileList
}
