import { MenuDataItem } from '@ant-design/pro-components'
import { cloneDeep } from 'lodash'
import { isValidArray } from './detect'

/* 如果有resource则进行判断，判断不匹配则无权限，隐藏其权限路由 */
export const handleMenu = function (
  menuData: MenuDataItem[],
  authCodeList?: string[]
) {
  if (!authCodeList) return []
  let _menuData = cloneDeep(menuData)
  function traversal(menuItem: MenuDataItem & { resource?: string }) {
    if (!menuItem?.resource) {
      return null
    } else {
      if (
        isValidArray(authCodeList) &&
        !authCodeList.includes(menuItem?.resource)
      ) {
        menuItem.hideInMenu = true
      } else if (isValidArray(menuItem?.children as MenuDataItem[])) {
        for (const ee of menuItem?.children) traversal(ee)
      }
    }
  }
  for (const item of _menuData) traversal(item)
  return _menuData
}
