import { ReactionRole } from '@/services/brain'

export const updateRoleMap = (
  map: Record<string, ReactionRole>,
  reactants: string[]
): Record<string, ReactionRole> => {
  const newMap = Object.entries(map).reduce<Record<string, ReactionRole>>(
    (acc, [k, v]) => {
      if (v === 'main_reactant' && !reactants.includes(k)) acc[k] = 'reactant'
      else acc[k] = v
      return acc
    },
    {}
  )
  if (!Object.values(newMap).includes('main_reactant')) {
    newMap[reactants[0]] = 'main_reactant'
  }
  return newMap
}
