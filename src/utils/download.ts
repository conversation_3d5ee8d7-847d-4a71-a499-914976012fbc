import { getLocaleTag } from '@/components/BpmnEditor/utils'
import message from '@/components/BpmnEditor/utils/message'
import { getToken } from '@/utils'
export function downloadFile(
  path: string,
  fileName: string,
  method?: 'post' | 'get',
  data?: any,
  useAuth: boolean = true
) {
  const xhr = new XMLHttpRequest()
  xhr.open(method || 'get', path)
  if (useAuth) {
    xhr.setRequestHeader('Authorization', `Bearer ${getToken()}`)
  }
  xhr.setRequestHeader('locale', getLocaleTag())
  xhr.setRequestHeader('Content-type', 'application/json')
  xhr.responseType = 'blob'
  xhr.send(data)
  xhr.onload = function () {
    if (this.status === 200 || this.status === 304) {
      const fileReader = new FileReader()
      fileReader.readAsDataURL(this.response)
      fileReader.onload = function () {
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = this.result || ''
        a.download = fileName
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      }
    } else {
      const blobData = xhr.response
      const reader = new FileReader()
      reader.onloadend = () => {
        try {
          const errorResult = JSON.parse(reader.result as string)
          if (errorResult?.error?.message)
            message.error({ message: errorResult?.error?.message })
        } catch (e) {
          console.error(`Error when download file ${fileName}`)
        }
      }
      reader.readAsText(blobData) // 将 Blob 对象转换为文本
    }
  }
}
