import dayjs, { Dayjs } from 'dayjs'

export const formatYTSTime = (time: Dayjs | Date | number) =>
  time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : ''

export const formatYMDHMTime = (time: Dayjs | Date) =>
  time ? dayjs(time).format('YYYY-MM-DD HH:mm') : ''

export const formatYMDTime = (time: Dayjs) =>
  time ? dayjs(time).format('YYYY-MM-DD') : ''

export const waitTime = (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true)
    }, time)
  })
}

export const timeForFilename = (time?: Dayjs | Date) => {
  return dayjs(time).format('YYYYMMDD_HHmmss')
}
