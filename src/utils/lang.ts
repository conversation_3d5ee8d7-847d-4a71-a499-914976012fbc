import { LangData } from '@/types/common'
import { isEmpty } from 'lodash'
import type { ITask } from './../index.d'
import { get } from './storage'

let langType: 'zh-CN' | 'en-US' =
  localStorage.getItem('umi_locale') || window.navigator.language
export const isEN = () => langType === 'en-US'
export function getBpmnTaskText(key: string) {
  let bpmnTaskList = <ITask[]>(
    JSON.parse(sessionStorage.getItem('bpmnTaskList') as string)
  )
  let title = bpmnTaskList.find((e) => e.key === key)?.iconName
  return title
}

export function getLocaleTag() {
  return isEN() ? 'en' : 'zh-CN'
}

export function getWord(char: string) {
  let corpus = get('localLangData'),
    word
  if (!isEmpty(corpus))
    word = corpus.find((e: LangData) => e.code === char)?.value
  return word || char
}
