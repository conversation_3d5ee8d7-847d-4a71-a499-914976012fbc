export function isPDF(mime: string): boolean {
  return /pdf/.test(mime)
}

export function isImage(mime: string) {
  return /image/.test(mime)
}

export function isVideo(mime: string) {
  return /video/.test(mime)
}

// TODO 根据需要单独封装上传功能
export async function upload(file: File) {
  try {
    if (isVideo(file.type)) {
      console.log('---video---')
      // return await uploadVideo(file) TODO review
    } else if (isImage(file.type)) {
      console.log('---image---')
      // return await uploadImage(file) TODO review
    } else {
      console.log('---other---', file)
      // return await uploadFile(file) TODO review
    }
  } catch (e) {
    console.error(e)
  }
}
