import { EnvironmentConfig } from '@/services/brain/types/environment-config'
import { getWord } from '@/utils'
import message from '@/utils/message'
import Cookies from 'js-cookie'
import { delSessionValue, getSessionValue, setSessionValue } from './detect'
import { get } from './storage'
export const tokenKey = 'c12_AI_AUTH'
export function getToken() {
  return localStorage.getItem(tokenKey)
}

export function setToken(token: string) {
  localStorage.setItem(tokenKey, token)
  //   window.location.href = window.location.href.split('/?Authorization=')[0]
}

export function removeToken() {
  if (getToken()) {
    localStorage.clear()
    sessionStorage.clear()
  }
}

export function toProjectPage(errorMessage?: string) {
  if (getSessionValue('showNoPermissionTip') === 'true') return
  setSessionValue('showNoPermissionTip', 'true')
  message.error({
    description: errorMessage || getWord('no-auth-tip'),
    duration: 5
  })
  setTimeout(() => {
    delSessionValue('showNoPermissionTip')
    window.location.replace('/projects')
  }, 5000)
}

export const getEnvConfig = (): EnvironmentConfig & { apiBase: string } => {
  const config = (get('environmentConfig') as EnvironmentConfig) || {}
  const strip = (domain?: string) =>
    domain?.length ? domain.replace(/\/$/, '') : ''
  const stripped = {
    ...config,
    keycloakDomain: strip(config.keycloakDomain),
    baseDomain: strip(config.baseDomain),
    botDomain: strip(config.botDomain)
  }
  const defaultBasePrefix = strip(process.env.defaultBasePrefix)
  const basePrefix = stripped.baseDomain
    ? `${stripped.mode === 'local' ? 'http' : 'https'}://${stripped.baseDomain}`
    : defaultBasePrefix
  return { ...stripped, apiBase: basePrefix || location.origin }
}

export function toLoginPage() {
  let cookies = Cookies.get()
  for (let cookie in cookies) {
    if (cookie) Cookies.remove(cookie)
  }
  localStorage.clear()
  sessionStorage.clear()
  const { loginUrl } = getEnvConfig()
  if (loginUrl) {
    window.location.replace(loginUrl)
  } else if (window.location.pathname !== '/user/login') {
    window.location.replace('/user/login')
  }
  sessionStorage.setItem('redirectHref', window.location.href)
  return
}

export function isPartenerMode() {
  return getEnvConfig().mode === 'partener'
}

export function detectIsNoPermission(status: number, errorMessage?: string) {
  if (status === 403) toProjectPage(errorMessage)
}
