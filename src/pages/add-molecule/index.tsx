import { LazyKetcherEditor } from '@/components/MoleculeEditor'
import { priorityOptions } from '@/constants/options'
import useKetcher from '@/hooks/useKetcher'
import useOptions from '@/hooks/useOptions'
import { ProjectCompound, service } from '@/services/brain'
import { getWord } from '@/utils'
import {
  PageContainer,
  ProForm,
  ProFormGroup,
  ProFormSelect,
  ProFormText
} from '@ant-design/pro-components'
import { Button, message } from 'antd'
import cs from 'classnames'
import { useEffect } from 'react'
import { history, useModel, useParams } from 'umi'
import styles from './index.less'

export default function AddMolecule() {
  const { typeMapForSelect } = useOptions()
  const { id: projectId } = useParams<{ id: string }>()
  const { getProjectInfo, userList } = useModel('project')
  const isSearchMoleculeMode = location.pathname.includes('/search-molecule')
  const { getSmiles, setSmiles, props } = useKetcher()

  useEffect(() => {
    if (!isSearchMoleculeMode) getProjectInfo(projectId as string)
    return () => setSmiles('')
  }, [])
  const { initialState } = useModel('@@initialState')

  return (
    <PageContainer className={cs(styles.addMolecule)}>
      {!isSearchMoleculeMode ? (
        <ProForm
          name="validate_other"
          initialValues={{
            director_id: initialState?.userInfo?.id.toString(),
            type: 'target',
            priority: 'P1'
          }}
          submitter={{
            resetButtonProps: { style: { display: 'none' } },
            submitButtonProps: { className: styles.submitButton }
          }}
          onFinish={async (
            values: Pick<ProjectCompound, 'no' | 'priority' | 'type'>
          ) => {
            let _smiles = await getSmiles({ kekulize: false })
            if (!_smiles) return
            const { error } = await service<ProjectCompound>(
              'project-compounds'
            ).create({
              ...values,
              input_smiles: _smiles,
              project: { id: Number(projectId) }
            } as ProjectCompound)
            setSmiles(_smiles as string)
            if (error) {
              message.error(error?.message)
            } else {
              history.replace(`/projects/${projectId}`)
            }
          }}
        >
          <ProFormGroup>
            <ProFormText
              rules={[{ required: true }]}
              name="no"
              label={getWord('molecules-no')}
              tooltip={getWord('max-l-30')}
              formItemProps={{
                rules: [{ max: 30, message: getWord('max-30-l') }]
              }}
            />
            <ProFormSelect
              name="type"
              label={getWord('molecules-type')}
              valueEnum={typeMapForSelect}
              placeholder={getWord('select-tip')}
              rules={[{ required: true }]}
            />
            <ProFormSelect
              name="priority"
              label={getWord('molecule-priority')}
              showSearch
              debounceTime={300}
              options={priorityOptions}
              placeholder={getWord('select-tip')}
              rules={[{ required: true }]}
            />
            <ProFormSelect
              name="director_id"
              label={getWord('molecule-owner')}
              rules={[{ required: true }]}
              options={userList}
            />
          </ProFormGroup>
        </ProForm>
      ) : (
        <div className={styles.operateContent}>
          <Button
            type="primary"
            className={styles.searchButton}
            onClick={async () => {
              const smiles = await getSmiles({ inchify: true })
              if (typeof smiles === 'string') {
                history.push(
                  `/material/manage?searchSmiles=${encodeURIComponent(smiles)}`
                )
              } else {
                history.push(`/material/manage`)
              }
            }}
          >
            {getWord('search')}
          </Button>
        </div>
      )}
      <LazyKetcherEditor {...props} />
    </PageContainer>
  )
}
