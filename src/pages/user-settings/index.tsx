import { ReactComponent as ConfirmIcon } from '@/assets/svgs/confrim.svg'
import Fields from '@/components/ReactionTabs/ReactionLibTab/Filter/Fields'
import {
  frontToBack,
  useFilterForm
} from '@/components/ReactionTabs/ReactionLibTab/Filter/useFilterForm'
import RouteSortDimension from '@/components/RouteSortDimension'
import SectionTitle from '@/components/SectionTitle'
import { useUserSettingQuery } from '@/hooks/useUserSetting'
import { service } from '@/services/brain'
import {
  UserQuotationSetting,
  UserRetroSetting
} from '@/services/brain/types/user-setting'
import { SettingLabel, UserSetting } from '@/types/models'
import { getWord } from '@/utils'
import {
  PageContainer,
  ProForm,
  ProFormCheckbox,
  ProFormDigit,
  ProFormRadio,
  ProFormSwitch
} from '@ant-design/pro-components'
import { useAccess, useModel } from '@umijs/max'
import { But<PERSON>, Form } from 'antd'
import useMessage from 'antd/es/message/useMessage'
import cs from 'classnames'
import { useEffect, useState } from 'react'
import { MaterialOption } from '../compound/components/SearchParam/MaterialLabSelect'
import styles from './index.less'

export default function UserSettings() {
  const access = useAccess()
  const [message, msgContextHolder] = useMessage()
  const { getMaterialLibOptions } = useModel('compound')
  const { initialState } = useModel('@@initialState')
  const { setting: { retro, procedure, quote, idMap } = {}, refetch } =
    useUserSettingQuery()
  const [retroParamsForm] = Form.useForm<UserRetroSetting>()
  const [quotationForm] = Form.useForm<UserQuotationSetting>()
  const { form: procedureForm } = useFilterForm(procedure)
  const [materialLibOptions, setMaterialLibOptions] = useState<
    MaterialOption[]
  >([])

  useEffect(() => {
    retroParamsForm.setFieldsValue(retro || {})
  }, [retro])
  useEffect(() => {
    quotationForm.setFieldsValue(quote || {})
  }, [quote])
  useEffect(() => {
    console.log(procedure)
  }, [procedure])

  useEffect(() => {
    let mount = true
    const fetchData = async () => {
      let options = await getMaterialLibOptions()
      if (mount) setMaterialLibOptions(options)
    }
    fetchData()
    return () => {
      mount = false
    }
  }, [])

  const getValues = async (label: SettingLabel) => {
    switch (label) {
      case 'retro_params':
        return await retroParamsForm.validateFields()
      case 'quotation':
        return await quotationForm.validateFields()
      case 'procedure':
        return frontToBack(await procedureForm.validateFields())
    }
  }
  const saveSetting = async (label: SettingLabel, values: any) => {
    const data = { setting_value: values, setting_label: label }
    const request = async (id?: number) => {
      if (id) {
        return await service<UserSetting>(`user-settings`).update(id, data)
      }
      return await service<UserSetting>(`user-settings`).create(data)
    }
    switch (label) {
      case 'retro_params':
        return await request(idMap?.retro)
      case 'quotation':
        return await request(idMap?.quote)
      case 'procedure':
        return await request(idMap?.procedure)
    }
  }
  const confirm = async (labels: SettingLabel[]) => {
    const result = await Promise.all(
      labels.map(async (label) => saveSetting(label, await getValues(label)))
    )
    const resError = result.find((res) => res.error)

    if (resError) message.error(resError.error?.message)
    else message.success(getWord('operate-success'))
    refetch()
  }

  return (
    <PageContainer
      className={cs(styles.userSettings, {
        [styles['unfoldWidth']]: !initialState?.isMenuCollapsed,
        [styles['foldWidth']]: initialState?.isMenuCollapsed
      })}
    >
      {msgContextHolder}
      {access?.authCodeList?.includes('settings.tab.retro_params') && (
        <>
          <SectionTitle
            word={getWord(`retro_params-settings`)}
            extra={
              <Button
                type="primary"
                className="flex-align-items-center"
                icon={<ConfirmIcon width={14} fill="#fff" />}
                onClick={() => confirm(['retro_params', 'procedure'])}
                key="config-button"
              >
                {getWord('apply')}
              </Button>
            }
          />
          <div className={styles.formContent}>
            <ProForm
              form={retroParamsForm}
              submitter={false}
              layout="horizontal"
              wrapperCol={{ span: 24 }}
            >
              <div className={styles.title}>{getWord('search-setting')}</div>
              <ProFormDigit
                name="max_search_time"
                label={getWord('max-search-time')}
                min={0}
                addonAfter={getWord('min')}
              />
              <ProFormCheckbox.Group
                name="material_lib"
                label={getWord('default-material-sources-for-retrosynthesis')}
                options={materialLibOptions}
              />

              <div className={styles.title}>
                {getWord('route-display-style')}
              </div>
              <div className={styles.subTitle}>
                {getWord('route-sort-dimension-weight-setting')}
              </div>
              <RouteSortDimension />
              <div className={styles.commonBorder} />

              <ProFormRadio.Group
                name="route_detail_show_policy"
                label={getWord('route-detail-display-style')}
                options={[
                  {
                    label: getWord('default-intermediates'),
                    value: 'only_key_reactions'
                  },
                  {
                    label: getWord('default-all-route'),
                    value: 'all_route'
                  }
                ]}
              />
              <ProFormSwitch
                name="route_show_yields"
                label={getWord('show-yield')}
              />

              <div className={styles.title}>
                {getWord('procedure-filter.title')}
              </div>
              <ProForm
                form={procedureForm}
                className={styles.innerForm}
                submitter={false}
                layout="horizontal"
                wrapperCol={{ span: 24 }}
              >
                <Fields />
              </ProForm>
            </ProForm>
          </div>
        </>
      )}
      {access?.authCodeList?.includes('settings.tab.quotation') && (
        <>
          <SectionTitle
            word={getWord(`quotation-settings`)}
            extra={
              <Button
                type="primary"
                className="flex-align-items-center"
                icon={<ConfirmIcon width={14} fill="#fff" />}
                onClick={() => confirm(['quotation'])}
                key="config-button"
              >
                {getWord('apply')}
              </Button>
            }
          />
          <div className={styles.formContent}>
            <ProForm
              form={quotationForm}
              submitter={false}
              layout="horizontal"
              wrapperCol={{ span: 24 }}
            >
              <div className={cs(styles.title, styles.empty)}></div>
              <ProFormDigit
                name="yields"
                label={getWord('default-yield')}
                max={100}
                min={0}
                addonAfter="%"
              />
              <ProFormDigit
                name="FTE_rate"
                label={getWord('FTE-unit')}
                min={0}
                addonAfter={getWord('rmb-unit')}
              />
              <ProFormDigit
                name="ratio"
                label={getWord('Premium-coefficient')}
                max={100}
                min={0}
                addonAfter={' '}
              />
              <ProFormRadio.Group
                name="labor_logic"
                label={getWord('WH-calculation-method')}
                options={[
                  {
                    label: getWord('Estimate-procedure'),
                    value: 'by_procedure'
                  },
                  {
                    label: getWord('Estimate-reaction'),
                    value: 'by_leyan_reaction_difficulty'
                  }
                ]}
              />
              <ProFormCheckbox.Group
                name="material_lib"
                label={getWord('default-material-sources-for-quotation')}
                options={materialLibOptions}
              />
            </ProForm>
          </div>
        </>
      )}
    </PageContainer>
  )
}
