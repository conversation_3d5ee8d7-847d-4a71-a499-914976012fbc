@import '@/style/variables.less';
.unfoldWidth {
  width: @content-width;
}
.foldWidth {
  width: @content-width-foldMenu;
}
.userSettings {
  padding: 16px;
  background-color: #fff;
  :global {
    .ant-pro-page-container-children-container {
      min-height: @main-content-height_hasCrumbs;
    }
    .ant-pagination {
      margin: 10px 0;
    }
  }
  .formContent {
    padding: 4px 0px 4px 0px;
    border-radius: 4px;
    .noBorder {
      border-bottom: unset !important;
    }
    .formItem {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: calc(100% - 12px);
      font-weight: 500;
      font-size: 14px;
      letter-spacing: 0em;
      border-bottom: 1px solid #f0f0f0;
      span {
        position: relative;
        top: 3px;
      }
      :global {
        .ant-row {
          position: relative;
          top: 10px;
        }
        .ant-checkbox,
        .ant-radio-inner {
          top: 1px !important;
        }
      }
    }
    .yield {
      :global {
        input {
          width: 156px;
        }
      }
    }
    .FTE {
      :global {
        input {
          width: 136px;
        }
      }
    }
    .FTE_EN {
      :global {
        input {
          width: 117px;
        }
      }
    }
    .commonBorder {
      margin-bottom: 12px;
      border-top: 1px solid #f0f0f0;
    }
    .formItem:last-child {
      border-bottom: unset !important;
    }

    .title {
      position: relative;
      left: -22px;
      display: flex;
      align-items: center;
      width: calc(100% + 44px);
      height: 48px;
      margin-bottom: 12px;
      padding-left: 20px;
      font-weight: bold;
      font-size: 16px;
      text-align: left;
      background-color: #fafafa;
      &.empty {
        height: 0;
      }
    }
    .subTitle {
      margin: 20px 0 16px 0;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
    }
    :global {
      .commonStyle {
        color: #626262;
        font-weight: 400;
        font-size: 14px;
        text-align: left;
      }
      .ant-form {
        margin: 16px 2px;
        padding: 0px 22px 10px 22px;
        box-shadow: 0px 0px 8px 0px #0000001a;
      }
      .ant-form-item {
        margin-bottom: 20px;
      }
      .ant-form-item-label {
        label {
          .commonStyle;
        }
      }
      .ant-form-item-control,
      .ant-radio-wrapper-in-form-item,
      .ant-form-item-control-input-content,
      ant-radio-group {
        .commonStyle;
      }
    }

    .innerForm {
      margin: 0;
      padding: 0;
      box-shadow: unset;
    }
  }
}
