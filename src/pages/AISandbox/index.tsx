import { useModel } from '@umijs/max'
import cs from 'classnames'
import styles from './index.less'

export default function AISandbox() {
  const { initialState } = useModel('@@initialState')
  return (
    <div
      className={cs(styles.aiSandbox, {
        [styles['unfoldWidth']]: !initialState?.isMenuCollapsed,
        [styles['foldWidth']]: initialState?.isMenuCollapsed
      })}
    >
      <iframe src="https://algo.labwise.cn/" />
    </div>
  )
}
