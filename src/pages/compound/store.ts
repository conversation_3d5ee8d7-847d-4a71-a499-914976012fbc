import { create } from 'zustand'
import { combine, subscribeWithSelector } from 'zustand/middleware'

import { RetroModelOpen } from './components/RetroModel'

interface State {
  retroModel: RetroModelOpen
  filterDrawer: boolean
  compoundId?: number | string
}

const initState: State = {
  retroModel: 'close',
  filterDrawer: false
}

export const useSwitchStore = create(
  subscribeWithSelector(
    combine({ ...initState }, (set) => ({
      set: (newState: Partial<State>) => set((s) => ({ ...s, ...newState })),
      setRetroModel: (retroModel: RetroModelOpen) =>
        set((s) => ({ ...s, retroModel })),
      setFilterDrawer: (filterDrawer: boolean) =>
        set((s) => ({ ...s, filterDrawer })),
      setCompoundId: (compoundId?: number | string) =>
        set((s) => ({ ...s, compoundId }))
    }))
  )
)
