import { SearchLog } from '@/services/brain'
import { PageContainer } from '@ant-design/pro-components'
import { useParams } from '@umijs/max'
import { Content } from 'antd/es/layout/layout'
import cs from 'classnames'
import { FC, useEffect, useRef, useState } from 'react'
import BackboneTabs from './components/BackboneTabs'
import { useBackboneTabsStore } from './components/BackboneTabs/store'
import CommentDrawer from './components/Comment'
import CompoundInfo from './components/CompoundInfo'
import FilterDrawer from './components/FilterDrawer'
import RetroButton from './components/RetroButton'
import RetroLogModel from './components/RetroLogModel'
import RetroModel from './components/RetroModel'
import SearchHistory from './components/SearchHistory'
import './index.less'
import { RouteEventContext, useRouteEventContext } from './RouteEventContext'
import { useSwitchStore } from './store'

interface PageParams {
  id?: string
  compoundId?: string
  [key: string]: string | undefined
}

const CompoundDetail: FC = ({}) => {
  const context = useRouteEventContext()
  const { setCompoundId } = useSwitchStore()

  const { compoundId: moleculeId } = useParams<PageParams>()
  const { retroModel, setRetroModel, filterDrawer, setFilterDrawer } =
    useSwitchStore()
  const [displayLogs, setDisplayLogs] = useState<SearchLog[]>()
  const refetchHistoryRef = useRef<(retroId?: string) => void>()
  const { tabType, setPreference } = useBackboneTabsStore()

  const onRetroSuccess = (retroId?: string) => {
    refetchHistoryRef?.current?.(retroId)
    setRetroModel('close')
  }

  useEffect(() => setCompoundId(moleculeId), [moleculeId])

  return (
    <RouteEventContext.Provider value={context}>
      <CommentDrawer />
      <FilterDrawer
        open={filterDrawer}
        onClose={() => setFilterDrawer(false)}
        onChange={(p) => setPreference(p)}
      />
      <PageContainer className="target-molecule-detail-root">
        <Content className="compound-body">
          <div className={cs('left-side', { none: tabType !== 'aiGenerated' })}>
            <CompoundInfo moleculeId={moleculeId} />
            <RetroButton
              moleculeId={moleculeId}
              onRetro={() => setRetroModel('init')}
            />
            <SearchHistory
              moleculeId={moleculeId}
              refetchRegister={(fn) => (refetchHistoryRef.current = fn)}
              onDisplayLogs={(logs) => setDisplayLogs(logs)}
              onDisplayParams={(params) => setRetroModel(params)}
            />
          </div>
          <BackboneTabs compoundId={moleculeId} />
        </Content>
      </PageContainer>

      <RetroModel
        moleculeId={moleculeId}
        open={retroModel}
        onSuccess={onRetroSuccess}
        onCancel={() => setRetroModel('close')}
      />
      <RetroLogModel logs={displayLogs} />
    </RouteEventContext.Provider>
  )
}
export default CompoundDetail
