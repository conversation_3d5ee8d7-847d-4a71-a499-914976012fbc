import { ReactComponent as GenerateRoutesIcon } from '@/assets/svgs/gnerate-routes.svg'
import { getWord, isReadonlyMolecule } from '@/utils'
import { useAccess } from '@umijs/max'
import { Button } from 'antd'
import React from 'react'
import { useProjectCompound } from '../CompoundInfo/useCompoundInfo'

export interface RetroButtonProps {
  moleculeId?: string
  onRetro?: () => void
}

const RetroButton: React.FC<RetroButtonProps> = ({ moleculeId, onRetro }) => {
  const access = useAccess()
  const { data, isLoading } = useProjectCompound(moleculeId)

  const display =
    !isLoading &&
    data?.id &&
    !isReadonlyMolecule(undefined, data?.status) &&
    access?.authCodeList?.includes('compound.button.gnerate-routes')

  if (!display) return null

  return (
    <div className="buttons-wrapper">
      <Button
        type="primary"
        block
        size="middle"
        className="action-button flex-center"
        onClick={() => onRetro?.()}
      >
        <GenerateRoutesIcon width={16} fill="#fff" />
        {getWord('gnerate-routes')}
      </Button>
    </div>
  )
}

export default RetroButton
