import { RetroProcess, RetroProcesses, service } from '@/services/brain'
import { getWord } from '@/utils'
import { useModel } from '@umijs/max'
import { message, Modal, notification } from 'antd'
import React, { useEffect, useRef, useState } from 'react'
import { useProjectCompound } from '../CompoundInfo/useCompoundInfo'
import SearchParam, { type SearchParamFields } from '../SearchParam'

export type RetroModelOpen =
  | 'init'
  | 'close'
  | (SearchParamFields & { retroId?: string })

export interface RetroModelProps {
  moleculeId?: string
  open?: RetroModelOpen
  onSuccess?: (retroId?: string) => void
  onCancel: () => void
}

const RetroModel: React.FC<RetroModelProps> = ({
  moleculeId,
  onSuccess,
  onCancel,
  open: propOpen
}) => {
  const [messageApi, msgContextHolder] = message.useMessage()
  const [notificationApi, notificationContextHolder] =
    notification.useNotification()
  const { data: compound } = useProjectCompound(moleculeId)
  const { initialState: { userInfo = undefined } = {} } =
    useModel('@@initialState')

  const [open, setOpen] = useState<boolean>(false)
  const [readonly, setReadonly] = useState<boolean>(false)
  const [loading, setLoading] = useState<boolean>(false)
  const [viewParams, setViewParams] = useState<
    SearchParamFields & { retroId?: string }
  >()
  const getParamsRef = useRef<() => Promise<SearchParamFields | undefined>>()

  const doRetro = async () => {
    setLoading(true)
    const params = await getParamsRef.current?.().catch()
    if (!params) {
      setLoading(false)
      return
    }

    const { data, error } = await service<RetroProcesses>(
      'retro-processes'
    ).create({
      project_compound: compound?.id,
      creator_id: userInfo?.username || '',
      params
    })
    setLoading(false)

    if (!error && data) {
      messageApi.success(getWord('search-been-created'))
      onSuccess?.((data as RetroProcess).retro_id)
    } else if (error) {
      notificationApi.error({
        message: getWord('route-generate-failed'),
        description: `${getWord('error-detail')}${JSON.stringify(
          error?.message
        )}`
      })
    }
  }

  useEffect(() => {
    if (!!propOpen && typeof propOpen === 'object') {
      setReadonly(true)
      setViewParams(propOpen)
    } else {
      setReadonly(false)
      setViewParams(undefined)
    }

    setLoading(false)
    setOpen(propOpen !== 'close')
  }, [propOpen])

  if (!compound) return null

  return (
    <>
      {msgContextHolder}
      {notificationContextHolder}
      <Modal
        open={open}
        footer={readonly ? false : undefined}
        onCancel={() => onCancel()}
        okButtonProps={{ disabled: loading || readonly }}
        okText={getWord('submit')}
        confirmLoading={loading}
        centered
        width={510}
        destroyOnClose
        onOk={doRetro}
      >
        <SearchParam
          target={compound?.input_smiles}
          registerParamsGetter={(fn) => (getParamsRef.current = fn)}
          viewOnly={viewParams}
          retroId={viewParams?.retroId}
          onEdit={() => setReadonly(false)}
          onLoading={(loading) => setLoading(loading)}
        />
      </Modal>
    </>
  )
}

export default RetroModel
