import RouteSortDimension from '@/components/RouteSortDimension'
import { useUserSetting } from '@/hooks/useUserSetting'
import { RetroParamsConfig } from '@/services/brain'
import { getWord, isEN } from '@/utils'
import { ProForm } from '@ant-design/pro-components'
import { useModel } from '@umijs/max'
import { Divider, Drawer, Form } from 'antd'
import { isEmpty } from 'lodash'
import { useEffect, useState } from 'react'
import styles from './index.less'

export interface FilterDrawerProps {
  open: boolean
  onClose: () => void
  onChange?: (config: RetroParamsConfig) => void
}

export default function FilterDrawer({
  open,
  onClose: close,
  onChange
}: FilterDrawerProps) {
  const [retroParamsForm] = Form.useForm<any>()
  const { setting } = useUserSetting()
  const [openDrawer, setOpenDrawer] = useState<boolean>(true)

  const { retroParamsConfig, updateRetroParamsConfig } = useModel('compound')

  useEffect(() => {
    if (setting?.retro && !isEmpty(setting?.retro)) {
      let defaultRetroParams = {
        safety_score: setting?.retro?.safety_score,
        novelty_score: setting?.retro?.novelty_score,
        price_score: setting?.retro?.price_score
      }
      updateRetroParamsConfig(defaultRetroParams)
      retroParamsForm.setFieldsValue(defaultRetroParams)
      onChange?.(defaultRetroParams)
    }
  }, [setting?.retro])

  useEffect(() => {
    setOpenDrawer(open || false)
  }, [open])

  return (
    <Drawer
      forceRender
      width={isEN() ? 408 : 508}
      title={
        <div className="flex-align-items-center">
          {getWord('route-sort-dimension-weight-setting')}
        </div>
      }
      placement="right"
      onClose={() => {
        setOpenDrawer(false)
        close()
      }}
      open={openDrawer}
      className={styles.filterDrawer}
      destroyOnClose={true}
    >
      <section>
        <h1>{getWord('demension-weight')}</h1>
        <Divider />
        <ProForm
          onValuesChange={(values) => {
            updateRetroParamsConfig({ ...retroParamsConfig, ...values })
            onChange?.({ ...retroParamsConfig, ...values })
          }}
          form={retroParamsForm}
          submitter={false}
          layout="horizontal"
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          <RouteSortDimension />
        </ProForm>
      </section>
    </Drawer>
  )
}
