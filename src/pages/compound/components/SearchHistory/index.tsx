import { RetroProcess, SearchLog } from '@/services/brain'
import { getWord } from '@/utils'
import { FilterOutlined } from '@ant-design/icons'
import { Button, Checkbox, Flex, Popover, Spin } from 'antd'
import React, { useEffect, useState } from 'react'
import { useBackboneTabsStore } from '../BackboneTabs/store'
import { SearchParamFields } from '../SearchParam'
import HistoryCard from './HistoryCard'
import styles from './index.less'
import { useRetroUpdateStore } from './store'
import useRetroHistory from './useRetroHistory'

export interface SearchHistoryProps {
  moleculeId?: string
  refetchRegister?: (fn?: () => void) => void
  onDisplayLogs?: (logs: SearchLog[]) => void
  onDisplayParams?: (params: SearchParamFields & { retroId: string }) => void
}

const SearchHistory: React.FC<SearchHistoryProps> = ({
  moleculeId,
  refetchRegister,
  onDisplayLogs,
  onDisplayParams
}) => {
  const [successOnly, setSuccessOnly] = useState<boolean>(false)
  const { setSelectedProcessId, selectedProcessId } = useRetroUpdateStore()
  const { data, error, isLoading, refetch } = useRetroHistory(moleculeId, true)
  const { setCount } = useBackboneTabsStore()
  const selectedProcess = data?.find((p) => p.retro_id === selectedProcessId)

  const filtered = successOnly
  const filter = (p: RetroProcess) => {
    if (successOnly) {
      return p.status === 'completed'
    }
    return true
  }
  const processes = data?.filter(filter)

  useEffect(() => {
    const onCreated = (retroId?: string) => {
      refetch?.()
      setTimeout(() => {
        if (retroId) setSelectedProcessId(retroId)
      }, 300)
    }
    refetchRegister?.(onCreated)
  }, [refetch])

  useEffect(() => {
    const firstId = processes?.[0]?.retro_id
    if (
      firstId &&
      (!selectedProcessId || firstId !== selectedProcessId) &&
      processes?.findIndex((p) => p.retro_id === selectedProcessId) === -1
    ) {
      setSelectedProcessId(firstId)
    }
  }, [processes, selectedProcessId])

  useEffect(() => {
    if (!selectedProcessId) return
    setCount('aiGenerated', selectedProcess?.retro_backbones?.length || 0)
  }, [selectedProcessId, selectedProcess])

  if (error || !processes) {
    return null
  }

  const filterComp = (
    <Popover
      placement="right"
      content={
        <Checkbox
          checked={successOnly}
          onChange={(e) => setSuccessOnly(e.target.checked)}
        >
          {getWord('pages.projectCompound.retroHistory.success-retro-only')}
        </Checkbox>
      }
    >
      <Button type={filtered ? 'link' : 'text'} size="small">
        <FilterOutlined />
      </Button>
    </Popover>
  )

  return (
    <>
      <Flex justify="space-between" align="center" wrap={false}>
        <p>
          {getWord('pages.projectCompound.retroHistory.history')}
          {isLoading ? <Spin /> : `(${processes.length})`}
        </p>
        <p>{filterComp}</p>
      </Flex>
      <div className={styles.historyCardsWrapper}>
        <Flex gap={8} vertical>
          {processes.map((p) => (
            <HistoryCard
              retroProcess={p}
              newCount={p.offset}
              onDisplayLogs={() => onDisplayLogs?.([...(p.search_log || [])])}
              onDisplayParams={() =>
                onDisplayParams?.({ ...(p.params || {}), retroId: p.retro_id })
              }
              key={p.id}
            />
          ))}
        </Flex>
      </div>
    </>
  )
}

export default SearchHistory
