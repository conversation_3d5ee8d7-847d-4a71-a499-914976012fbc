import { queryWithDefaultOrder, RetroProcess } from '@/services/brain'
import { useQuery } from '@tanstack/react-query'
import { useRetroUpdateStore } from './store'
import useUpdateStoreWithSocket from './useRetroProcessUpdates'

const fields: (keyof RetroProcess)[] = [
  'id',
  'params',
  'retro_backbones',
  'retro_id',
  'status',
  'creator_id',
  'createdAt',
  'updatedAt',
  'search_log',
  'predict_start_time',
  'search_start_time',
  'search_end_time',
  'queue_count',
  'backbone_tree'
]

const showStatus: RetroProcess['status'][] = [
  'completed',
  'running',
  'pending',
  'limited',
  'failed'
]

const fetchHistory = async (moleculeId: number) => {
  const { data, error } = await queryWithDefaultOrder<RetroProcess>(
    'retro-processes',
    undefined,
    fields
  )
    .filterDeep('status', 'in', showStatus)
    .filterDeep('project_compound.id', 'eq', moleculeId)
    .populateWith('retro_backbones', ['id'])
    .paginate(1, 1000)
    .get()

  if (!error && data) {
    return data
  }

  throw new Error(error?.message || 'Server Error')
}

const useRetroHistory = (id?: number | string, withUpdate?: boolean) => {
  const idNum = typeof id !== 'number' ? Number.parseInt(id || '') : id
  const { data, error, isLoading, refetch } = useQuery({
    queryKey: ['retro-processes', idNum],
    queryFn: () => (id ? fetchHistory(idNum) : undefined),
    enabled: !isNaN(idNum)
  })

  const updates = useRetroUpdateStore((s) => s.getByCompoundId(idNum))
  const selectedProcessId = useRetroUpdateStore((s) => s.selectedProcessId)
  useUpdateStoreWithSocket(withUpdate && !isNaN(idNum) ? idNum : undefined)
  if (isNaN(idNum)) return {}

  let updated: RetroProcess[] = [...(data || [])]
  if (updates && withUpdate) {
    updated = updated?.map?.((process) => {
      if (updates[process.retro_id])
        return { ...process, ...updates[process.retro_id] }
      return process
    })
  }
  return {
    data: updated,
    error,
    isLoading,
    refetch,
    selected: updated.find((u) => u.retro_id === selectedProcessId)
  }
}

export default useRetroHistory
