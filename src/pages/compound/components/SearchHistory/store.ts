import { RetroProcess } from '@/services/brain'
import { create } from 'zustand'
import { combine, subscribeWithSelector } from 'zustand/middleware'

type RetroProcessMap = Record<number | string, Partial<RetroProcess>>

interface State {
  updates?: Record<number, RetroProcessMap>
  selectedProcessId?: string
  updatesByRetroId?: RetroProcessMap
}

const initState: State = {
  updates: {},
  updatesByRetroId: {}
}

const update = (
  compoundId: number,
  updates: (Partial<RetroProcess> & Required<Pick<RetroProcess, 'retro_id'>>)[],
  state: State
): State => {
  if (!updates.length) return state

  const nextMap = state.updates?.[compoundId] || {}
  for (const u of updates) {
    nextMap[u.retro_id] = { ...(nextMap[u.retro_id] || {}), ...u }
  }
  const nextUpdates = { ...(state.updates || {}), [compoundId]: nextMap }
  const nextUpdatesByRetroId = Object.values(
    nextUpdates
  ).reduce<RetroProcessMap>((acc, cur) => ({ ...acc, ...cur }), {})
  return {
    ...state,
    updates: nextUpdates,
    updatesByRetroId: nextUpdatesByRetroId
  }
}

export const useRetroUpdateStore = create(
  subscribeWithSelector(
    combine({ ...initState }, (set, get) => ({
      update: (compoundId: number, updates: RetroProcess[]) =>
        set((state) => update(compoundId, updates, state)),
      setSelectedProcessId: (retroId: string) =>
        set((state) => ({ ...state, selectedProcessId: retroId })),
      clearOffset: (compoundId: number, retroId: string) =>
        set((state) =>
          update(compoundId, [{ retro_id: retroId, offset: 0 }], state)
        ),
      getByCompoundId: (id: number) => get().updates?.[id],
      getByRetroId: (id: number) => get().updatesByRetroId?.[id]
    }))
  )
)
