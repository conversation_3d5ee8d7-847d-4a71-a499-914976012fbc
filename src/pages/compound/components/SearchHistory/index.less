.historyCardsWrapper {
  height: auto;
  padding: 8px 0;
  overflow: hidden auto;
}

.historyCard {
  position: relative;

  &.selected {
    background: #eef7ff;
    border: 1px solid #449ae1;
  }

  .statusTag {
    align-items: center;
    width: max-content;
    padding: 0 4px;
    font-size: 12px;
    line-height: 16px;
  }

  .buttonWrapper {
    position: absolute;
    top: 4px;
    right: 4px;
  }

  :global {
    .ant-card-body {
      padding: 4px;
    }
    .ant-descriptions-item-label,
    .ant-descriptions-item-content {
      font-size: 12px;
    }
  }
}
