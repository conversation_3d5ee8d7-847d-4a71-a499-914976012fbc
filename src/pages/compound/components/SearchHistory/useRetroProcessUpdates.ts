import { RetroProcess } from '@/services/brain'
import { getEnvConfig } from '@/utils'
import { useEffect } from 'react'
import { io } from 'socket.io-client'
import { useRetroUpdateStore } from './store'

const useUpdateStoreWithSocket = (projectCompoundId?: number) => {
  const { update } = useRetroUpdateStore()

  useEffect(() => {
    if (!projectCompoundId) return
    const socket = io(`${getEnvConfig().apiBase}`, {
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000
    })

    socket.on('connect', () => {
      socket.emit('retro-process:join', projectCompoundId)
    })

    socket.on('retro-process:update', (updates: RetroProcess[]) => {
      update(projectCompoundId, updates)
    })
  }, [projectCompoundId])
}

export default useUpdateStoreWithSocket
