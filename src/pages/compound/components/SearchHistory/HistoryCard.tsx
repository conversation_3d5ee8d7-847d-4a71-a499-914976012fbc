import StatusRender from '@/components/StatusRender'
import { retroStatus } from '@/constants'
import { RetroProcess } from '@/services/brain'
import { formatYTSTime, getWord } from '@/utils'
import { ControlOutlined, InfoCircleOutlined } from '@ant-design/icons'
import { Badge, <PERSON>ton, Card, ConfigProvider, Descriptions } from 'antd'
import { DescriptionsItemType } from 'antd/es/descriptions'
import cs from 'classnames'
import dayjs, { Dayjs } from 'dayjs'
import React from 'react'
import styles from './index.less'
import { useRetroUpdateStore } from './store'

const colorMap = {
  completed: 'success',
  running: 'processing',
  limited: 'warning',
  pending: 'warning',
  failed: 'error'
}

const getItems = (
  retroProcess: RetroProcess,
  newCount?: number
): DescriptionsItemType[] => {
  const getTimeString = (date?: Dayjs | Date) => {
    return date ? formatYTSTime(date) : ''
  }

  const items = [
    {
      key: 'owner',
      label: getWord('owner'),
      children: retroProcess.creator_id?.split('@')[0]
    },
    {
      key: 'status',
      label: getWord('status'),
      children: (
        <>
          <Badge size="small" className={styles.CardBadge} count={newCount}>
            <StatusRender
              colorMap={colorMap}
              className={cs(styles.statusTag)}
              label={getWord(retroStatus[retroProcess.status])}
              status={retroProcess.status}
            />
          </Badge>
        </>
      )
    },
    {
      key: 'route-count',
      label: getWord('number-of-routes'),
      children: retroProcess.retro_backbones?.length || retroProcess.count || 0
    },
    {
      key: 'creation-time',
      label: getWord('creation-time'),
      children: getTimeString(retroProcess.createdAt)
    }
  ]

  const { status, predict_start_time, search_start_time, search_end_time } =
    retroProcess
  if (['limited', 'pending'].includes(status) && predict_start_time) {
    items.push({
      key: 'predict-start-time',
      label: getWord('estimate-start'),
      children: getTimeString(dayjs(predict_start_time))
    })
  }
  if (status === 'pending') {
    items.push({
      key: 'tasks-in-queue',
      label: getWord('tasks-in-queue'),
      children: retroProcess.queue_count || 0
    })
  }
  if (search_start_time) {
    items.push({
      key: 'start-time',
      label: getWord('pages.searchTable.updateForm.schedulingPeriod.timeLabel'),
      children: getTimeString(dayjs(search_start_time))
    })
  }
  if (search_end_time) {
    items.push({
      key: 'complete-time',
      label: getWord('complete-time'),
      children: getTimeString(dayjs(search_end_time))
    })
  }

  return items.map((item) => ({ span: 24, ...item }))
}

export interface HistoryCardProps {
  retroProcess: RetroProcess
  newCount?: number
  onDisplayLogs?: () => void
  onDisplayParams?: () => void
}

const HistoryCard: React.FC<HistoryCardProps> = ({
  retroProcess,
  newCount,
  onDisplayLogs,
  onDisplayParams
}) => {
  const { selectedProcessId, setSelectedProcessId, clearOffset } =
    useRetroUpdateStore()
  const selected = retroProcess.retro_id === selectedProcessId

  return (
    <ConfigProvider
      theme={{
        components: {
          Descriptions: { itemPaddingBottom: 4 }
        }
      }}
    >
      <Card
        size="small"
        type="inner"
        title={false}
        hoverable
        className={cs(styles.historyCard, { [styles.selected]: selected })}
        onClick={() => {
          setSelectedProcessId(retroProcess.retro_id)
          clearOffset(retroProcess.compound_id, retroProcess.retro_id)
        }}
      >
        <Descriptions items={getItems(retroProcess, newCount)} column={24} />

        <div className={styles.buttonWrapper}>
          <Button
            size="small"
            type="text"
            shape="circle"
            onClick={(e) => {
              onDisplayLogs?.()
              e.stopPropagation()
            }}
          >
            <InfoCircleOutlined />
          </Button>

          <Button
            size="small"
            type="text"
            shape="circle"
            onClick={(e) => {
              onDisplayParams?.()
              e.stopPropagation()
            }}
          >
            <ControlOutlined />
          </Button>
        </div>
      </Card>
    </ConfigProvider>
  )
}

export default HistoryCard
