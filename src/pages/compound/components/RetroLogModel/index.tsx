import { SearchLog } from '@/services/brain'
import { formatYTSTime, getWord } from '@/utils'
import { Col, Modal, Row } from 'antd'
import React, { useEffect, useState } from 'react'

export interface RetroLogModelProps {
  logs?: SearchLog[]
}

const RetroLogModel: React.FC<RetroLogModelProps> = ({ logs }) => {
  const [open, setOpen] = useState<boolean>(false)

  useEffect(() => {
    if (logs?.length) {
      setOpen(true)
    }
  }, [logs])

  return (
    <Modal
      open={open}
      onCancel={() => setOpen(false)}
      afterOpenChange={(o) => setOpen(o)}
      title={getWord('log')}
      footer={false}
    >
      {logs?.map((item: SearchLog, index) => (
        <Row key={`${item?.event_time}-${index}`}>
          <Col span={8}>{formatYTSTime(item?.event_time as number)}</Col>
          <Col span={16}>{item?.event_msg}</Col>
        </Row>
      ))}
    </Modal>
  )
}

export default RetroLogModel
