@import '@/style/variables.less';
.searchHsitoryCardBody {
  position: relative;
  overflow-x: unset !important;
  overflow-y: unset !important;

  .redDot {
    position: absolute;
    top: -6px;
    right: 0px;
  }

  .searchHsitoryCard {
    width: 200px;
    min-width: 200px;
    margin-top: 12px;
    color: #9b9ba1 !important;
    font-size: 11px;
    .commonTag {
      display: flex;
      align-items: center;
      width: max-content;
      min-width: 32px;
      max-width: 78px;
      height: 24px;
      padding: 0 4px;
      font-size: 12px;
    }
    .statusDes {
      &_completed {
        color: @color-completed;
        background-color: #ecf8ef;
        border: 1px solid @color-completed;
      }
      &_running {
        color: @color-running;
        border: 1px solid @color-running;
      }
      &_limited {
        color: @color-pending;
        border: 1px solid @color-pending;
      }
      &_pending {
        color: @color-pending;
        border: 1px solid @color-pending;
      }
      &_failed {
        color: @color-failed;
        border: 1px solid @color-failed;
      }
    }
    .moreSvg {
      width: 24px;
      height: 24px;
      svg {
        width: 15px;
        fill: @color-text-gray;
      }
    }
    .moreSvg:hover {
      background-color: @fill-choosed;
      svg {
        fill: @text-choosed !important;
      }
    }
    &_choosed {
      background: #eef7ff;
      border: 1px solid #449ae1;
    }
    :global {
      .ant-card-body {
        padding: 6px 4px 4px 6px;
      }
    }
    .icon {
      width: 30px;
      height: 30px;
    }
    .icon:hover {
      cursor: pointer !important;
    }

    .label {
      width: auto;
      min-width: 40px;
      max-width: 80px;
    }
    .time {
      margin-left: 8px;
      color: black;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }

  .searchHsitoryCard:hover {
    cursor: pointer;
  }
}

.searchPopover {
  :global {
    .ant-popover-inner {
      padding: 0px !important;
    }
    .ant-popover-arrow {
      display: none !important;
    }
    .ant-popover .ant-popover-inner {
      padding: 0px !important;
    }
  }
}

.searchButton {
  padding: 0px;
  border: none !important;
  border-radius: 0px !important;
}

.searchButton:hover {
  color: @text-choosed !important;
  background-color: @fill-choosed !important;
}
