import { ReactComponent as MoreSvg } from '@/assets/svgs/More.svg'
import useOptions from '@/hooks/useOptions'
import { formatYTSTime, getWord, isValidArray } from '@/utils'
import { useModel } from '@umijs/max'
import { Badge, Button, Card, Popover, message } from 'antd'
import cs from 'classnames'
import { isArray, size } from 'lodash'
import type { SearchHsitoryCardProps } from './index.d'
import styles from './index.less'
export default function SearchHsitoryCard({
  curData,
  openAiGenerateInfo,
  getAiGenerateRoute,
  searchLog
}: SearchHsitoryCardProps) {
  const { aiGenerateStauts } = useOptions()
  const { curHistoryInfo } = useModel('compound')
  const isCompleted: boolean = curData?.status === 'completed'

  const ItemInfo = (props: { label: string; value: string | number }) => (
    <div
      className="flex-justify-space-between flex-align-items-center"
      style={{ height: '24px' }}
    >
      <div className={cs('display-flex flex-align-items-center')}>
        <div className={styles.label}>{props?.label}</div>
        <div className={styles.time}>{props?.value}</div>
      </div>
    </div>
  )

  return (
    <div className={styles.searchHsitoryCardBody}>
      <Card
        className={cs(styles.searchHsitoryCard, {
          [styles['searchHsitoryCard_choosed']]:
            curHistoryInfo?.id === curData?.id
        })}
        onClick={getAiGenerateRoute}
      >
        <div
          className={cs(
            'flex-justify-space-between',
            'flex-align-items-center'
          )}
          style={{ height: '24px' }}
        >
          <div className="display-flex flex-align-items-center">
            <div className={styles.label}>{getWord('owner')}</div>
            <div className={styles.time}>
              {curData?.creator_id
                ? curData?.creator_id.split('@')[0]
                : curData?.creator_id}
            </div>
          </div>
          <div className={cs('flex-center')}>
            <span
              className={cs(
                styles.statusDes,
                styles.commonTag,
                styles[`statusDes_${curData?.status}`]
              )}
            >
              {aiGenerateStauts[curData?.status]}
            </span>
            <Popover
              placement="bottomLeft"
              align={{
                offset: [-2, 5]
              }}
              overlayClassName={styles.searchPopover}
              content={
                <>
                  <Button
                    className={styles.searchButton}
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation()
                      openAiGenerateInfo()
                    }}
                  >
                    {getWord('parameters')}
                  </Button>
                  <br />
                  <Button
                    className={styles.searchButton}
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation()
                      if (!isValidArray(curData?.search_log))
                        return message.error(getWord('no-search-log'))
                      searchLog()
                    }}
                    style={{ width: '100%' }}
                  >
                    {getWord('log')}
                  </Button>
                </>
              }
            >
              <div className={cs(styles.moreSvg, 'flex-center')}>
                <MoreSvg />
              </div>
            </Popover>
          </div>
        </div>
        <ItemInfo
          label={getWord('number-of-routes')}
          value={
            ['completed', 'running'].includes(curData?.status) &&
            isArray(curData?.retro_backbones)
              ? String(curData?.count || size(curData?.retro_backbones))
              : '0'
          }
        />
        <ItemInfo
          label={getWord('creation-time')}
          value={formatYTSTime(curData?.createdAt)}
        />
        {['limited', 'pending'].includes(curData?.status) &&
        curData?.predict_start_time ? (
          <>
            <ItemInfo
              label={getWord('estimate-start')}
              value={formatYTSTime(curData?.predict_start_time)}
            />
            {curData?.status === 'pending' ? (
              <ItemInfo
                label={getWord('tasks-in-queue')}
                value={curData?.queue_count}
              />
            ) : (
              ''
            )}
          </>
        ) : (
          curData?.search_start_time && (
            <ItemInfo
              label={getWord(
                'pages.searchTable.updateForm.schedulingPeriod.timeLabel'
              )}
              value={formatYTSTime(curData?.search_start_time)}
            />
          )
        )}
        {!isCompleted || !curData?.search_end_time ? (
          ''
        ) : (
          <ItemInfo
            label={getWord('complete-time')}
            value={formatYTSTime(curData?.search_end_time)}
          />
        )}
      </Card>
      {(curData?.offset as number) > 0 && curData?.newMsgDot ? (
        <Badge
          size="small"
          count={`+${curData?.offset}`}
          className={cs(styles.redDot)}
        />
      ) : (
        ''
      )}
    </div>
  )
}
