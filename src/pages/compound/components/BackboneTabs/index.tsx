import { ProjectCompoundType } from '@/services/brain'
import { RouteType } from '@/types/common'
import { getWord } from '@/utils'
import { useAccess } from '@umijs/max'
import { Card } from 'antd'
import { CardTabListType } from 'antd/es/card'
import React, { useEffect } from 'react'
import { useProjectCompound } from '../CompoundInfo/useCompoundInfo'
import AiHeadExtra from './HeadButtons/AiHeadExtra'
import CreateMyRouteButton from './HeadButtons/CreateMyRouteButton'
import styles from './index.less'
import ProjectRoute from './ProjectRoute'
import RetroRoute from './RetroRoute'
import { useBackboneTabsStore } from './store'

const tabsForCompoundType: Record<ProjectCompoundType, RouteType[]> = {
  building_block: ['aiGenerated'],
  target: ['aiGenerated', 'myRoutes'],
  temp_block: ['aiGenerated']
}

export interface BackboneTabsProps {
  compoundId?: string
}

const BackboneTabs: React.FC<BackboneTabsProps> = ({ compoundId }) => {
  const access = useAccess()
  const { tabType, setTabType, counts, setCount } = useBackboneTabsStore()
  const { data } = useProjectCompound(compoundId)

  useEffect(() => {
    setCount('myRoutes', data?.project_routes?.length || 0)
  }, [data?.project_routes?.length])

  const tabs: CardTabListType[] = tabsForCompoundType[data?.type || 'target']
    .filter((tab) => access?.authCodeList?.includes(`compound.tab.${tab}`))
    .map((tab) => ({
      key: tab,
      label: !!counts[tab] ? `${getWord(tab)} (${counts[tab]})` : getWord(tab)
    }))

  const extra =
    tabType === 'myRoutes' ? (
      <CreateMyRouteButton compoundId={compoundId} />
    ) : tabType === 'aiGenerated' ? (
      <AiHeadExtra compoundId={compoundId} />
    ) : null

  return (
    <Card
      className={styles.root}
      tabList={tabs}
      size="small"
      activeTabKey={tabType}
      onTabChange={(key) => setTabType(key as RouteType)}
      tabProps={{ size: 'small' }}
      tabBarExtraContent={extra}
    >
      {tabType === 'myRoutes' ? (
        <ProjectRoute compoundId={compoundId} />
      ) : (
        <RetroRoute compoundId={compoundId} />
      )}
    </Card>
  )
}

export default BackboneTabs
