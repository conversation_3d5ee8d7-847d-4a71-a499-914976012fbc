import {
  query,
  RetroBackbone,
  RetroParamsConfig,
  StrapiPagination
} from '@/services/brain'
import { calcScoreOfRetroRoute } from '@/utils/calcScoreOfRetroRoute'
import { useQuery } from '@tanstack/react-query'
import { isEmpty } from 'lodash'

export interface FrontRetroBackbone extends RetroBackbone {
  collected: boolean
  originScore: number
  score: number
}
interface FilterParams {
  group?: 'start_material' | 'cluster'
  groupSimilarId?: number
  page?: number
  pageSize?: number
  preference?: RetroParamsConfig & { plus_process_feasibility_score?: boolean }
}
interface HookParam {
  backboneIds?: number[]
  filters?: FilterParams
}

const backboneFields: (keyof RetroBackbone)[] = [
  'no',
  'score',
  'backbone',
  'main_trees',
  'group_conditions',
  'group_info',
  'known_reaction_rate',
  'min_n_main_tree_steps',
  'createdAt',
  'updatedAt',
  'safety_score',
  'novelty_score',
  'price_score',
  'collected_retro_backbones',
  'process_feasibility_score'
]

const fetchByFilter = async (
  retroProcessId: number,
  params: FilterParams = {}
): Promise<{ data: FrontRetroBackbone[]; pagination?: StrapiPagination }> => {
  const {
    page = 1,
    pageSize = 10,
    group = 'start_material',
    groupSimilarId,
    preference
  } = params

  const request = query<RetroBackbone>(
    `retro-backbones?comment=true`,
    { params: { group, preference } },
    backboneFields
  )
    .filterDeep('retro_process.id', 'eq', retroProcessId)
    .populateWith('collected_retro_backbones', ['id', 'user_id'])
    .paginate(page, pageSize)
  if (groupSimilarId !== undefined) {
    request.filterDeep('group_conditions.start_material', 'eq', groupSimilarId)
  }

  const { data, error, meta } = await request.get()

  if (!error && data) {
    const routes: FrontRetroBackbone[] = data.map((d) => ({
      ...d,
      collected: !isEmpty(d?.collected_retro_backbones),
      originScore: d.score || 0,
      score: calcScoreOfRetroRoute(d, preference || {}) * 100
    }))
    return { data: routes, pagination: meta?.pagination }
  }
  throw new Error('Network response was not ok')
}

const fetchByIds = async (
  retroProcessId: number,
  retroBackboneIds: number[]
): Promise<{ data: FrontRetroBackbone[]; pagination?: StrapiPagination }> => {
  const { data, error, meta } = await query<RetroBackbone>(
    `retro-backbones?comment=true`,
    undefined,
    backboneFields
  )
    .filterDeep('retro_process.id', 'eq', retroProcessId)
    .populateWith('collected_retro_backbones', ['id', 'user_id'])
    .filterDeep('id', 'in', retroBackboneIds)
    .get()

  if (!error && data) {
    const routes: FrontRetroBackbone[] = data.map((d) => ({
      ...d,
      collected: !isEmpty(d?.collected_retro_backbones),
      originScore: d.score || 0,
      score:
        calcScoreOfRetroRoute(d, {
          safety_score: 2,
          novelty_score: -2,
          price_score: 2
        }) * 100
    }))
    return { data: routes, pagination: meta?.pagination }
  }
  throw new Error('Network response was not ok')
}

const useRetroRouteByFilter = (
  id: number | string | undefined,
  filters?: FilterParams
) => {
  const idNum = typeof id !== 'number' ? Number.parseInt(id || '') : id
  const { data, error, isLoading, isFetching, refetch } = useQuery({
    queryKey: ['retro-backbones', id, JSON.stringify(filters)],
    queryFn: () => (id && filters ? fetchByFilter(idNum, filters) : {}),
    enabled: !isNaN(idNum),
    keepPreviousData: true
  })

  if (isNaN(idNum)) return {}
  return { data, error, isLoading, isFetching, refetch }
}

const useRetroRouteByIds = (
  id: number | string | undefined,
  backboneIds?: number[]
) => {
  const idNum = typeof id !== 'number' ? Number.parseInt(id || '') : id
  const { data, error, isLoading, isFetching, refetch } = useQuery({
    queryKey: ['retro-backbones', id, backboneIds?.join(',')],
    queryFn: () =>
      id && backboneIds?.length ? fetchByIds(idNum, backboneIds) : {},
    enabled: !isNaN(idNum),
    keepPreviousData: true
  })

  if (isNaN(idNum)) return {}
  return { data, error, isLoading, isFetching, refetch }
}

export const useRetroRoute = (
  id: number | string | undefined,
  { backboneIds, filters }: HookParam
): {
  data?: { data?: FrontRetroBackbone[]; pagination?: StrapiPagination }
  error?: unknown
  isLoading?: boolean
  isFetching?: boolean
  refetch?: () => void
} => {
  const idNum = typeof id !== 'number' ? Number.parseInt(id || '') : id
  const filterHookResult = useRetroRouteByFilter(id, filters)
  const idHookResult = useRetroRouteByIds(id, backboneIds)

  if (isNaN(idNum)) return {}
  if (backboneIds) return idHookResult
  return filterHookResult
}
