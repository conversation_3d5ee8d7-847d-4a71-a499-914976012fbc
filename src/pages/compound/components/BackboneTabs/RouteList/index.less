.cardRoot {
  margin: 4px 0;
  border-radius: 4px;
  :global {
    .ant-card-body {
      padding: 0 8px;
      overflow-x: auto;
      overflow-y: hidden;
    }
  }

  .root {
    display: flex;
    flex-direction: row;
    width: fit-content;
    padding: 8px 0;

    .reactionWrapper {
      position: relative;

      &.arrowWrapper {
        display: flex;
        align-items: center;
        padding: 0 20px;
      }
      .reactionBtns {
        position: absolute;
        top: calc(50% - 20px);
        right: 0;
        left: 0;
        display: flex;
        justify-content: center;
        margin-right: auto;
        margin-left: auto;

        .reactionBtn {
          width: 16px;
          height: 16px;
          cursor: pointer;

          svg > path {
            fill: #bfbfbf;
          }
          &:hover {
            svg > path {
              fill: #1a90ff;
            }
          }
        }
      }
    }

    .structureWrapper {
      position: relative;
      flex: 0 0 auto;
      width: 184px;
      height: 184px;
      border: 2px solid #6bd3fd;
      &:first-child {
        border-color: #fecf7c;
      }
      &:last-child:not(:first-child) {
        border-color: #979797;
        border-style: dashed;
      }
      .structureInfo {
        position: absolute;
        top: 0;
        left: 4px;
        font-weight: 300;
      }
      .filterInfo {
        --width: 36px;
        position: absolute;
        top: -3px;
        right: calc(-1 * var(--width) - 2px);
        z-index: 1;
        width: var(--width);
        height: 24px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-size: small;
        line-height: 12px;
        & > button {
          width: 100%;
          margin: auto;
          padding: 0;
          border-left: none;
        }
      }
    }
  }
}
