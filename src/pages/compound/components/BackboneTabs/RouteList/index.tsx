import { ProjectRoute, RetroBackbone } from '@/services/brain'
import { Card } from 'antd'
import React from 'react'
import styles from './index.less'
import MainChain from './MainChain'
import Actions from './RouteHead/Actions'
import Title from './RouteHead/Title'
import { routeToLink } from './utils'

export interface RouteListProps {
  routes: ProjectRoute[] | RetroBackbone[]
  refetch: () => void
  tempRoute?: boolean
}

const RouteList: React.FC<RouteListProps> = ({
  routes,
  refetch,
  tempRoute
}) => {
  return (
    <div className={styles.routesList}>
      {routes.map((r) => (
        <Card
          size="small"
          key={r.id}
          className={styles.cardRoot}
          title={<Title route={r} reload={refetch} tempRoute={tempRoute} />}
          extra={<Actions route={r} reload={refetch} tempRoute={tempRoute} />}
        >
          <MainChain node={routeToLink(r)} withWrapper />
        </Card>
      ))}
    </div>
  )
}

export default RouteList
