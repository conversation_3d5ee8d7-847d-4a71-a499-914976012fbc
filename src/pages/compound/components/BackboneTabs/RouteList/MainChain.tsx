import { ReactComponent as CopyMaterialIcon } from '@/assets/svgs/route-operation/copy-material.svg'
import Arrow from '@/components/Arrow'
import RemoteMoleculeStructure from '@/components/MoleculeStructure'
import { useCopyToClipboard } from '@/components/MoleculeStructure/util'
import { SyntheticLink } from '@/types/SyntheticRoute/SyntheticLink'
import { PushpinOutlined, RightOutlined } from '@ant-design/icons'
import { Button } from 'antd'
import React, { useContext } from 'react'
import { FilterBackboneIdsContext } from '../filterBackboneIdsContext'
import styles from './index.less'

const calDepth = (node: SyntheticLink): number => {
  if (!node.child) return 0
  return calDepth(node.child) + 1
}

export interface MainChainProps {
  node: SyntheticLink
  withWrapper?: boolean
}

const MainChain: React.FC<MainChainProps> = ({ node, withWrapper }) => {
  const { copy } = useCopyToClipboard()
  const { hook: { select, unselect, getNode, isSelected } = {}, showFilter } =
    useContext(FilterBackboneIdsContext) || {}
  const { value, child, rxn, path = [] } = node
  const selected = isSelected?.(path)

  const content = (
    <>
      <div className={styles.structureWrapper}>
        <RemoteMoleculeStructure
          structure={value}
          className={styles.structure}
        />
        <div className={styles.structureInfo}>m{calDepth(node) + 1}</div>
        {showFilter && node.path && node.path.length > 1 && node.child && (
          <div
            className={styles.filterInfo}
            onClick={() => {
              if (selected) unselect?.(path)
              else select?.(path)
            }}
          >
            <Button
              size="small"
              color={selected ? 'primary' : 'default'}
              variant={selected ? 'filled' : 'outlined'}
            >
              {selected ? (
                <PushpinOutlined />
              ) : (
                <>
                  {getNode?.(path)?.children.length}
                  <RightOutlined />
                </>
              )}
            </Button>
          </div>
        )}
      </div>
      {child && (
        <>
          <div
            title={rxn}
            className={`${styles.reactionWrapper} ${styles.arrowWrapper}`}
          >
            <Arrow />
            <div className={styles.reactionBtns}>
              <div
                className={`${styles.reactionCopyBtn} ${styles.reactionBtn}`}
                onClick={() => copy(`${child?.value}>>${value}`)}
              >
                <CopyMaterialIcon />
              </div>
            </div>
          </div>
          <MainChain node={child} />
        </>
      )}
    </>
  )

  if (withWrapper) return <div className={styles.root}>{content}</div>
  return content
}

export default MainChain
