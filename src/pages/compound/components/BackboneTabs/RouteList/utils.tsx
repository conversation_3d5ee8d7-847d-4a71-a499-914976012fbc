import GeneratingImg from '@/assets/svgs/lottie/generating.gif'
import { ReactComponent as GeneratingRouteTipIcon } from '@/assets/svgs/systems/generatingRouteTip.svg'
import { ReactComponent as QueuingIcon } from '@/assets/svgs/systems/queuing.svg'
import { ReactComponent as SearchFailedIcon } from '@/assets/svgs/systems/searchFailed.svg'
import { ProjectRoute, RetroBackbone } from '@/services/brain'
import {
  backboneToLink,
  SyntheticLink,
  syntheticTreeToLink
} from '@/types/SyntheticRoute/SyntheticLink'
import { getWord } from '@/utils'
import { history } from 'umi'

export type RetroRouteStatus =
  | 'default'
  | 'queueing'
  | 'failed'
  | 'generating'
  | 'empty'
  | 'empty-with-filter'

export const getRetroRouteImage = (status: RetroRouteStatus) => {
  switch (status) {
    case undefined:
    case 'default':
      return null
    case 'failed':
      return <SearchFailedIcon />
    case 'queueing':
      return <QueuingIcon />
    case 'generating':
      return <img src={GeneratingImg} />
    default:
      return <GeneratingRouteTipIcon />
  }
}

export const getRetroRouteTip = (
  status: RetroRouteStatus,
  openAiGenerateModel?: () => void
) => {
  switch (status) {
    case undefined:
    case 'default':
      return getWord('no-routes-returned')
    case 'failed':
      return getWord('search-failed')
    case 'queueing':
      return getWord('wait-tip')
    case 'generating':
      return getWord('generating-tip')
    case 'empty':
      return (
        <>
          {getWord('no-AI-returned-create-I')}
          <a onClick={openAiGenerateModel}>【{getWord('gnerate-routes')}】</a>
          {getWord('no-AI-returned-create-II')}
        </>
      )
    case 'empty-with-filter':
    default:
      return getWord('no-AI-returned')
  }
}

export const getProjectRouteTip = (
  projectId: number | string,
  compoundId: number | string
) => {
  return (
    <span>
      {getWord('no-routes-create-tip')}
      <a
        onClick={() =>
          history.push(`/projects/${projectId}/compound/${compoundId}/create`)
        }
      >
        【{getWord('new-route')}】
      </a>
      {getWord('add-routes')}
    </span>
  )
}

export const routeToLink = (
  route: ProjectRoute | RetroBackbone
): SyntheticLink => {
  if ('backbone' in route) {
    return backboneToLink(route.backbone)
  }
  return syntheticTreeToLink(route.main_tree)
}
