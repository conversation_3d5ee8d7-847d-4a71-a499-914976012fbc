import { RetroBackbone, service } from '@/services/brain'
import { getWord } from '@/utils'
import { StarFilled, StarOutlined } from '@ant-design/icons'
import { useAccess, useModel } from '@umijs/max'
import { App } from 'antd'
import React from 'react'
import Base from './Base'

export interface CollectProps {
  route: RetroBackbone
  reload: () => void
  tempRoute?: boolean
}

const Collect: React.FC<CollectProps> = ({ route, tempRoute, reload }) => {
  const access = useAccess()
  const { message } = App.useApp()
  const { initialState } = useModel('@@initialState')
  const userId = initialState?.userInfo?.id

  if (!access?.authCodeList?.includes('compound.button.collect')) return null

  const collects = route.collected_retro_backbones
  const collectId = collects?.find((b) => b.user_id === String(userId))?.id
  const collected = typeof collectId === 'number'

  const handleCollected = async () => {
    if (tempRoute) return

    const { error } = collected
      ? await service('collected-retro-backbones').deleteOne(collectId)
      : await service('collected-retro-backbones').create({
          user_id: String(userId),
          retro_backbone: route.id
        })

    if (!error) {
      reload()
      message.success(getWord('operate-success'))
    }
  }

  const text = getWord(collected ? 'unfavorite' : 'favorite')
  const icon = collected ? <StarFilled /> : <StarOutlined />

  return (
    <Base
      icon={icon}
      text={text}
      disabled={tempRoute}
      onClick={handleCollected}
    />
  )
}

export default Collect
