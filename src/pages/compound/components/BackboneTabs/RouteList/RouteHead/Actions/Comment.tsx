import { ProjectRoute, RetroBackbone } from '@/services/brain'
import { getWord } from '@/utils'
import { MessageOutlined } from '@ant-design/icons'
import { useAccess, useModel } from '@umijs/max'
import React from 'react'
import Base from './Base'

export interface CommentProps {
  route: ProjectRoute | RetroBackbone
  tempRoute?: boolean
}

const Comment: React.FC<CommentProps> = ({ route, tempRoute }) => {
  const access = useAccess()
  const { getProfileInfo } = useModel('commend')
  if (!access?.authCodeList?.includes('compound.button.feedback')) return null

  const countText = route.content_count ? `(${route.content_count})` : ''
  const text = `${getWord('comment')}${countText}`

  return (
    <Base
      icon={<MessageOutlined />}
      text={text}
      disabled={tempRoute}
      onClick={() => {
        getProfileInfo({
          _commendSuject: route,
          collection_class: 'project-route'
        })
      }}
    />
  )
}

export default Comment
