import { useBrainFetch } from '@/hooks/useBrainFetch'
import { useProjectCompound } from '@/pages/compound/components/CompoundInfo/useCompoundInfo'
import { useSwitchStore } from '@/pages/compound/store'
import { ProjectCompound, ProjectRoute, service } from '@/services/brain'
import { getWord } from '@/utils'
import { ExperimentFilled, ExperimentOutlined } from '@ant-design/icons'
import { message } from 'antd'
import React from 'react'
import Base from './Base'

export interface SetDefaultProps {
  route: ProjectRoute
  reload: () => void
}

const SetDefault: React.FC<SetDefaultProps> = ({ route, reload }) => {
  const { fetch } = useBrainFetch()
  const { compoundId } = useSwitchStore()
  const { defaultRouteId, refetch } = useProjectCompound(compoundId)
  const isDefault = route?.id === defaultRouteId && !!defaultRouteId

  const handleSetAsDefault = async () => {
    if (!route?.id || !compoundId || isDefault) return
    const { data } = await fetch(
      service<ProjectCompound>('project-compounds').update(compoundId, {
        default_route: route.id as unknown as ProjectRoute
      })
    )
    if (data) {
      message.success(getWord('default-route-set'))
      refetch?.()
      reload()
    }
  }

  const icon = isDefault ? <ExperimentFilled /> : <ExperimentOutlined />
  const text = getWord(isDefault ? 'default-route' : 'set-default-route')

  return <Base icon={icon} text={text} onClick={handleSetAsDefault} />
}

export default SetDefault
