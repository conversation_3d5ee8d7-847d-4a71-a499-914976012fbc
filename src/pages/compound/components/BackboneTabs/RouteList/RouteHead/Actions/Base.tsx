import { getWord } from '@/utils'
import { Button, ButtonProps, Popover } from 'antd'
import React from 'react'
import styles from './index.less'

export interface BaseProps {
  icon: React.JSX.Element
  text: string
  onClick?: () => void | Promise<void>
  disabled?: boolean
  disabledText?: string
  style?: React.CSSProperties
}

const Base: React.FC<BaseProps> = ({
  icon,
  text,
  onClick,
  disabled,
  style,
  disabledText = getWord('temporary-route-tip')
}) => {
  const buttonProps: ButtonProps = {
    type: 'link',
    size: 'small',
    icon,
    disabled,
    onClick,
    title: disabled ? disabledText : undefined,
    style
  }
  return (
    <>
      <Popover content={text} className={styles.small}>
        <Button {...buttonProps} />
      </Popover>
      <Button {...buttonProps} className={styles.big}>
        {text}
      </Button>
    </>
  )
}

export default Base
