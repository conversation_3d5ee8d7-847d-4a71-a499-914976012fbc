import { ProjectRoute, RetroBackbone } from '@/services/brain'
import React from 'react'
import Collect from './Collect'
import Comment from './Comment'
import styles from './index.less'
import SetDefault from './SetDefault'
import Status from './Status'
import View from './View'

export interface ActionsProps {
  route: ProjectRoute | RetroBackbone
  reload: () => void
  tempRoute?: boolean
}

const Actions: React.FC<ActionsProps> = ({ route, reload, tempRoute }) => {
  if ('backbone' in route) {
    return (
      <div className={styles.root}>
        <View route={route} />
        <Collect route={route} reload={reload} tempRoute={tempRoute} />
        <Comment route={route} tempRoute={tempRoute} />
      </div>
    )
  }

  return (
    <div className={styles.root}>
      <Status route={route} reload={reload} />
      <View route={route} />
      {route.status === 'confirmed' && (
        <SetDefault route={route} reload={reload} />
      )}
      <Comment route={route} />
    </div>
  )
}

export default Actions
