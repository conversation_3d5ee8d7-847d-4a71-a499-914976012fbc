import { useProjectCompound } from '@/pages/compound/components/CompoundInfo/useCompoundInfo'
import { useSwitchStore } from '@/pages/compound/store'
import { ProjectRoute, RetroBackbone } from '@/services/brain'
import { getWord } from '@/utils'
import { get, set } from '@/utils/storage'
import { EyeOutlined } from '@ant-design/icons'
import { history, useAccess } from '@umijs/max'
import React from 'react'
import Base from './Base'

const viewedRouteKey = 'viewedRoute'

export interface ViewProps {
  route: ProjectRoute | RetroBackbone
}

const View: React.FC<ViewProps> = ({ route }) => {
  const access = useAccess()
  const { compoundId } = useSwitchStore()
  const { projectId } = useProjectCompound(compoundId)

  const type = 'backbone' in route ? 'retro' : 'project'
  const storageKey = `${viewedRouteKey}-${type}`
  const viewedHistory = get(storageKey) as number[]
  const viewed = viewedHistory?.includes(route.id)
  if (!access?.authCodeList?.includes('compound.button.view')) return null

  const handleView = () => {
    set(storageKey, [...(viewedHistory || []), route.id])
    if ('backbone' in route) {
      history.push(
        `/projects/${projectId}/compound/${compoundId}/view-by-backbone/${route.id}`
      )
    } else {
      history.push(
        `/projects/${projectId}/compound/${compoundId}/${
          route.status === 'editing' ? 'edit' : 'view'
        }/${route.id}`
      )
    }
  }

  const text = getWord('pages.projectTable.actionLabel.viewDetail')

  return (
    <Base
      icon={<EyeOutlined />}
      text={text}
      onClick={handleView}
      style={viewed ? { color: '#a52ad2' } : {}}
    />
  )
}

export default View
