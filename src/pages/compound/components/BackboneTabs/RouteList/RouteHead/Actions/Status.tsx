import StatusRender from '@/components/StatusRender'
import { useBrainFetch } from '@/hooks/useBrainFetch'
import EnumSwitcher from '@/pages/projects/components/EnumSwitcher'
import { ProjectRoute, ProjectRouteStatus, service } from '@/services/brain'
import { getWord } from '@/utils'
import { App } from 'antd'
import React from 'react'

const projectStatusTransferMap: Record<
  ProjectRouteStatus,
  ProjectRouteStatus[]
> = {
  canceled: [],
  confirmed: ['canceled'],
  editing: ['canceled'],
  finished: []
}

export interface StatusProps {
  route: ProjectRoute
  reload: () => void
}

const Status: React.FC<StatusProps> = ({ route: { status, id }, reload }) => {
  const { fetch } = useBrainFetch()
  const { message } = App.useApp()
  const availableStatus = projectStatusTransferMap[status] || []
  const handleChangeStatus = async (status: ProjectRouteStatus) => {
    if (!id) return
    const { data } = await fetch(
      service<ProjectRoute>('project-routes').update(id, { status })
    )
    if (data) {
      message.success(getWord('success-update-status'))
      reload()
    }
  }

  return availableStatus.length ? (
    <EnumSwitcher
      currentValue={status}
      avalibleValues={availableStatus}
      onSelect={handleChangeStatus}
      valueRender={(s) => (
        <StatusRender labelPrefix="pages.route.statusLabel" status={s} />
      )}
    />
  ) : (
    <StatusRender labelPrefix="pages.route.statusLabel" status={status} />
  )
}

export default Status
