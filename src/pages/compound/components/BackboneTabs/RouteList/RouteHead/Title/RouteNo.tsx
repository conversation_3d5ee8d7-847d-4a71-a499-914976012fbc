import { ProjectRoute, RetroBackbone } from '@/services/brain'
import { getWord } from '@/utils'
import React from 'react'
import styles from './index.less'

export interface RouteNoProps {
  route: ProjectRoute | RetroBackbone
}

const RouteNo: React.FC<RouteNoProps> = ({ route }) => {
  return (
    <div className={styles.wrapper}>
      {getWord('route-id')}: {route.id}
    </div>
  )
}

export default RouteNo
