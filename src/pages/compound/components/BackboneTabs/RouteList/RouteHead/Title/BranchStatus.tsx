import { ProjectRoute, RetroBackbone } from '@/services/brain'
import { calHasBranch } from '@/types/SyntheticRoute/SyntheticTree'
import { getWord, isEN } from '@/utils'
import { Tag } from 'antd'
import React from 'react'
import styles from './index.less'

export interface BranchStatusProps {
  route: ProjectRoute | RetroBackbone
}

const BranchStatus: React.FC<BranchStatusProps> = ({ route }) => {
  const mainTrees = 'backbone' in route ? route.main_trees : [route.main_tree]
  const treeCountHasBranch = mainTrees
    .map(calHasBranch)
    .reduce((acc, cur) => (cur ? acc + 1 : acc), 0)

  return (
    <Tag className={styles.wrapper} color="blue">
      {treeCountHasBranch > 0 ? getWord('has') : getWord('No')}
      {isEN() ? ' ' : ''}
      {treeCountHasBranch > 1 ? getWord('multiple') : ''}
      {isEN() ? ' ' : ''}
      {getWord('branched-chain')}
    </Tag>
  )
}

export default BranchStatus
