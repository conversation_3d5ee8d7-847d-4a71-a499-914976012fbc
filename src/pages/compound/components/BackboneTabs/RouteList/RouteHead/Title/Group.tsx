import { RetroBackbone } from '@/services/brain'
import { getWord } from '@/utils'
import { Button, Popover } from 'antd'
import React from 'react'
import { useBackboneTabsStore } from '../../../store'
import styles from './index.less'

export interface GroupProps {
  route: RetroBackbone
  tempRoute?: boolean
}

const Group: React.FC<GroupProps> = ({
  route: { group_conditions },
  tempRoute
}) => {
  const { retroRouteFilters, setGroupSimilarId } = useBackboneTabsStore()
  const groupId = group_conditions[retroRouteFilters.group]

  return (
    <div className={styles.wrapper}>
      <Popover content={getWord('filter-routes-group')}>
        <Button
          size="small"
          type="primary"
          disabled={tempRoute}
          onClick={() => setGroupSimilarId(groupId)}
        >
          {getWord('new-group')} {groupId + 1}
        </Button>
      </Popover>
    </div>
  )
}

export default Group
