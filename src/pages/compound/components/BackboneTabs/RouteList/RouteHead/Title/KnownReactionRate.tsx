import { RetroBackbone } from '@/services/brain'
import { getWord } from '@/utils'
import React from 'react'
import styles from './index.less'

export interface KnownReactionRateProps {
  route: RetroBackbone
}

const KnownReactionRate: React.FC<KnownReactionRateProps> = ({
  route: { known_reaction_rate }
}) => {
  if (known_reaction_rate === undefined) return null
  return (
    <div className={styles.wrapper}>
      {getWord('known-reaction-proportion')} {known_reaction_rate}%
    </div>
  )
}

export default KnownReactionRate
