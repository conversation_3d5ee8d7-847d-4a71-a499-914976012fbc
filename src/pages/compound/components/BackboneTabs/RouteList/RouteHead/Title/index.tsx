import { useBackboneTabsStore } from '@/pages/compound/components/BackboneTabs/store'
import { ProjectRoute, RetroBackbone } from '@/services/brain'
import { Divider } from 'antd'
import React from 'react'
import BranchStatus from './BranchStatus'
import Group from './Group'
import styles from './index.less'
import KnownReactionRate from './KnownReactionRate'
import ProcessFeasibilityScore from './ProcessFeasibilityScore'
import RouteLongestChain from './RouteLongestChain'
import RouteName from './RouteName'
import RouteNo from './RouteNo'
import RouteScore from './RouteScore'
import RouteStepCount from './RouteStepCount'
import UpdateTime from './UpdateTime'

export interface TitleProps {
  route: ProjectRoute | RetroBackbone
  reload: () => void
  tempRoute?: boolean
}

const Title: React.FC<TitleProps> = ({ route, tempRoute }) => {
  const { retroLayout } = useBackboneTabsStore()
  if ('backbone' in route) {
    return (
      <div className={styles.root}>
        {retroLayout === 'group' && (
          <Group route={route} tempRoute={tempRoute} />
        )}
        <RouteNo route={route} />

        <Divider type="vertical" />
        <RouteStepCount route={route} />

        <Divider type="vertical" />
        <RouteLongestChain route={route} />

        <Divider type="vertical" />
        <BranchStatus route={route} />

        {retroLayout !== 'filter' && (
          <>
            <Divider type="vertical" />
            <RouteScore route={route} tempRoute={tempRoute} />
          </>
        )}

        <Divider type="vertical" />
        <KnownReactionRate route={route} />

        {retroLayout === 'feasibility' && (
          <>
            <Divider type="vertical" />
            <ProcessFeasibilityScore route={route} />
          </>
        )}
      </div>
    )
  }
  return (
    <div className={styles.root}>
      <RouteNo route={route} />

      <Divider type="vertical" />
      <RouteName route={route} />

      <Divider type="vertical" />
      <RouteStepCount route={route} />

      <Divider type="vertical" />
      <RouteLongestChain route={route} />

      <Divider type="vertical" />
      <BranchStatus route={route} />

      <Divider type="vertical" />
      <UpdateTime route={route} />
    </div>
  )
}

export default Title
