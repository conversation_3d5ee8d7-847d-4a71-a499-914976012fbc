import { RetroBackbone } from '@/services/brain'
import { getWord, roundToOneDecimalPlaces } from '@/utils'
import { InfoCircleOutlined } from '@ant-design/icons'
import { Divider, Popover } from 'antd'
import React from 'react'
import styles from './index.less'

export interface RouteScoreProps {
  route: RetroBackbone
  tempRoute?: boolean
}

const RouteScore: React.FC<RouteScoreProps> = ({
  route: {
    score = 100,
    originScore = 1,
    price_score = 1,
    novelty_score = 1,
    safety_score = 1
  },
  tempRoute
}) => {
  return (
    <div className={styles.wrapper}>
      {getWord('algorithmic-score')} {Math.round(score * 100) / 100}
      {!tempRoute && (
        <Popover
          content={
            <>
              <p>
                {getWord('route-novelty-score')}:
                {roundToOneDecimalPlaces(novelty_score * 100)}
                /100
              </p>
              <p>
                {getWord('route-price-score')}:
                {roundToOneDecimalPlaces(price_score * 100)}
                /100
              </p>
              <p>
                {getWord('route-safety-score')}:
                {roundToOneDecimalPlaces(safety_score * 100)}
                /100
              </p>
              <Divider style={{ margin: '8px 0' }} />
              <p>
                {getWord('route-base-score')}:
                {roundToOneDecimalPlaces(originScore)}
                /100
              </p>
            </>
          }
        >
          <InfoCircleOutlined className={styles.icon} />
        </Popover>
      )}
    </div>
  )
}

export default RouteScore
