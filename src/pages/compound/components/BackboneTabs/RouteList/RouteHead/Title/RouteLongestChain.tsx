import { ProjectRoute, RetroBackbone } from '@/services/brain'
import { calSyntheticStep } from '@/types/SyntheticRoute/SyntheticTree'
import { getWord } from '@/utils'
import React from 'react'
import styles from './index.less'

export interface RouteLongestChainProps {
  route: ProjectRoute | RetroBackbone
}

const RouteLongestChain: React.FC<RouteLongestChainProps> = ({ route }) => {
  const longestStep =
    'backbone' in route
      ? route.backbone.length - 1
      : calSyntheticStep(route.main_tree)
  return (
    <div className={styles.wrapper}>
      {getWord('longest-chain-l')}
      {longestStep}
    </div>
  )
}

export default RouteLongestChain
