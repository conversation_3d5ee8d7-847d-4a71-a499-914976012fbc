import { ProjectRoute } from '@/services/brain'
import { formatYTSTime, getWord } from '@/utils'
import { FieldTimeOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import React from 'react'
import styles from './index.less'

export interface UpdateTimeProps {
  route: ProjectRoute
}

const UpdateTime: React.FC<UpdateTimeProps> = ({ route }) => {
  const updateTime = route.updatedAt || route.updated_at
  if (!updateTime) return null

  return (
    <div className={styles.wrapper}>
      <FieldTimeOutlined />
      {getWord('modified-time')}: {formatYTSTime(dayjs(updateTime))}
    </div>
  )
}

export default UpdateTime
