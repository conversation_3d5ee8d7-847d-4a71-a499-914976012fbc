import { MainTreeForRoute } from '@/pages/route/util'
import { ProjectRoute, RetroBackbone } from '@/services/brain'
import { getWord } from '@/utils'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { Popover } from 'antd'
import React from 'react'
import styles from './index.less'

const calcMainTreeSteps = (main_trees: MainTreeForRoute): number => {
  if (!main_trees.children?.length) {
    return 0
  }
  return (
    1 +
    main_trees.children.reduce((acc, cur) => acc + calcMainTreeSteps(cur), 0)
  )
}

export interface RouteStepCountProps {
  route: ProjectRoute | RetroBackbone
}

const RouteStepCount: React.FC<RouteStepCountProps> = ({ route }) => {
  const count =
    'backbone' in route
      ? route.min_n_main_tree_steps || 0
      : calcMainTreeSteps(route.main_tree)
  return (
    <Popover content={getWord('multi-steps-tip')} className={styles.wrapper}>
      {getWord('route-length')}
      <QuestionCircleOutlined className={styles.icon} />: {count}
    </Popover>
  )
}

export default RouteStepCount
