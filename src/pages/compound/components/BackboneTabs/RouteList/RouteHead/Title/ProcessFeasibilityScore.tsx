import { RetroBackbone } from '@/services/brain'
import { getWord } from '@/utils'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { Popover } from 'antd'
import React from 'react'
import styles from './index.less'

export interface ProcessFeasibilityScoreProps {
  route: RetroBackbone
}

const ProcessFeasibilityScore: React.FC<ProcessFeasibilityScoreProps> = ({
  route: { process_feasibility_score = 1 }
}) => {
  return (
    <Popover
      className={styles.wrapper}
      content={getWord('process-feasibility-tip')}
    >
      {getWord('process-feasibility')}
      <QuestionCircleOutlined className={styles.icon} />:{' '}
      {Math.round(process_feasibility_score * 100)}%
    </Popover>
  )
}

export default ProcessFeasibilityScore
