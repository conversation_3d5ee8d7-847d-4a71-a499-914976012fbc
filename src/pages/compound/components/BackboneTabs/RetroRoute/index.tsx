import StatusTip from '@/components/StatusTip'
import { useSwitchStore } from '@/pages/compound/store'
import { SearchStatus } from '@/services/brain/types'
import { Pagination, Spin } from 'antd'
import React, { useEffect } from 'react'
import useRetroHistory from '../../SearchHistory/useRetroHistory'
import { FilterBackboneIdsContext } from '../filterBackboneIdsContext'
import RouteList from '../RouteList'
import {
  getRetroRouteImage,
  getRetroRouteTip,
  RetroRouteStatus
} from '../RouteList/utils'
import { RetroFilter, useBackboneTabsStore } from '../store'
import { useFilterBackboneIds } from '../useFilterBackboneIds'
import { useRetroRoute } from '../useRetroRoute'

const getRetroRouteStatusFromProcess = (
  status?: SearchStatus,
  length?: number
): RetroRouteStatus => {
  return status === 'failed' || status === 'canceled'
    ? 'failed'
    : status === 'pending' || status === 'limited'
    ? 'queueing'
    : status === 'running'
    ? 'generating'
    : length
    ? 'default'
    : 'empty'
}

const feasibilityFilters: RetroFilter = {
  group: 'start_material',
  preference: {
    plus_process_feasibility_score: true,
    price_score: 2,
    safety_score: 2,
    novelty_score: -2
  }
}

export interface RetroRouteProps {
  compoundId?: number | string
}

const RetroRoute: React.FC<RetroRouteProps> = ({ compoundId }) => {
  const { setRetroModel } = useSwitchStore()
  const { selected } = useRetroHistory(compoundId, true)
  const { backbone_tree, id, status } = selected || {}
  const { retroRouteFilters, setPagination, retroLayout } =
    useBackboneTabsStore()
  const hookResult = useFilterBackboneIds(backbone_tree)
  const { backboneIds } = hookResult

  const filters =
    retroLayout === 'feasibility' ? feasibilityFilters : retroRouteFilters
  const {
    data: { data, pagination } = {},
    isLoading,
    refetch
  } = useRetroRoute(id, {
    backboneIds: retroLayout === 'filter' ? backboneIds : undefined,
    filters
  })

  useEffect(() => {
    refetch?.()
  }, [backbone_tree])

  const showPagination = retroLayout !== 'filter'
  const showFilter = retroLayout === 'filter'
  const routeStatus = getRetroRouteStatusFromProcess(status, data?.length)

  return (
    <>
      <Spin spinning={isLoading || false}>
        {!data?.length ? (
          <StatusTip
            image={getRetroRouteImage(routeStatus)}
            des={getRetroRouteTip(routeStatus, () => setRetroModel('init'))}
          />
        ) : (
          <FilterBackboneIdsContext.Provider
            value={{ hook: hookResult, showFilter }}
          >
            <RouteList
              routes={data}
              refetch={() => refetch?.()}
              tempRoute={status !== 'completed'}
            />
          </FilterBackboneIdsContext.Provider>
        )}
      </Spin>

      {showPagination && (
        <Pagination
          total={pagination?.total || 0}
          align="end"
          hideOnSinglePage
          current={pagination?.page || retroRouteFilters.page}
          pageSize={pagination?.pageSize || retroRouteFilters.pageSize}
          showSizeChanger={false}
          onChange={(page, pageSize) => setPagination(page, pageSize)}
        />
      )}
    </>
  )
}

export default RetroRoute
