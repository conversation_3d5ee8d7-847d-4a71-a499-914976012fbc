import { ProjectRoute, query, StrapiPagination } from '@/services/brain'
import { useQuery } from '@tanstack/react-query'

const fetchInfo = async (
  compoundId: number,
  page: number,
  pageSize: number
): Promise<{ data: ProjectRoute[]; pagination?: StrapiPagination }> => {
  const { data, error, meta } = await query<ProjectRoute>(
    `project-routes?comment=true`
  )
    .filterDeep('project_compound.id', 'eq', compoundId)
    .sortBy([
      { field: 'createdAt', order: 'desc' },
      { field: 'updatedAt', order: 'desc' }
    ])
    .paginate(page, pageSize)
    .get()

  if (!error && data) {
    return { data, pagination: meta?.pagination }
  }
  throw new Error('Network response was not ok')
}

export const useProjectRoute = (
  id?: number | string,
  page: number = 1,
  pageSize: number = 10
) => {
  const idNum = typeof id !== 'number' ? Number.parseInt(id || '') : id
  const { data, error, isLoading, isFetching, refetch } = useQuery({
    queryKey: ['project-route', idNum, page, pageSize],
    queryFn: () => (id ? fetchInfo(idNum, page, pageSize) : undefined),
    enabled: !isNaN(idNum),
    keepPreviousData: true
  })

  if (isNaN(idNum)) return {}
  return { data, error, isLoading, isFetching, refetch }
}
