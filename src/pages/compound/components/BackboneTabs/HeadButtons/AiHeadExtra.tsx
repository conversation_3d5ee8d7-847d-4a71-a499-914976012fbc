import { useUserSettingQuery } from '@/hooks/useUserSetting'
import { useSwitchStore } from '@/pages/compound/store'
import { getWord } from '@/utils'
import { SettingOutlined } from '@ant-design/icons'
import { Divider, Segmented, Tag } from 'antd'
import React, { useEffect } from 'react'
import useRetroHistory from '../../SearchHistory/useRetroHistory'
import { useBackboneTabsStore } from '../store'

export const retroLayouts = ['group', 'filter', 'feasibility'] as const
export type RetroLayout = (typeof retroLayouts)[number]

export interface AiHeadExtraProps {
  compoundId?: string
}

const AiHeadExtra: React.FC<AiHeadExtraProps> = ({ compoundId }) => {
  const { setFilterDrawer } = useSwitchStore()
  const { setting: { retro: { display_feasibility_layout } = {} } = {} } =
    useUserSettingQuery()
  const { selected: { backbone_tree, status } = {} } = useRetroHistory(
    compoundId,
    true
  )
  const {
    retroLayout,
    retroRouteFilters: { groupSimilarId, preference },
    setGroupSimilarId,
    setRetroLayout
  } = useBackboneTabsStore()
  const preferenceSetted =
    preference?.novelty_score ||
    preference?.price_score ||
    preference?.safety_score

  const layouts: RetroLayout[] = ['group']
  if (backbone_tree) {
    layouts.push('filter')
  }
  if (display_feasibility_layout) {
    layouts.push('feasibility')
  }

  const layoutOptions = layouts.map((layout) => ({
    label: getWord(`retro-route-${layout}-layout`),
    value: layout
  }))

  useEffect(() => {
    if (!backbone_tree && retroLayout === 'filter') {
      setRetroLayout('group')
    }
  }, [backbone_tree])

  const layoutSelect = (
    <Segmented
      options={layoutOptions}
      value={retroLayout}
      onChange={setRetroLayout}
    />
  )

  const groupTag =
    groupSimilarId !== undefined &&
    retroLayout === 'group' &&
    status === 'completed' ? (
      <Tag
        key="similarTag"
        closable
        color="pink"
        onClose={() => setGroupSimilarId()}
      >
        {`${getWord('group')}${groupSimilarId + 1}`}
      </Tag>
    ) : null

  const groupFilterSwitch =
    retroLayout === 'group' && status === 'completed' ? (
      <SettingOutlined
        onClick={() => setFilterDrawer(true)}
        color={preferenceSetted ? '#0047BB' : '#191919'}
      />
    ) : null

  return (
    <>
      {groupTag}
      {groupFilterSwitch}
      {groupTag || groupFilterSwitch ? <Divider type="vertical" /> : null}
      {layoutSelect}
    </>
  )
}

export default AiHeadExtra
