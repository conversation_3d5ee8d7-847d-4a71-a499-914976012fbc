import { getWord } from '@/utils'
import { PlusOutlined } from '@ant-design/icons'
import { history, useAccess } from '@umijs/max'
import { Button } from 'antd'
import React from 'react'
import { useProjectCompound } from '../../CompoundInfo/useCompoundInfo'

export interface CreateMyRouteButtonProps {
  compoundId?: string
}

const CreateMyRouteButton: React.FC<CreateMyRouteButtonProps> = ({
  compoundId
}) => {
  const access = useAccess()
  const { data } = useProjectCompound(compoundId)
  const canCreateRoute = access?.authCodeList?.includes(
    'compound.button.new-route'
  )
  const createRouteButton = (
    <Button
      type="primary"
      block
      size="small"
      onClick={() =>
        history.push(
          `/projects/${data?.project?.id}/compound/${data?.id}/create`
        )
      }
      hidden={!canCreateRoute}
      disabled={!data?.input_smiles}
    >
      <PlusOutlined />
      {getWord('new-route')}
    </Button>
  )
  return compoundId ? createRouteButton : null
}

export default CreateMyRouteButton
