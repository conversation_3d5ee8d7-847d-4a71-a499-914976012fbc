import StatusTip from '@/components/StatusTip'
import usePagination from '@/hooks/usePagination'
import { Pagination, Spin } from 'antd'
import React from 'react'
import { useProjectCompound } from '../../CompoundInfo/useCompoundInfo'
import RouteList from '../RouteList'
import { getProjectRouteTip } from '../RouteList/utils'
import { useProjectRoute } from '../useProjectRoute'

export interface ProjectRouteProps {
  compoundId?: string
}

const ProjectRoute: React.FC<ProjectRouteProps> = ({ compoundId }) => {
  const { projectId } = useProjectCompound(compoundId)
  const { page, pageSize, setPagination } = usePagination(1, 10)
  const {
    data: { data, pagination } = {},
    isLoading,
    refetch
  } = useProjectRoute(compoundId, page, pageSize)

  return (
    <>
      <Spin spinning={isLoading || false}>
        {!projectId || !compoundId ? null : !data?.length ? (
          <StatusTip des={getProjectRouteTip(projectId, compoundId)} />
        ) : (
          <RouteList routes={data} refetch={() => refetch?.()} />
        )}
      </Spin>
      <Pagination
        align="end"
        total={pagination?.total || 0}
        hideOnSinglePage
        current={pagination?.page}
        pageSize={pagination?.pageSize}
        showSizeChanger={false}
        onChange={(page, pageSize) => setPagination({ page, pageSize })}
      />
    </>
  )
}

export default ProjectRoute
