import { RetroParamsConfig } from '@/services/brain'
import { RouteType } from '@/types/common'
import { create } from 'zustand'
import { combine, subscribeWithSelector } from 'zustand/middleware'
import { RetroLayout } from './HeadButtons/AiHeadExtra'

export interface RetroFilter {
  group: 'start_material' | 'cluster'
  groupSimilarId?: number
  page?: number
  pageSize?: number
  preference?: RetroParamsConfig & { plus_process_feasibility_score?: boolean }
}

interface State {
  tabType: RouteType
  counts: Record<RouteType, number>
  retroLayout: RetroLayout
  retroRouteFilters: RetroFilter
}

const initState: State = {
  tabType: 'aiGenerated',
  counts: { aiGenerated: 0, myRoutes: 0, reaction: 0 },
  retroLayout: 'group',
  retroRouteFilters: { group: 'start_material' }
}

export const useBackboneTabsStore = create(
  subscribeWithSelector(
    combine({ ...initState }, (set) => ({
      set: (newState: Partial<State>) => set((s) => ({ ...s, ...newState })),
      setTabType: (tab: RouteType) => set((s) => ({ ...s, tabType: tab })),
      setCount: (type: RouteType, count: number) =>
        set((s) => {
          const oldValue = s.counts[type]
          if (oldValue !== count) {
            return { ...s, counts: { ...s.counts, [type]: count } }
          }
          return s
        }),
      setRetroLayout: (layout: RetroLayout) =>
        set((s) => ({ ...s, retroLayout: layout })),
      setRetroRouteFilters: (filters: RetroFilter) =>
        set((s) => ({ ...s, retroRouteFilters: filters })),
      setPagination: (page: number, pageSize: number) =>
        set((s) => ({
          ...s,
          retroRouteFilters: { ...s.retroRouteFilters, page, pageSize }
        })),
      setPreference: (preference: RetroFilter['preference']) =>
        set((s) => ({
          ...s,
          retroRouteFilters: { ...s.retroRouteFilters, preference, page: 1 }
        })),
      setGroupSimilarId: (group?: number) =>
        set((s) => ({
          ...s,
          retroRouteFilters: {
            ...s.retroRouteFilters,
            groupSimilarId: group,
            page: 1
          }
        }))
    }))
  )
)
