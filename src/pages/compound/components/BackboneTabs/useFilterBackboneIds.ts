import { useCallback, useEffect, useMemo, useState } from 'react'

export interface BackboneTreeNode {
  smiles: string
  maxScore: number
  maxScoreId: number
  children: BackboneTreeNode[]
}

const traceNode = (
  trace: string[],
  tree?: BackboneTreeNode
): BackboneTreeNode | undefined => {
  const [target, ...left] = trace
  if (!target || tree?.smiles !== target) return undefined
  if (!left.length) return tree
  const child = tree.children.find((c) => c.smiles === left[0])
  return traceNode(left, child)
}

export interface FilterBackboneIdsResult {
  backboneIds: number[]
  setTrace: React.Dispatch<React.SetStateAction<string[]>>
  getNode: (path: string[]) => BackboneTreeNode | undefined
  isSelected: (path: string[]) => boolean
  select: (path: string[]) => void
  unselect: (path: string[]) => void
}

export const useFilterBackboneIds = (
  backboneTree: BackboneTreeNode
): FilterBackboneIdsResult => {
  const [trace, setTrace] = useState<string[]>([])

  useEffect(() => {
    setTrace(backboneTree?.smiles ? [backboneTree.smiles] : [])
  }, [backboneTree])

  const traced = useMemo(
    () => traceNode(trace, backboneTree),
    [trace, backboneTree]
  )

  const select = useCallback((path: string[]) => {
    if (path?.length) {
      setTrace(path)
    }
  }, [])

  const unselect = useCallback((path: string[]) => {
    if (path?.length && path.length > 1) {
      setTrace(path.slice(0, -1))
    }
  }, [])

  const getNode = useCallback(
    (path: string[]) =>
      path.length ? traceNode(path, backboneTree) : undefined,
    [backboneTree]
  )

  const isSelected = useCallback(
    (path: string[]): boolean => path.every((p, i) => p === trace[i]),
    [trace]
  )

  const backboneIds = useMemo(
    () =>
      traced?.children.map((c) => c.maxScoreId) ||
      (backboneTree?.maxScoreId ? [backboneTree.maxScoreId] : []),
    [traced, backboneTree]
  )

  return {
    backboneIds,
    setTrace,
    getNode,
    isSelected,
    select,
    unselect
  }
}
