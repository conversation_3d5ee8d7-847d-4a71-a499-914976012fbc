import { getWord } from '@/utils'
import { ProForm, ProFormDependency } from '@ant-design/pro-components'
import { Button, Col, Divider, Form, TreeSelect } from 'antd'
import { isEmpty } from 'lodash'
import { useEffect, useState } from 'react'
import { useModel } from 'umi'
import { ElementProps } from './util'
export interface MaterialOption {
  label: string
  value: number
  published?: boolean
}

export const MaterialLibSelect: React.FC<{ props: ElementProps }> = ({
  props
}) => {
  const { getUserConfigs } = useModel('login')
  const { getMaterialLibOptions } = useModel('compound')
  const [options, setOptions] = useState<MaterialOption[]>([])
  const form = Form.useFormInstance()

  const selectDefaultConfig = (defaultMaterialLib: number[]) => {
    form.setFieldValue(props.key, defaultMaterialLib)
    form.validateFields()
  }

  const initOptions = async () => {
    const options = await getMaterialLibOptions(true)
    setOptions(options)
    if (!props.disabled) {
      const data = await getUserConfigs()
      if (!data || isEmpty(data)) return
      for (const item of data) {
        if (
          item?.setting_label === 'retro_params' &&
          item?.setting_value?.material_lib
        ) {
          selectDefaultConfig(item?.setting_value?.material_lib as number[])
        }
      }
    }
  }

  useEffect(() => {
    initOptions()
  }, [])

  return (
    <ProFormDependency name={[props.key]} key={props.key}>
      {(values) => {
        const selectedAll = values[props.key]?.length === options.length
        return (
          <Col span={24} key={props.key}>
            <ProForm.Item {...props}>
              <TreeSelect
                allowClear={true}
                treeCheckable={true}
                showCheckedStrategy={TreeSelect.SHOW_CHILD}
                maxTagCount={3}
                maxTagPlaceholder={(omittedValues) =>
                  `+ ${omittedValues.length} ${getWord('raw-material-lib')}...`
                }
                treeData={
                  props.disabled ? options : options.filter((o) => o.published)
                }
                dropdownRender={(menu) => (
                  <>
                    <Button
                      type="text"
                      onClick={() =>
                        selectDefaultConfig(
                          selectedAll ? [] : options.map((o) => o.value)
                        )
                      }
                      key="all"
                    >
                      {selectedAll
                        ? getWord('un-select')
                        : getWord('select-all')}
                    </Button>
                    <Divider style={{ margin: '8px 0' }} key="divider" />
                    {menu}
                  </>
                )}
              />
            </ProForm.Item>
          </Col>
        )
      }}
    </ProFormDependency>
  )
}
