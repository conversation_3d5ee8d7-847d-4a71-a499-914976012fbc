import { useBrainFetch } from '@/hooks/useBrainFetch'
import {
  fetchUserSetting,
  updateParamsConfigDefaultWithUserSetting
} from '@/hooks/useUserSetting'
import { RetroParamConfig, query } from '@/services/brain'
import { getWord } from '@/utils'
import { ProForm } from '@ant-design/pro-components'
import { Button, Form, Typography } from 'antd'
import { groupBy, sortBy } from 'lodash'
import React, { useEffect, useState } from 'react'

import SearchMode from './SearchMode'
import { type SearchParamFields } from './index.d'
import styles from './index.less'
import {
  Config,
  convertFormValuesToServerValues,
  convertServerValuesToFormValues,
  getDefaultValue,
  getParam
} from './util'

export interface SearchParamProps {
  getFilterEvent?: { onGetFilters?: (value: SearchParamFields) => void }
  registerParamsGetter?: (
    getterFn: () => Promise<SearchParamFields | undefined>
  ) => void
  viewOnly?: SearchParamFields
  retroId?: string
  target?: string
  getTarget?: () => Promise<string>
  onEdit?: () => void
  onLoading?: (loading: boolean) => void
}

const SearchParam: React.FC<SearchParamProps> = ({
  getFilterEvent,
  registerParamsGetter,
  viewOnly: propViewOnly,
  onEdit,
  target,
  getTarget,
  retroId,
  onLoading
}) => {
  const { fetch, loading } = useBrainFetch()
  const [changingMode, setChangingMode] = useState<boolean>(false)
  const [viewOnly, setViewOnly] = useState<boolean>(!!propViewOnly || false)
  const [config, setConfig] = useState<Config>({
    professional: [],
    normal: [],
    other: []
  })

  const fetchConfig = async () => {
    const { data } = await fetch(
      query<RetroParamConfig>('retro-param-configs')
        .setLocale('zh-CN')
        .paginate(1, 1000)
        .get()
    )
    return data
  }

  useEffect(() => {
    Promise.all([fetchConfig(), fetchUserSetting()]).then(([c, s]) => {
      const updatedConfig = updateParamsConfigDefaultWithUserSetting(c || [], s)
      const configs = groupBy(sortBy(updatedConfig, ['order']), 'zone')
      setConfig(configs as unknown as Config)
    })
  }, [])

  const [form] = Form.useForm()
  const disabled = !!viewOnly || changingMode || loading

  useEffect(() => {
    form
      .validateFields()
      .then((values) => {
        getFilterEvent?.onGetFilters?.(
          convertFormValuesToServerValues(values, config)
        )
      })
      .catch()
  }, [getFilterEvent])

  useEffect(() => {
    const newValues = {
      ...getDefaultValue(config),
      ...(propViewOnly
        ? convertServerValuesToFormValues(propViewOnly, config)
        : {})
    }
    form.setFieldsValue(newValues)
  }, [propViewOnly, config])

  useEffect(() => {
    onLoading?.(changingMode || loading)
  }, [changingMode, loading])

  useEffect(() => {
    registerParamsGetter?.(async () => {
      try {
        const values = await form.validateFields()
        return convertFormValuesToServerValues(values, config)
      } catch {
        return
      }
    })
  }, [registerParamsGetter])

  return (
    <ProForm
      form={form}
      className={styles['filter-form-root']}
      grid
      disabled={disabled}
      loading={loading}
      submitter={false}
    >
      <SearchMode
        form={form}
        onLoading={(l) => setChangingMode(l)}
        viewOnly={viewOnly}
        target={target}
        getTarget={getTarget}
        updateFields={[...config.normal, ...config.professional]
          .filter((c) => !c.config?.not_update_when_change_mode)
          .map((c) => c.field)}
        config={config}
      />
      {config.other.map((c) => getParam(c, disabled))}
      {retroId && viewOnly ? (
        <Typography.Text type="secondary" italic copyable>
          {retroId}
        </Typography.Text>
      ) : null}
      {viewOnly ? (
        <Button
          type="primary"
          className={styles['re-retro-btn']}
          disabled={false}
          onClick={() => {
            setViewOnly(false)
            onEdit?.()
          }}
        >
          {getWord('search-again')}
        </Button>
      ) : null}
    </ProForm>
  )
}

export default SearchParam

export { type SearchParamFields } from './index.d'
