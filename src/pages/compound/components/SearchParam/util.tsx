import SmilesInput from '@/components/SmilesInput'
import { RetroParamConfig } from '@/services/brain'
import {
  ProForm,
  ProFormCheckbox,
  ProFormDependency,
  ProFormDigit,
  ProFormMoney,
  ProFormSelect,
  ProFormSwitch,
  ProFormText
} from '@ant-design/pro-components'
import { Col } from 'antd'
import { isNil, omit } from 'lodash'
import { ReactNode } from 'react'
import { MaterialLibSelect } from './MaterialLabSelect'
import { SearchParamFields } from './index.d'
import styles from './index.less'

export interface Config {
  professional: RetroParamConfig[]
  normal: RetroParamConfig[]
  other: RetroParamConfig[]
}

export interface ElementProps {
  label: string
  name: string
  key: string
  disabled: boolean | undefined
  required: boolean
  rules: { required: boolean }[]
  colProps: { md: number }
}

export const getDefaultValue = (config: Config): SearchParamFields => {
  return [...config.normal, ...config.professional]
    .map((c) =>
      c.type === 'enum' && isNil(c.default_value)
        ? { ...c, default_value: c.enums?.[0] }
        : c
    )
    .filter((c) => !isNil(c.default_value))
    .reduce((acc, cur) => {
      acc[cur.field] = cur.default_value
      return acc
    }, {} as SearchParamFields)
}

export const getConfirmFieldKey = (field: string): string => `${field}-confirm`

export const getParam = (
  config: RetroParamConfig,
  disabled?: boolean,
  colSpan: number = 24
): ReactNode => {
  const props: ElementProps = {
    label: config.label,
    name: config.field,
    key: config.field,
    disabled,
    required: config.required || false,
    rules: [{ required: config.required || false }],
    colProps: { md: colSpan }
  }
  if (config.config?.preconfirm) {
    const confirmProps = {
      label: config.config.confirm_label,
      name: getConfirmFieldKey(config.field),
      key: getConfirmFieldKey(config.field),
      disabled
    }
    return (
      <Col span={colSpan} key={config.field} className={styles['confirm-col']}>
        <ProFormSwitch {...confirmProps} />
        <ProFormDependency name={[getConfirmFieldKey(config.field)]}>
          {(value) => {
            if (value?.[getConfirmFieldKey(config.field)]) {
              return getParam({ ...config, config: undefined }, disabled, 24)
            }
            return null
          }}
        </ProFormDependency>
      </Col>
    )
  }
  switch (config.type) {
    case 'money':
      return <ProFormMoney {...props} min={0} locale="zh-CN" />
    case 'int':
      return <ProFormDigit {...props} fieldProps={{ precision: 0 }} />
    case 'float':
      return (
        <ProFormDigit {...props} fieldProps={{ precision: 1, step: 0.1 }} />
      )
    case 'enum':
      return <ProFormSelect {...props} options={config.enums || []} />
    case 'boolean':
      return <ProFormSwitch {...props} />
    case 'materials':
      return (
        <Col span={24} key={config.field}>
          <ProForm.Item {...props}>
            <SmilesInput disabled={disabled} />
          </ProForm.Item>
        </Col>
      )
    case 'rxns':
      return (
        <Col span={24} key={config.field}>
          <ProForm.Item {...props}>
            <SmilesInput type="reaction" disabled={disabled} />
          </ProForm.Item>
        </Col>
      )
    case 'select':
      return (
        <ProFormCheckbox.Group
          {...props}
          convertValue={(value) => {
            if (isNil(value)) {
              return []
            } else if (Array.isArray(value)) {
              return value
            }
            return Object.entries(value).reduce<string[]>((acc, cur) => {
              if (cur[1]) acc.push(cur[0])
              return acc
            }, [])
          }}
          layout="vertical"
          options={config.enums}
        />
      )
    case 'material_libs':
      return <MaterialLibSelect props={props} key={props.key} />
    default:
      return <ProFormText {...props} />
  }
}

export const getConfirmFields = (config: Config): RetroParamConfig[] => {
  return Object.values(config)
    .flatMap((v) => v)
    .reduce<RetroParamConfig[]>((acc, cur) => {
      if (cur?.config?.preconfirm) {
        acc.push(cur)
      }
      return acc
    }, [])
}

export const convertServerValuesToFormValues = (
  values: Record<string, any>,
  config?: Config
): Record<string, any> => {
  if (config) {
    getConfirmFields(config).forEach((f) => {
      const serverValue = values?.[f.field]
      values[getConfirmFieldKey(f.field)] = !!f.config?.default_confirm
      values[f.field] = serverValue
      if (isNil(serverValue) || serverValue === f.config?.not_confirm_value) {
        values[getConfirmFieldKey(f.field)] = false
        values[f.field] = undefined
      } else {
        values[getConfirmFieldKey(f.field)] = true
      }
    })
  }
  const diversity_control = []
  if (!!values?.cluster_route) diversity_control.push('cluster_route')
  if (!!values?.cluster_reaction) diversity_control.push('cluster_reaction')
  return { ...values, diversity_control }
}

export const convertFormValuesToServerValues = (
  values: Record<string, any>,
  config?: Config
): SearchParamFields => {
  if (config) {
    getConfirmFields(config).forEach((f) => {
      if (!values?.[getConfirmFieldKey(f.field)]) {
        values[f.field] = f.config?.not_confirm_value
      }
      delete values[getConfirmFieldKey(f.field)]
    })
  }
  return {
    ...omit(values, 'diversity_control'),
    cluster_route: !!values.diversity_control?.includes('cluster_route'),
    cluster_reaction: !!values.diversity_control?.includes('cluster_reaction')
  } as unknown as SearchParamFields
}
