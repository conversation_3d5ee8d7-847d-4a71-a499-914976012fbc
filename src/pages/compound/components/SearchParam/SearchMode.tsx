import { query, service } from '@/services/brain'
import { getWord } from '@/utils'
import { DownOutlined, UpOutlined } from '@ant-design/icons'
import { FormInstance, isNil } from '@ant-design/pro-components'
import { Col } from 'antd'
import Segmented, { SegmentedValue } from 'antd/es/segmented'
import React, { useEffect, useState } from 'react'

import type { SearchParamFields } from './index.d'
import {
  Config,
  convertServerValuesToFormValues,
  getConfirmFieldKey,
  getConfirmFields,
  getParam
} from './util'

const modes = [
  { label: getWord('fast-mode'), value: 'fast' },
  { label: getWord('complex-mode'), value: 'refined' }
  // { label: getWord('recommend-to-me'), value: 'recommend' }
]
const defaultMode = 'fast'
const getModeValues = async (
  mode: string,
  target?: string,
  config?: Config
): Promise<Partial<SearchParamFields> | null> => {
  if (mode === 'recommend') {
    if (!target) return {}
    const res = await query<SearchParamFields>('retro/recommend_config', {
      method: 'post',
      data: { target },
      normalizeData: false
    }).get()

    return convertServerValuesToFormValues(res, config) as SearchParamFields
  }
  const fieldName = `${mode}_params`
  const { data } = await service<any>('retro-config').select([fieldName]).get()
  return (
    convertServerValuesToFormValues(
      (data as unknown as { [key: string]: SearchParamFields })?.[fieldName],
      config
    ) || {}
  )
}

export interface SearchModeProps {
  form: FormInstance
  onLoading?: (loading: boolean) => void
  viewOnly?: boolean
  target?: string
  getTarget?: () => Promise<string>
  updateFields?: string[]
  config: Config
}

const SearchMode: React.FC<SearchModeProps> = ({
  form,
  onLoading,
  viewOnly,
  target,
  getTarget,
  updateFields,
  config
}) => {
  const [loading, setLoading] = useState<boolean>(false)
  const [expanded, setExpanded] = useState<boolean>(viewOnly || false)
  const onChange = async (mode: SegmentedValue, config: Config) => {
    setLoading(true)
    const confirmFields = new Set(getConfirmFields(config).map((f) => f.field))
    const smiles = target || (await getTarget?.()) || ''
    let values = await getModeValues(mode as string, smiles, config)
    if (values && updateFields?.length) {
      values = updateFields.reduce<Partial<SearchParamFields>>((acc, cur) => {
        if (!isNil(values?.[cur])) acc[cur] = values?.[cur]
        if (confirmFields.has(cur)) {
          acc[getConfirmFieldKey(cur)] = values?.[getConfirmFieldKey(cur)]
        }
        return acc
      }, {})
    }
    form.resetFields(updateFields)
    form.setFieldsValue(values)
    setLoading(false)
  }

  useEffect(() => onLoading?.(loading), [loading])
  useEffect(() => {
    if (!viewOnly && config) {
      onChange(defaultMode, config)
    }
  }, [config, open])

  return (
    <>
      {!viewOnly && (
        <Col span={24} style={{ marginBottom: '10px' }}>
          <Segmented
            size="small"
            options={modes}
            disabled={loading || viewOnly}
            onChange={(mode) => onChange(mode, config)}
          />
        </Col>
      )}
      {config.normal.map((c) => getParam(c, viewOnly))}
      <Col span={24} key="expand-btn" className="expand-btn-wrapper">
        <a type="link" onClick={() => setExpanded((p) => !p)}>
          {getWord('advanced-settings')}
          {expanded ? <UpOutlined /> : <DownOutlined />}
        </a>
      </Col>
      <div style={{ display: expanded ? 'contents' : 'none' }}>
        {config.professional.map((c) => getParam(c, viewOnly))}
      </div>
    </>
  )
}

export default SearchMode
