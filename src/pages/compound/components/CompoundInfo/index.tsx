import MoleculeStructure from '@/components/MoleculeStructure'
import WordParser from '@/components/WordParser'
import useOptions from '@/hooks/useOptions'
import { ProjectCompound, ProjectType } from '@/services/brain'
import { getWord, isEN } from '@/utils'
import { useModel } from '@umijs/max'
import { Card, Skeleton, Tag, Typography } from 'antd'
import React from 'react'
import { useProjectCompound } from './useCompoundInfo'

export interface CompoundInfoProps {
  moleculeId?: string
}

const CompoundInfo: React.FC<CompoundInfoProps> = ({ moleculeId }) => {
  const { typeMap } = useOptions()
  const { resetFilterInfo } = useModel('compound')
  const { data, error, isLoading } = useProjectCompound(moleculeId)

  const compoundInfo = (comp: ProjectCompound) => (
    <>
      <MoleculeStructure structure={comp.input_smiles} height={150} />
      <Typography.Text
        style={{ width: isEN() ? 115 : 162 }}
        copyable
        ellipsis={{ tooltip: comp.no }}
      >
        {comp.no}
      </Typography.Text>
      <div className="display-flex targetMoleculeInfo">
        {comp.type && (
          <Tag
            key="targetMoleculeType"
            className="targetMoleculeType"
            onClose={() => resetFilterInfo()}
          >
            {typeMap[comp.type]}
          </Tag>
        )}
        <Tag
          key="targetMoleculeStatus"
          className="targetMoleculeStatus"
          onClose={() => resetFilterInfo()}
        >
          <WordParser word={comp.status as ProjectType} />
        </Tag>
      </div>
    </>
  )

  return (
    <Card
      size="small"
      className="target-card"
      bordered
      title={getWord('target-molecules')}
    >
      {isLoading ? (
        <Skeleton />
      ) : error || !data ? (
        getWord('noticeIcon.empty')
      ) : (
        compoundInfo(data)
      )}
    </Card>
  )
}

export default CompoundInfo
