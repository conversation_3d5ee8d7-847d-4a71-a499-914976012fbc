import { ProjectCompound, service } from '@/services/brain'
import { useQuery } from '@tanstack/react-query'

const fetchInfo = async (id: number): Promise<ProjectCompound> => {
  const { data: compoundData, error } = await service<ProjectCompound>(
    'project-compounds'
  )
    .selectManyByID([id])
    .populateWith('project_routes', ['id'])
    .populateWith('compound', ['id', 'smiles'])
    .populateDeep([
      {
        path: 'retro_processes',
        fields: ['id'],
        children: [{ key: 'retro_backbones', fields: ['id'] }]
      }
    ])
    .populateWith('default_route', ['id'])
    .populateWith('project', ['id'])
    .get()

  if (!error && compoundData?.[0]) {
    const compound = compoundData[0]
    compound.project_routes_number = compound.project_routes?.length
    compound.retro_backbones_number = compound.retro_processes?.flatMap(
      (p) => p.retro_backbones
    ).length
    return compound
  }
  throw new Error('Network response was not ok')
}

export const useProjectCompound = (id?: string | number) => {
  const idNum = typeof id !== 'number' ? Number.parseInt(id || '') : id
  const { data, error, isLoading, refetch } = useQuery({
    queryKey: ['project-compound', idNum],
    queryFn: () => (id ? fetchInfo(idNum) : undefined),
    enabled: !isNaN(idNum)
  })

  if (isNaN(idNum)) return {}
  const projectId = data?.project?.id
  return {
    data,
    error,
    isLoading,
    refetch,
    projectId,
    defaultRouteId: data?.default_route?.id
  }
}
