import { ReactComponent as FilterIcon } from '@/assets/svgs/filterIcon.svg'
import { ReactComponent as GnerateRoutesIcon } from '@/assets/svgs/gnerate-routes.svg'
import { ReactComponent as SettingsIcon } from '@/assets/svgs/navbar/settings.svg'
import Launcher from '@/components/Launcher'
import ModalBase from '@/components/ModalBase'
import { useModalBase } from '@/components/ModalBase/useModalBase'
import MoleculeStructure from '@/components/MoleculeStructure'
import SyntheticRoutes from '@/components/SyntheticRoutes'
import WordParser from '@/components/WordParser'
import { allSearchStatus, defatulFilterInfo } from '@/constants'
import useOptions from '@/hooks/useOptions'
import { useUserSetting } from '@/hooks/useUserSetting'
import type {
  ProjectType,
  RouteSearchParams,
  SearchLog
} from '@/services/brain'
import { SearchHsitory, service } from '@/services/brain'
import { RetroProcesses } from '@/services/brain/types/retro-processes'
import { ITag, type RouteType } from '@/types/Common'
import {
  formatYTSTime,
  getWord,
  isEN,
  isReadonlyMolecule,
  isValidArray,
  toInt
} from '@/utils'
import message from '@/utils/message'
import { PlusOutlined } from '@ant-design/icons'
import { PageContainer } from '@ant-design/pro-components'
import {
  history,
  useAccess,
  useLocation,
  useModel,
  useParams,
  useSearchParams
} from '@umijs/max'
import { useUpdateEffect } from 'ahooks'
import {
  Button,
  Card,
  Col,
  Modal,
  Popover,
  Row,
  Switch,
  Tag,
  Typography
} from 'antd'
import { Content } from 'antd/lib/layout/layout'
import cs from 'classnames'
import { isEqual, isNil, pick } from 'lodash'
import { FC, useEffect, useState } from 'react'
import { RouteEventContext, useRouteEventContext } from './RouteEventContext'
import FilterDrawer from './components/FilterDrawer'
import SearchHsitoryCard from './components/SearchHsitoryCard'
import SearchParam, { SearchParamProps } from './components/SearchParam'
import type { SearchParamFields } from './components/SearchParam/index.d'
import './index.less'
import styles from './index.less'

const CompoundDetail: FC = ({}) => {
  const { typeMap } = useOptions()
  const { dialogProps, confirm } = useModalBase()
  const [viewingParams, setViewingParams] = useState<SearchParamFields>()
  const [viewingRetroId, setViewingRetroId] = useState<string>()
  const { setting } = useUserSetting()
  const { id: projectId, compoundId: moleculeId } = useParams<{
    id: string
    compoundId: string
  }>()

  const {
    curFilterInfo,
    resetFilterInfo,
    getSearchHistory,
    getSearchLogs,
    searchHistoryData,
    filterSearchHistory,
    filteredSearchHistory,
    removeNewMsgDot,
    updateCurHistoryInfo,
    updateRouteType,
    routeType,
    resetCompoundData,
    getTargetMolecule,
    curAiRouteNum,
    targetMolecule,
    backboneRange,
    mainTreeStepsRange,
    cacheFilterInfo,
    smiles,
    retroParamsConfig,
    retroProcessUpdates,
    curHistoryInfo,
    cacheRetroProcessUpdates
  } = useModel('compound')
  const access = useAccess()
  const [curSearchHistory, setCurSearchHistory] = useState<SearchHsitory[]>([])
  const [onlySuccessData, setOnlySuccessData] = useState<SearchHsitory[]>([])
  const [allStatusResult, setAllStatusResult] = useState<SearchHsitory[]>([])
  const [searchAble, setSearchAble] = useState<boolean>(false)
  useEffect(() => {
    setCurSearchHistory(filteredSearchHistory)
  }, [filteredSearchHistory])

  useEffect(() => {
    let _onlySuccessData = filterSearchHistory(curSearchHistory, [
      'completed',
      'running',
      'pending'
    ])
    let _allStatusResult = filterSearchHistory(
      curSearchHistory,
      allSearchStatus
    )
    setOnlySuccessData(_onlySuccessData)
    setAllStatusResult(_allStatusResult)
  }, [curSearchHistory])

  useEffect(() => {
    if (!isValidArray(retroProcessUpdates)) return
    cacheRetroProcessUpdates([])
    const newHistory = curSearchHistory.map((o) => {
      return retroProcessUpdates[0]?.retro_id === o.retro_id
        ? { ...o, ...retroProcessUpdates[0], newMsgDot: true }
        : o
    })
    // const targetHistoryInfo = newHistory.find(
    //   (e) => e?.id === curHistoryInfo?.id
    // )
    // if (targetHistoryInfo) {updateCurHistoryInfo(targetHistoryInfo)}
    setCurSearchHistory(newHistory)
  }, [retroProcessUpdates])

  const { initialState } = useModel('@@initialState')
  const [showFooter, setShowFooter] = useState<boolean>(true)
  const [isSuccessData, setIsSuccessData] = useState<boolean>(false)
  useEffect(() => {
    getTargetMolecule(moleculeId)
  }, [moleculeId])

  const showSearchHistory = isValidArray(searchHistoryData)

  const { showLauncher, isOpen, sendMessage } = useModel('commend')
  const context = useRouteEventContext()
  const [searchParams] = useSearchParams()
  const [aiGenerateModelOpen, setAiGenerateModelOpen] = useState<boolean>(false)
  const [aiGenerating, setAiGenerating] = useState<boolean>(false)
  const getTabName = (tab: RouteType): string => {
    switch (tab) {
      case 'aiGenerated': {
        const backboneNumber = curAiRouteNum || ''
        if (Number.isInteger(backboneNumber)) {
          return `${getWord(tab)} (${backboneNumber})`
        }
        return getWord(tab)
      }
      case 'myRoutes':
        if (targetMolecule?.project_routes_number) {
          return `${getWord(tab)} (${targetMolecule.project_routes_number})`
        }
        return getWord(tab)
      default:
        return ''
    }
  }

  const [getFilterEvent, setGetFilterEvent] = useState<
    SearchParamProps['getFilterEvent']
  >({})

  const { pathname } = useLocation()
  useEffect(() => {
    setCurSearchHistory([])
    resetFilterInfo(true)
    getSearchHistory(moleculeId as string)
    return () => {
      resetCompoundData()
      if (isOpen) showLauncher()
    }
  }, [])

  useUpdateEffect(() => {
    if (
      pathname.includes('/compound') &&
      targetMolecule?.project_routes_number &&
      targetMolecule?.project_routes_number > 0 &&
      !searchParams.get('tab')
    ) {
      updateRouteType('myRoutes')
    }
  }, [targetMolecule])

  const [filterTag, setFilterTag] = useState<ITag>()
  const [openFilter, setOpenFilter] = useState<boolean>(false)
  const handleFilter = (_tag: ITag) => {
    setFilterTag(_tag)
    setOpenFilter(true)
  }
  const isAiGeneratedTab = routeType !== 'aiGenerated'
  const isDefaultRouteSetting = isEqual(
    {
      safety_score: setting?.retro?.safety_score,
      novelty_score: setting?.retro?.novelty_score,
      price_score: setting?.retro?.price_score
    },
    retroParamsConfig
  )

  const isDefaultChoose =
    isEqual(
      defatulFilterInfo,
      pick(curFilterInfo, [
        'group',
        'knownReactionRateRange',
        'scoreRange',
        'similarId',
        'sortFiled',
        'sortOrder'
      ])
    ) &&
    (curFilterInfo?.backboneLengthRange === backboneRange ||
      !isValidArray(backboneRange)) &&
    (curFilterInfo?.mainTreeSteps === mainTreeStepsRange ||
      !isValidArray(mainTreeStepsRange))

  const HeadExtra = () => {
    if (isAiGeneratedTab) {
      return access?.authCodeList?.includes('compound.button.new-route') ? (
        <Button
          type="primary"
          block
          size="middle"
          className="createRouteButton"
          onClick={() =>
            history.push(`/projects/${projectId}/compound/${moleculeId}/create`)
          }
          disabled={!smiles}
        >
          <PlusOutlined />
          {getWord('new-route')}
        </Button>
      ) : (
        ''
      )
    } else {
      return (
        <div className="flex-align-items-center">
          {!isNil(curFilterInfo?.group_conditions?.start_material) &&
          !isNil(curFilterInfo.similarId) ? (
            <Tag
              key="similarTag"
              className={styles.similarTag}
              closable
              color="pink"
              onClose={() => resetFilterInfo()}
            >
              {`${getWord('group')}${
                curFilterInfo?.group_conditions?.start_material + 1
              }`}
            </Tag>
          ) : (
            ''
          )}
          {curHistoryInfo?.status === 'completed' && (
            <Popover
              // placement="topRight"
              placement="left"
              overlayClassName={styles.routeSort}
              content={getWord('route-sort-dimension-weight-setting')}
            >
              <div
                className={cs(styles.settings, 'flex-center', {
                  [styles['filtered']]: !isDefaultRouteSetting
                })}
                onClick={() => handleFilter('settings')}
              >
                <SettingsIcon
                  fill={isDefaultRouteSetting ? '#191919' : '#0047BB'}
                />
              </div>
            </Popover>
          )}
          {access?.authCodeList?.includes('compound.button.filter') && (
            <>
              <div
                key="compoundFilter"
                className={cs(styles.filterContent, 'flex-center')}
                onClick={() => handleFilter('filter')}
              >
                <FilterIcon fill={isDefaultChoose ? '#191919' : '#108ee9'} />
              </div>
              <span>
                {isDefaultChoose ? (
                  ''
                ) : (
                  <Tag
                    className={cs(styles.topScore, 'flex-center')}
                    closable
                    color="blue"
                    onClose={(e) => {
                      e.preventDefault()
                      resetFilterInfo()
                    }}
                  >
                    filtered
                  </Tag>
                )}
              </span>
            </>
          )}
        </div>
      )
    }
  }

  const displayTypes =
    targetMolecule?.type === 'temp_block'
      ? { aiGenerated: { name: getWord('aiGenerated') } }
      : {
          aiGenerated: { name: getWord('aiGenerated') },
          myRoutes: { name: getWord('myRoutes') },
          reaction: { name: getWord('reaction') }
        }

  const getTabs = () => {
    /* && access?.authCodeList?.includes('compound.tab.aiGenerated') */
    let newTabs = []
    Object.entries(displayTypes).map(([key]) =>
      access?.authCodeList?.includes(`compound.tab.${key}`)
        ? newTabs.push({
            key,
            tab: getTabName(key as RouteType)
          })
        : ''
    )
    return newTabs
  }

  const switchSuccessData = async (checked: boolean) => {
    setIsSuccessData(checked)
  }

  const [curSearchLog, setCurSearchLog] = useState<SearchLog[]>([])
  const SearchHistory = ({ data }: { data: SearchHsitory[] }) => (
    <>
      {isValidArray(data)
        ? data?.map((e: SearchHsitory, index: number) => {
            return (
              <SearchHsitoryCard
                curData={e}
                searchLog={async () => {
                  const searchLog = await getSearchLogs(
                    moleculeId as string,
                    e?.retro_id
                  )
                  setCurSearchLog(searchLog)
                  confirm()
                }}
                getAiGenerateRoute={() => {
                  cacheFilterInfo({
                    curQueryId: e?.id,
                    curQueryStatus: e?.status
                  })
                  updateCurHistoryInfo(e)
                  removeNewMsgDot(curSearchHistory, e?.retro_id)
                }}
                key={`${e.id}-${index}`}
                openAiGenerateInfo={() => {
                  setShowFooter(false)
                  setViewingRetroId(e.retro_id)
                  setViewingParams(e?.params as RouteSearchParams)
                  setAiGenerateModelOpen(true)
                }}
              />
            )
          })
        : null}
    </>
  )
  return (
    <RouteEventContext.Provider value={context}>
      <Launcher
        onMessageWasSent={sendMessage}
        hiddenLauncher={showLauncher}
        isOpen={isOpen}
      />
      <FilterDrawer
        open={openFilter}
        filterTag={filterTag as ITag}
        onClose={() => setOpenFilter(false)}
        values={{
          knownReactionRateRange: curFilterInfo?.knownReactionRateRange || [
            0, 100
          ],
          scoreRange: curFilterInfo?.scoreRange || [0, 100],
          backboneLengthRange: curFilterInfo?.backboneLengthRange || [],
          mainTreeSteps: curFilterInfo?.mainTreeSteps || []
        }}
      />
      <PageContainer className="target-molecule-detail-root">
        <Content className="body">
          <div className={cs('left-side', { none: isAiGeneratedTab })}>
            <Card
              size="small"
              className="target-card"
              bordered
              title={getWord('target-molecules')}
            >
              {!smiles?.length ? (
                getWord('noticeIcon.empty')
              ) : (
                <>
                  <MoleculeStructure structure={smiles} height={150} />
                  <Typography.Text
                    style={{ width: isEN() ? 115 : 162 }}
                    copyable
                    ellipsis={{ tooltip: targetMolecule?.no }}
                  >
                    {targetMolecule?.no}
                  </Typography.Text>
                  <div className="display-flex targetMoleculeInfo">
                    {targetMolecule?.type && (
                      <Tag
                        key="targetMoleculeType"
                        className="targetMoleculeType"
                        onClose={() => resetFilterInfo()}
                      >
                        {typeMap[targetMolecule.type]}
                      </Tag>
                    )}
                    <Tag
                      key="targetMoleculeStatus"
                      className="targetMoleculeStatus"
                      onClose={() => resetFilterInfo()}
                    >
                      <WordParser
                        word={targetMolecule?.status as ProjectType}
                      />
                    </Tag>
                  </div>
                </>
              )}
            </Card>
            {access?.authCodeList?.includes('compound.button.gnerate-routes') &&
            !isReadonlyMolecule(undefined, targetMolecule?.status) &&
            targetMolecule?.id ? (
              <div className="buttons-wrapper">
                <Button
                  type="primary"
                  block
                  size="middle"
                  className="action-button flex-center"
                  onClick={() => setAiGenerateModelOpen(true)}
                >
                  <GnerateRoutesIcon width={16} fill="#fff" />
                  &nbsp;
                  {getWord('gnerate-routes')}
                </Button>
              </div>
            ) : (
              ''
            )}
            {showSearchHistory && (
              <>
                {getWord('results')}：{curSearchHistory?.length}
                {isEN() ? '' : '条'}
                <div className="flex-align-items-center" key="search-switch">
                  {getWord('success-results')}
                  &nbsp;
                  <Switch
                    key="search-switch-item"
                    size="small"
                    onChange={switchSuccessData}
                    checked={isSuccessData}
                  />
                </div>
                <div className="searchHistoryContent">
                  {isSuccessData ? (
                    <SearchHistory data={onlySuccessData} />
                  ) : (
                    <SearchHistory data={allStatusResult} />
                  )}
                </div>
              </>
            )}
          </div>
          {targetMolecule ? (
            <Card
              className="routes-list-wrapper"
              tabList={getTabs()}
              activeTabKey={routeType}
              onTabChange={(key) => updateRouteType(key as RouteType)}
              extra={<HeadExtra />}
            >
              <SyntheticRoutes
                moleculeId={Number(moleculeId)}
                isSearching={targetMolecule?.searching}
                openAiGenerateModel={() => setAiGenerateModelOpen(true)}
              />
            </Card>
          ) : (
            ''
          )}
        </Content>
        <ModalBase {...dialogProps} title={getWord('log')} footer={null}>
          {curSearchLog?.map((item: SearchLog, index: number) => (
            <Row
              className={cs('flex-align-items-center', styles.logText)}
              key={`search-log-${index}`}
            >
              <Col span={8}>{formatYTSTime(item?.event_time as number)}</Col>
              <Col span={16}>{item?.event_msg}</Col>
            </Row>
          ))}
        </ModalBase>
        <Modal
          open={aiGenerateModelOpen}
          wrapClassName={cs({ 'no-footer': !showFooter })}
          onCancel={() => {
            setAiGenerating(false)
            setViewingParams(undefined)
            setViewingRetroId(undefined)
            setAiGenerateModelOpen(false)
            setShowFooter(true)
            setGetFilterEvent(undefined)
          }}
          okButtonProps={{ disabled: !searchAble }}
          okText={getWord('submit')}
          confirmLoading={aiGenerating}
          centered
          width={510}
          destroyOnClose
          onOk={() => {
            setGetFilterEvent({
              onGetFilters: async (value) => {
                setAiGenerating(true)
                const { data, error } = await service<RetroProcesses>(
                  'retro-processes'
                ).create({
                  project_compound: toInt(moleculeId), // 卡片ID
                  creator_id: initialState?.userInfo?.username,
                  params: value
                })
                if (!error && data) {
                  message.success({
                    message: getWord('search-been-created'),
                    description: ''
                  })
                  getSearchHistory(moleculeId as string)
                  getTargetMolecule(moleculeId)
                } else if (error) {
                  message.error({
                    message: getWord('route-generate-failed'),
                    description: `${getWord('error-detail')}${JSON.stringify(
                      error?.message
                    )}`
                  })
                }
                setAiGenerating(false)
                setAiGenerateModelOpen(false)
                setGetFilterEvent({})
              }
            })
          }}
        >
          <SearchParam
            getFilterEvent={getFilterEvent}
            viewOnly={viewingParams}
            retroId={viewingRetroId}
            target={smiles}
            onEdit={() => setShowFooter(true)}
            onLoading={(loading) => setSearchAble(!loading)}
          />
        </Modal>
      </PageContainer>
    </RouteEventContext.Provider>
  )
}
export default CompoundDetail
