@import '@/style/variables.less';
.commonPostion {
  position: relative;
  bottom: 5px;
}
.similarTag {
  .commonPostion;
}
.topScore {
  position: relative;
  bottom: 5px;
  :global {
    .ant-radio-button-wrapper {
      color: #626262;
    }
    .ant-radio-button-wrapper-checked {
      color: @text-choosed;
      border-color: @text-choosed;
    }
  }
}
.routeSort {
  :global {
    .ant-popover-title {
      min-width: 80px !important;
    }
  }
}
.filtered {
  background: #e5edf8;
  border: 1px solid #99b5e4 !important;
}

.settings {
  position: relative;
  top: -4px;
  width: 24px;
  height: 24px;
  border: 1px solid #dbdbdb;
  border-radius: 2px;
  cursor: pointer;
  svg {
    width: 14px;
  }
}
.filterContent {
  .commonPostion;
  width: 30px;
  height: 30px;
  margin-left: 5px;
  svg {
    width: 16px;
    height: 18px;
  }
}

.filterContent:hover {
  cursor: pointer;
}

.target-molecule-detail-root {
  .ant-layout-content {
    height: @main-content-height;
  }
  overflow: auto;
  .compound-body {
    display: flex;
    flex-direction: row;
    .left-side {
      display: flex;
      flex: 0 0 auto;
      flex-direction: column;
      width: 220px;
      height: 100%;
      .target-card {
        .targetMoleculeInfo {
          margin-top: 6px;
          .targetMoleculeType {
            margin-right: 8px;
            color: @color-completed;
            background-color: #ecf8ef;
            border: 1px solid @color-completed;
            border-radius: 1px;
          }
          .targetMoleculeStatus {
            color: @color-pending;
            border: 1px solid @color-pending;
            border-radius: 1px;
          }
        }
      }
      .buttons-wrapper {
        .action-button {
          height: 32px;
          margin: 10px 0;
          background: @fill-cursor;
          border-radius: 4px;
          border-radius: 2px;
        }
        .new-route-button {
          color: #027aff;
          background-color: #dce9fb;
          border-color: #dce9fb;
        }
      }
    }
  }

  .pagination {
    width: fit-content;
    margin-left: auto;
  }

  .routes-list-wrapper {
    display: flex;
    flex: 1 1 auto;
    flex-direction: column;
    margin-left: 16px;
    overflow: hidden;
    > .ant-card-body {
      flex: auto;
      height: auto;
      max-height: calc(@main-content-height_noHeader - 126px);
      margin-top: 4px;
      padding-top: 0px;
      padding-bottom: 8px;
      overflow: auto;
    }
    > .ant-card-head > .ant-card-head-wrapper {
      position: absolute !important;
      top: 3px !important;
      right: 10px !important;
      z-index: 1;
      width: auto;
      min-width: 300px;
    }
  }

  .createRouteButton {
    position: relative;
    top: -7px;
  }

  .searchHistoryContent {
    height: auto;
    max-height: calc(100vh - 499px);
    overflow-x: hidden;
    overflow-y: auto;
  }
}

.no-footer {
  .ant-modal-footer {
    display: none;
    background-color: red !important;
  }
}

.logText {
  width: 100%;
  height: 22px;
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 22px;
}
