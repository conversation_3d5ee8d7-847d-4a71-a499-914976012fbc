import { createContext, useMemo, useState } from 'react'

interface GenerateCompletedEvent {
  type: 'check-generation'
  processId: number
}
type RouteEvent = GenerateCompletedEvent

export interface Context {
  event?: RouteEvent
  trigger?: (e: RouteEvent) => void
}

export const useRouteEventContext = (): Context => {
  const [event, setEvent] = useState<RouteEvent>()
  return useMemo(
    () => ({ event, trigger: (e: RouteEvent) => setEvent(e) }),
    [event]
  )
}

export const RouteEventContext = createContext<Context>({})
