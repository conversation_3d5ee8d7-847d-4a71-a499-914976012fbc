import LazySmileDrawer from '@/components/LazySmileDrawer'
import useOptions from '@/hooks/useOptions'
import { ProjectCompound } from '@/services/brain'
import { getWord, isEN } from '@/utils'
import { Card, Col, Row, Space, Tag, Typography } from 'antd'
import cs from 'classnames'
import dayjs from 'dayjs'
import React from 'react'
import { history } from 'umi'
import styles from './index.less'

export interface CompoundCardProps {
  compound: ProjectCompound
}

const CompoundCard: React.FC<CompoundCardProps> = ({
  compound: projectCompound
}) => {
  const { typeMap } = useOptions()
  const {
    compound,
    project,
    id,
    no,
    type,
    retro_backbones_number,
    project_routes_number,
    updatedAt
  } = projectCompound
  const updateTime = dayjs(updatedAt).format('YYYY-MM-DD HH:mm:ss')

  return (
    <div className={styles.moleculeCardRoot}>
      <Card
        className={cs('enablePointer', styles.card)}
        onClick={() =>
          history.push(
            `/projects/${project?.id}/compound/${id}?page=1&pageSize=10`
          )
        }
      >
        <Row>
          <Col flex="auto">
            <LazySmileDrawer
              structure={compound?.smiles || ''}
              height={300}
              className={cs('enablePointer')}
            />
          </Col>
        </Row>
        <Row justify="space-between" align={'middle'} wrap={false}>
          <Col flex="auto">
            <Typography.Text strong ellipsis={{ tooltip: no }}>
              {no}
            </Typography.Text>
          </Col>
          <Col className={styles.tagsWrapper} flex="none">
            <Tag color="green">{typeMap[type]}</Tag>
          </Col>
        </Row>
        <Row gutter={12}>
          <Col className={cs(styles.routeNum, styles.label)} flex="none">
            <Space>
              <div>
                {getWord('aiGenerated')} {retro_backbones_number || 0}{' '}
                {isEN() ? '' : '条'}
              </div>
              <div>•</div>
              <div>
                {getWord('myRoutes')} {project_routes_number || 0}{' '}
                {isEN() ? '' : '条'}
              </div>
            </Space>
          </Col>
          <Col flex="auto" className={styles.updateTimeWrapper}>
            <Typography.Text ellipsis={{ tooltip: updateTime }}>
              {getWord('updated-at')}
              &nbsp;
              {updateTime}
            </Typography.Text>
          </Col>
        </Row>
      </Card>
    </div>
  )
}

export default CompoundCard
