import ButtonWithLoading from '@/components/ButtonWithLoading'
import { LazyKetcherEditor } from '@/components/MoleculeEditor'
import useKetcher from '@/hooks/useKetcher'
import useSearchParams from '@/hooks/useSearchParams'
import SearchParam from '@/pages/compound/components/SearchParam'
import {
  Project,
  ProjectCompound,
  query,
  RetroProcesses,
  service
} from '@/services/brain'
import { getWord } from '@/utils'
import { PageContainer, ProForm, ProFormText } from '@ant-design/pro-components'
import { history, useModel } from '@umijs/max'
import { App, Button, Space } from 'antd'
import { useForm } from 'antd/es/form/Form'
import cs from 'classnames'
import React, { useState } from 'react'
import styles from '../index.less'

const QuickSearch: React.FC = () => {
  const {
    initialState: { userInfo = undefined, isMenuCollapsed = false } = {}
  } = useModel('@@initialState')
  const { props, getSmiles } = useKetcher()
  const { props: searchProps, getParams } = useSearchParams()
  const { message } = App.useApp()
  const [form] = useForm<{ no?: string }>()
  const [searchAble, setSearchAble] = useState<boolean>(false)

  const getPersonalProjectId = async (): Promise<number> => {
    if (userInfo?.personal_project?.id) return userInfo.personal_project.id
    const pp = await query<Project>('projects/my').get()
    return (pp.data as unknown as Project)?.id
  }

  const search = async () => {
    try {
      const smiles = await getSmiles({ kekulize: false })
      if (!smiles) throw ''

      const personalProjectId = await getPersonalProjectId()
      if (!personalProjectId) throw ''

      const { data: compound, error: compoundError } =
        await service<ProjectCompound>('project-compounds').create({
          no: form.getFieldValue('no') || undefined,
          type: 'target',
          priority: 'P1',
          status: 'created',
          director_id: `${userInfo?.id}`,
          input_smiles: smiles,
          project: { id: personalProjectId }
        } as ProjectCompound)
      if (!compound?.id || compoundError) throw compoundError?.message

      const params = await getParams()
      const { data, error } = await service<RetroProcesses>(
        'retro-processes'
      ).create({
        project_compound: compound.id,
        creator_id: `${userInfo?.username}` || '',
        params
      })
      if (!data || error) throw error?.message
      history.push(`/projects/${personalProjectId}/compound/${compound.id}`)
    } catch (e) {
      message.error(e || getWord('search-failed-I'))
    }
  }

  const content = (
    <div className={styles.editorRoot}>
      <LazyKetcherEditor
        {...props}
        extraFormItem={
          <ProForm
            grid
            submitter={false}
            form={form}
            className={styles.moleculesNoInput}
          >
            <ProFormText
              label={getWord('molecules-no')}
              name="no"
              width="md"
              tooltip={getWord('max-l-30')}
              formItemProps={{
                rules: [{ max: 30, message: getWord('max-30-l') }]
              }}
              colProps={{ span: 4 }}
            />
          </ProForm>
        }
      />
    </div>
  )

  const extraContent = (
    <div className={styles.searchRoot}>
      <div className={styles.searchContent}>
        <SearchParam
          {...searchProps}
          getTarget={async () => {
            const smiles = await getSmiles({ validate: false })
            if (!smiles) return ''
            return smiles
          }}
          onLoading={(loading) => setSearchAble(!loading)}
        />
      </div>
      <div className={styles.buttonsRoot}>
        <Space className={styles.buttonsWrapper}>
          <Button
            key="cancel"
            onClick={() => history.push(`/experimental-zone`)}
          >
            {getWord('pages.experiment.label.operation.cancel')}
          </Button>
          <ButtonWithLoading
            key="confirm"
            type="primary"
            onClick={search}
            disabled={!searchAble}
          >
            {getWord('submit')}
          </ButtonWithLoading>
        </Space>
      </div>
    </div>
  )

  return (
    <PageContainer
      className={cs(styles.searchPageRoot, {
        [styles['unfoldWidth']]: !isMenuCollapsed,
        [styles['foldWidth']]: isMenuCollapsed
      })}
      content={content}
      extraContent={extraContent}
    />
  )
}

export default QuickSearch
