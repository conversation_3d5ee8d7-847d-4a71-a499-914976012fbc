import { ReactComponent as SearchRouteIcon } from '@/assets/svgs/route-operation/search-route.svg'
import StatusTip from '@/components/StatusTip'
import { useBrainFetch } from '@/hooks/useBrainFetch'
import { useFormStorage } from '@/hooks/useFormStorage'
import useOptions from '@/hooks/useOptions'
import { ProjectCompound, query } from '@/services/brain'
import { getWord } from '@/utils'
import {
  LightFilter,
  PageContainer,
  ProForm,
  ProFormSelect,
  ProList
} from '@ant-design/pro-components'
import { history, useAccess, useModel } from '@umijs/max'
import { ConfigProvider } from 'antd'
import Button from 'antd/es/button'
import { useForm } from 'antd/es/form/Form'
import React, { useEffect, useState } from 'react'
import { useCompoundFilterProps } from '../workspace/component/Filters/Compound'
import SortOrder, {
  SortOrderValue
} from '../workspace/component/Filters/SortOrder'
import CompoundCard from './CompoundCard'
import styles from './index.less'

interface CompoundFilter {
  no?: string
  sortBy: 'no' | 'updatedAt' | 'createdAt'
  sortOrder: SortOrderValue
}

const initFilter: CompoundFilter = {
  sortOrder: 'desc',
  sortBy: 'createdAt'
}

const ExperimentalZone: React.FC = () => {
  const { sortStandard } = useOptions()
  const access = useAccess()
  const { initialState: { userInfo = undefined } = {} } =
    useModel('@@initialState')

  const userId = userInfo?.id
  const personalProjectId = userInfo?.personal_project?.id || 0
  const compoundFilterProps = useCompoundFilterProps(
    userId,
    personalProjectId,
    true
  )
  const [form] = useForm<CompoundFilter>()
  const [listTotal, setListTotal] = useState<number>(0)
  const [filter, setFilter] = useState<CompoundFilter>()
  const { fetch } = useBrainFetch()
  const [get, set] = useFormStorage()

  useEffect(() => {
    form.setFieldsValue({ ...get(), ...initFilter })
  }, [])

  if (!userId) return null
  const request = async (
    params: Partial<CompoundFilter & { current: number; pageSize: number }>
  ) => {
    if (personalProjectId === 0) {
      setListTotal(0)
      return { data: [], total: 0, success: true }
    }
    const p = { ...initFilter, current: 1, pageSize: 20, ...params }
    const req = query<ProjectCompound>('project-compounds')
      .equalTo('director_id', userId)
      .paginate(p.current, p.pageSize)
      .sortBy([{ field: p.sortBy, order: p.sortOrder }])
      .populateWith('project_routes', ['id'])
      .populateWith('project', ['id'])
      .filterDeep('project.id', 'eq', personalProjectId)
      .populateWith('compound', ['smiles'])
      .notEqualTo('type', 'temp_block')
      .populateDeep([
        {
          path: 'retro_processes',
          fields: ['id'],
          children: [{ key: 'retro_backbones', fields: ['id'] }]
        }
      ])
    if (p.no) req.equalTo('no', p.no)
    const { data, meta } = await fetch(req.get())
    data?.forEach((item) => {
      item.project_routes_number = item.project_routes?.length
      item.retro_backbones_number = item.retro_processes?.flatMap(
        (p) => p.retro_backbones
      ).length
    })
    setListTotal(meta?.pagination.total)
    return { data: data || [], total: meta?.pagination.total, success: !!data }
  }
  const onUpdateFilter = async (filter: CompoundFilter) => {
    setFilter(filter)
  }

  const filterComp = (
    <div className="flex-align-items-center">
      {getWord('molecules-no')}&nbsp;&nbsp;
      <LightFilter
        bordered
        onFinish={onUpdateFilter}
        form={form}
        onValuesChange={(_, v) => set(v)}
      >
        <ProFormSelect {...compoundFilterProps} />
      </LightFilter>
    </div>
  )

  const sortComp = (
    <LightFilter
      bordered
      form={form}
      onFinish={onUpdateFilter}
      className={styles.sort}
      onValuesChange={(_, v) => set(v)}
    >
      <ProFormSelect
        name="sortBy"
        placeholder="排序方式"
        valueEnum={sortStandard}
        allowClear={false}
        fieldProps={{
          popupMatchSelectWidth: false,
          onClick: (e) => e.stopPropagation()
        }}
      />
      <ProForm.Item name="sortOrder">
        <SortOrder />
      </ProForm.Item>
    </LightFilter>
  )

  const listComp = (
    <ConfigProvider
      renderEmpty={() => (
        <StatusTip
          des={getWord('experimental-zone-empty-tip')}
          wrapperClassName={styles.experimentalEmpty}
        />
      )}
    >
      <ProList<ProjectCompound>
        ghost
        pagination={
          listTotal > 0
            ? { defaultPageSize: 20, showSizeChanger: false }
            : false
        }
        showActions="hover"
        rowSelection={false}
        grid={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 4, xxl: 4 }}
        renderItem={(item) => <CompoundCard compound={item} />}
        request={request}
        params={filter}
      />
    </ConfigProvider>
  )

  return (
    <PageContainer
      className={styles.rootContainer}
      fixedHeader
      header={{
        extra: access?.authCodeList?.includes('EZ.button.search') ? (
          <Button
            type="primary"
            icon={<SearchRouteIcon />}
            className={styles.searchButton}
            onClick={() => history.push('/experimental-zone/search')}
          >
            {getWord('menu.list.experimental-zone.search')}
          </Button>
        ) : null
      }}
      content={filterComp}
      extraContent={sortComp}
    >
      {listComp}
    </PageContainer>
  )
}

export default ExperimentalZone
