@import '@/style/variables.less';

.experimentalEmpty {
  min-height: calc(100vh - @footer-height - 206px);
  background-color: transparent;
}

.rootContainer {
  :global {
    .ant-pro-page-container-affix {
      background-color: #fff !important;
    }
    .ant-page-header .ant-page-header-heading {
      display: flex;
      margin-right: 12px;
    }
  }
  .searchButton {
    display: inline-flex;
    gap: 4px;
    align-items: center;
    height: 32px;
    border-radius: 2px !important;
    svg {
      width: 16px;
      height: 16px;
      g {
        fill: white;
      }
    }
  }

  .sort {
    margin-right: 12px;
    :global {
      .ant-pro-form-light-filter-container {
        justify-content: end;
      }
    }
  }
}

.unfoldWidth {
  width: 100%;
  max-width: calc(
    100vw - @menu-width - @scrollbar-width - @layout-horizontal-padding
  ) !important;
  overflow: hidden;
  --content-width: calc(
    100vw - @menu-width - @scrollbar-width - @layout-horizontal-padding - 314px -
      8px
  );
  :global {
    .ant-pro-page-container-main {
      width: var(--content-width) !important;
    }
  }
  .editorRoot {
    width: var(--content-width) !important;
  }
}
.foldWidth {
  --fold-content-width: calc(
    100vw - @fold-menu-width - @layout-horizontal-padding - @scrollbar-width -
      314px - 8px
  );
  width: calc(
    100vw - @fold-menu-width - @layout-horizontal-padding - @scrollbar-width
  ) !important;
  overflow: hidden;
  :global {
    .ant-pro-page-container-main {
      width: var(--fold-content-width) !important;
    }
  }
  .editorRoot {
    width: var(--fold-content-width) !important;
  }
}

.searchPageRoot {
  .editorRoot {
    position: relative;
    .moleculesNoInput {
      :global {
        .ant-form-item {
          width: 130px !important;
        }
      }
    }
  }
  :global {
    .ant-pro-page-container-detail {
      width: @menu-width;
    }
    .ant-pro-page-container-extraContent {
      width: 322px;
      max-width: 322px;
      height: 100%;
      margin-inline-start: 16px;
    }
  }
  .searchRoot {
    display: flex;
    flex-direction: column;
    width: 314px;
    max-width: 314px;
    height: 100%;
    padding: 20px 20px 6px 20px;
    overflow-x: hidden;
    overflow-y: scroll;
    text-align: start;
    background-color: @fill-base;
    form {
      background-color: inherit;
    }
    .searchContent {
      flex: 1;
    }
    .buttonsRoot {
      height: 32px;
      .buttonsWrapper {
        justify-content: end;
        width: 100%;
        margin-left: auto;
        button {
          width: 133px;
          height: 32px;
          border-radius: 2px;
        }
        :global {
          .ant-btn-primary {
            background: @fill-cursor !important;
          }
        }
      }
    }
  }
}

.moleculeCardRoot {
  min-height: 426px;
  margin: 4px;
  .card {
    :global .ant-card-body {
      padding: 4px 12px;
      border-radius: 4px;
    }
  }

  .routeNum {
    min-width: 38px;
  }
  .label {
    min-width: 48px;
    margin: 4px 0;
    color: #626262;
    font-weight: 400;
    font-size: 14px;
  }
  .tagsWrapper {
    :global {
      .ant-tag:last-child {
        margin-right: 0;
      }
    }
  }
  .alignRight {
    margin-left: auto;
  }

  .updateTimeWrapper {
    display: inline-flex;
    align-items: center;
  }

  .desItem {
    min-width: 206px;
    padding-bottom: 8px;
    overflow-x: hidden;
    :global {
      .ant-tag {
        margin-inline-end: 0px !important;
      }
    }
  }
  .routesAmount {
    .desItem;
    padding-bottom: 0px;
  }
  :global {
    .ant-card-head {
      padding: 0 12px;
    }
    .ant-card-body {
      padding: 4px 16px 2px;
    }
  }
  .moleculeNo {
    color: black;
    font-weight: 500;
    font-size: 16px;
  }
  .valueItem {
    color: @brand-primary;
  }
  .normalText {
    color: black;
  }
}
