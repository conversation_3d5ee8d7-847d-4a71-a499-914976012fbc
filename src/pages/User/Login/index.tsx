import { ReactComponent as LogoIcon } from '@/assets/svgs/logo.svg'
import Footer from '@/components/Footer'
import SwichLang from '@/components/SwitchLang'
import {
  apiLoginLocal,
  currentUser as loginRequest,
  parseResponseResult,
  updateEnvConfig
} from '@/services'
import { getEnvConfig, setToken } from '@/utils'
import { LockOutlined, UserOutlined } from '@ant-design/icons'
import { LoginForm, ProFormText } from '@ant-design/pro-components'
import { useEmotionCss } from '@ant-design/use-emotion-css'
import { Alert, message } from 'antd'
import axios from 'axios'
import cs from 'classnames'
import React, { useState } from 'react'
import { flushSync } from 'react-dom'
import { Helmet, history, useIntl, useModel } from 'umi'
import Settings from '../../../../config/defaultSettings'
import styles from './index.less'
const Lang = () => {
  return (
    <div className={styles.lang} data-lang>
      <SwichLang />
    </div>
  )
}

const LoginMessage: React.FC<{
  content: string
}> = ({ content }) => {
  return (
    <Alert
      className={styles.loginMessage}
      message={content}
      type="error"
      showIcon
    />
  )
}

const Login: React.FC = () => {
  const intl = useIntl()

  const [userLoginState, setUserLoginState] = useState<API.LoginResult>({})
  const { initialState, setInitialState } = useModel('@@initialState')
  const containerClassName = useEmotionCss(() => {
    return {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage:
        "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
      backgroundSize: '100% 100%',
      position: 'relative'
    }
  })

  updateEnvConfig()

  const fetchUserInfo = async () => {
    let redirectHref = sessionStorage.getItem('redirectHref') as string
    if (redirectHref) window.location.href = redirectHref
    const newUserInfo = await initialState?.queryUserInfo?.()
    if (newUserInfo) {
      flushSync(() => {
        setInitialState((preInitialState) => ({
          ...preInitialState,
          userInfo: newUserInfo
        }))
        const urlParams = new URL(window.location.href).searchParams
        message.success(intl.formatMessage({ id: 'pages.login.success' }))
        history.push(urlParams.get('redirect') || '/')
      })
    }
  }

  const getTokenInfo = (jwt: string) => {
    if (jwt) setToken(jwt)
    fetchUserInfo()
    return
  }

  const handleSubmit = async (values: API.LoginParams) => {
    const { username, password } = values
    const { keycloakSecret, keycloakDomain, useLocalLogin } = getEnvConfig()
    if (useLocalLogin) {
      const res = await apiLoginLocal({
        data: { identifier: username, password }
      })
      if (!parseResponseResult(res).ok) return
      getTokenInfo(res?.data?.jwt)
    } else {
      const params = new URLSearchParams()
      const baseUrl = keycloakDomain?.length ? `https://${keycloakDomain}` : ''
      params.append('client_secret', keycloakSecret as string)
      params.append('client_id', 'strapi')
      params.append('username', username as string)
      params.append('password', password as string)
      params.append('grant_type', 'password')
      axios
        .post(
          `${baseUrl}/auth/realms/strapi/protocol/openid-connect/token`,
          params,
          { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
        )
        .then(async (res) => {
          if (res && res?.data?.access_token) {
            // 会返回包含 access_token、refresh_token 等信息，access_token 可在线解析成明文数据：https://jwt.io/
            const jwtInfo = await loginRequest({
              params: { query: `access_token=${res?.data?.access_token}` }
            })
            if (jwtInfo?.data) getTokenInfo(jwtInfo?.data?.jwt)
          } else {
            setUserLoginState({ status: 'error' })
          }
        })
        .catch((error) => {
          const defaultLoginFailureMessage = intl.formatMessage({
            id: 'pages.login.failure'
          })
          console.log(error)
          message.error(defaultLoginFailureMessage)
        })
    }
  }

  return (
    <div className={cs(containerClassName)}>
      <Helmet>
        <title>
          {intl.formatMessage({ id: 'menu.login' })}- {Settings.title}
        </title>
      </Helmet>
      <div className={styles.loginForm}>
        <Lang />
        <LoginForm
          contentStyle={{
            minWidth: 280,
            maxWidth: '75vw'
          }}
          logo={
            <LogoIcon width={42} height={42} className={styles.loginIcon} />
          }
          title="Labwise"
          subTitle="Build AI-Native R&D Labs"
          initialValues={{
            autoLogin: true
          }}
          onFinish={async (values) =>
            await handleSubmit(values as API.LoginParams)
          }
        >
          {userLoginState?.status === 'error' && (
            <LoginMessage
              content={intl.formatMessage({
                id: 'pages.login.accountLogin.errorMessage'
              })}
            />
          )}
          <ProFormText
            name="username"
            fieldProps={{
              size: 'large',
              prefix: <UserOutlined />
            }}
            placeholder={intl.formatMessage({
              id: 'pages.login.username.required'
            })}
            rules={[
              {
                required: true,
                message: intl.formatMessage({
                  id: 'pages.login.username.required'
                })
              }
            ]}
          />
          <ProFormText.Password
            name="password"
            fieldProps={{
              size: 'large',
              prefix: <LockOutlined />
            }}
            placeholder={intl.formatMessage({
              id: 'pages.login.password.required'
            })}
            rules={[
              {
                required: true,
                message: intl.formatMessage({
                  id: 'pages.login.password.required'
                })
              }
            ]}
          />
        </LoginForm>
      </div>
      <Footer />
    </div>
  )
}
export default Login
