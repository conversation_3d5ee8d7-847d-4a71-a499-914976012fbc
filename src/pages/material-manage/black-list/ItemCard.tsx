import LazySmileDrawer from '@/components/LazySmileDrawer'
import Button<PERSON>ith<PERSON>onfirm from '@/pages/projects/components/ButtonWithConfirm'
import { service } from '@/services/brain'
import { MaterialBlackList } from '@/services/brain/types/material-black-list'
import { getWord } from '@/utils'
import { DeleteOutlined } from '@ant-design/icons'
import { App, Card, Col, Row, Typography } from 'antd'
import Paragraph from 'antd/lib/typography/Paragraph'
import dayjs from 'dayjs'
import React, { useState } from 'react'
import styles from './index.less'

export interface ItemCardProps {
  item: MaterialBlackList
  onUpdate?: () => void
}

const ItemCard: React.FC<ItemCardProps> = ({ item, onUpdate }) => {
  const [expanded, setExpanded] = useState<boolean>(false)
  const { message } = App.useApp()

  const remove = async () => {
    const { error } = await service<MaterialBlackList>(
      'material-black-lists'
    ).deleteOne(item.id)
    if (error) {
      message.error(error.message)
    } else {
      message.success('删除成功')
      onUpdate?.()
    }
  }

  const expandText = (
    <Typography.Text
      className={styles.expandText}
      onClick={(e) => {
        e.stopPropagation()
        setExpanded((pre) => !pre)
      }}
    >
      {expanded
        ? getWord('component.tagSelect.collapse')
        : getWord('component.tagSelect.expand')}
    </Typography.Text>
  )

  return (
    <div className={styles.blackItemCardRoot}>
      <Card size="small">
        <Row>
          <Col flex="auto">
            <LazySmileDrawer structure={item.inchified_smiles} height={300} />
          </Col>
        </Row>
        <Row justify="space-between" align={'middle'} wrap={false}>
          <Col flex="none">
            <Typography.Text>
              {getWord('pages.experiment.label.owner')}：
              {item.creator?.username}
            </Typography.Text>
          </Col>
          <Col flex="auto" className={styles.crateTime}>
            <Typography.Text ellipsis={{ tooltip: true }}>
              {getWord('creation-time')}：
              {dayjs(item.createdAt).format('YYYY-MM-DD')}
            </Typography.Text>
          </Col>
        </Row>

        <Row align={'top'} wrap={false}>
          <Col flex="none">{getWord('reason')}：</Col>
          <Col flex="auto">
            <Paragraph
              className={styles.reason}
              ellipsis={
                expanded
                  ? false
                  : { rows: 1, expandable: true, symbol: expandText }
              }
            >
              {item.reason}
              {expanded && expandText}
            </Paragraph>
          </Col>
        </Row>

        <ButtonWithConfirm
          buttonProps={{
            className: styles.deleteButton,
            danger: true
          }}
          buttonText={<DeleteOutlined />}
          title="删除黑名单原料"
          description="确认删除该黑名单原料？"
          type="link"
          onConfirm={remove}
        />
      </Card>
    </div>
  )
}

export default ItemCard
