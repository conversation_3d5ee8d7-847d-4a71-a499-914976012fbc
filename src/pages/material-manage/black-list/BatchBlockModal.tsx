import ModalBase from '@/components/ModalBase'
import { ModalBaseProps } from '@/components/ModalBase/index.d'
import MoleculeStructure from '@/components/MoleculeStructure'
import { MaterialBlackList, service } from '@/services/brain'
import { UserBasicInfo } from '@/types/common'
import { getWord, isEN } from '@/utils'
import {  useModel } from '@umijs/max'
import { App, Col, Form, Input, Row, Space, Typography } from 'antd'
import React, { useState } from 'react'

export interface BatchBlockModalProps extends ModalBaseProps {
  smiles: string[]
}

const BatchBlockModal: React.FC<BatchBlockModalProps> = ({
  smiles: propSmiles,
  ...props
}) => {
  const { initialState: { userInfo = undefined } = {} } =
    useModel('@@initialState')
  const userId = userInfo?.id
  const smiles = Array.from(new Set(propSmiles))
  const [reason, setReason] = useState<string>()
  const { message, modal } = App.useApp()

  const add = async (smiles: string) => {
    const { error } = await service<Partial<MaterialBlackList>>(
      'material-black-lists'
    ).create({
      inchified_smiles: smiles,
      reason,
      creator: { id: userId } as UserBasicInfo
    })
    if (error) {
      throw error.message
    }
    return true
  }

  const batchAdd = async () => {
    if (!reason) {
      throw message.error(getWord('enter-reason'))
    }
    if (!smiles.length) {
      throw message.error(getWord('enter-molecule'))
    }

    const results = await Promise.allSettled(smiles.map(add))
    const successN = results.filter((r) => r.status === 'fulfilled').length
    const failedN = results.filter((r) => r.status === 'rejected').length

    setReason('')

    if (failedN === 0) {
      message.success(
        isEN()
          ? `Successfully Add ${successN} Materials to Material Blacklist`
          : `成功添加了${successN}个原料到黑名单`
      )
      return
    }
    if (successN > 0) {
      message.warning(
        isEN()
          ? `Successfully Add ${successN} Materials to Material Blacklist,Failed to add ${failedN} Materials to Material Blacklist`
          : `成功添加了${successN}个原料到黑名单，${failedN}个分子添加失败`
      )
    } else {
      message.error(
        isEN()
          ? `Failed to add ${failedN} Materials to Material Blacklist`
          : `${failedN}个分子添加失败`
      )
    }

    const failedSmilesReason = results.reduce<
      { smiles: string; reason: string }[]
    >((acc, cur, index) => {
      if (cur.status === 'rejected') {
        acc.push({ smiles: smiles[index], reason: cur.reason })
      }
      return acc
    }, [])

    modal.error({
      title: getWord('failed-add-material-blacklist'),
      cancelButtonProps: { hidden: true },
      content: (
        <>
          {failedSmilesReason.map(({ smiles, reason }) => (
            <Row key={smiles}>
              <Space size={40}>
                <MoleculeStructure
                  width={100}
                  height={100}
                  structure={smiles}
                />
                <Typography.Text type="danger">{reason}</Typography.Text>
              </Space>
            </Row>
          ))}
        </>
      )
    })
  }

  return (
    <ModalBase
      title={getWord('batch-add-blacklist')}
      width={800}
      onConfirm={batchAdd}
      onCancel={async () => setReason('')}
      {...props}
    >
      <Form.Item label={getWord('reson-add-blacklist')} required>
        <Input value={reason} onChange={(e) => setReason(e.target.value)} />
      </Form.Item>
      <Row>
        {smiles.map((s) => (
          <Col key={s}>
            <MoleculeStructure width={100} height={100} structure={s} />
          </Col>
        ))}
      </Row>
    </ModalBase>
  )
}

export default BatchBlockModal
