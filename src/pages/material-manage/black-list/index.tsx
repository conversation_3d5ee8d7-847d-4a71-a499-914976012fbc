import { queryWithDefaultOrder } from '@/services/brain'
import { MaterialBlackList } from '@/services/brain/types/material-black-list'
import { getWord } from '@/utils'
import { PageContainer, ProList } from '@ant-design/pro-components'
import { history } from '@umijs/max'
import Button from 'antd/es/button'
import React, { useState } from 'react'
import ItemCard from './ItemCard'

interface SearchParams {
  pageSize?: number
  current?: number
}

const BlackList: React.FC = () => {
  const [refetch, setRefetch] = useState<boolean>(false)
  const request = async ({ pageSize, current }: SearchParams) => {
    const { data, meta } = await queryWithDefaultOrder<MaterialBlackList>(
      'material-black-lists'
    )
      .populateWith('creator')
      .paginate(current || 1, pageSize || 12)
      .get()
    return { data: data || [], success: !!data, total: meta?.pagination.total }
  }

  const content = ' '
  const extraContent = (
    <Button
      type="primary"
      onClick={() => history.push('/material/black-list/add')}
    >
      {getWord('add-blacklist-material')}
    </Button>
  )
  const list = (
    <ProList<MaterialBlackList>
      ghost
      pagination={{ defaultPageSize: 12, showSizeChanger: false }}
      rowSelection={false}
      grid={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 4, xxl: 4 }}
      renderItem={(item) => (
        <ItemCard item={item} onUpdate={() => setRefetch(!refetch)} />
      )}
      request={request}
      params={{ refetch }}
    />
  )

  return (
    <PageContainer ghost content={content} extraContent={extraContent}>
      {list}
    </PageContainer>
  )
}

export default BlackList
