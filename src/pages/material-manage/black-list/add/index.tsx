import ButtonWithLoading from '@/components/ButtonWithLoading'
import { LazyKetcherEditor } from '@/components/MoleculeEditor'
import useKetcher from '@/hooks/useKetcher'
import { MaterialBlackList, service } from '@/services/brain'
import { UserBasicInfo } from '@/types/common'
import { getWord } from '@/utils'
import { PageContainer } from '@ant-design/pro-components'
import { App, Form, Input, Space } from 'antd'
import Button from 'antd/es/button'
import { useState } from 'react'
import { history, useModel } from 'umi'

export default function AddMolecule() {
  const { getSmiles, setSmiles, props } = useKetcher()

  const [reason, setReason] = useState<string>()
  const { message } = App.useApp()
  const { initialState: { userInfo = undefined } = {} } =
    useModel('@@initialState')
  const userId = userInfo?.id

  if (!userId) return null

  const submit = async () => {
    if (!reason) {
      message.error(getWord('enter-reason'))
      return false
    }

    const smiles = await getSmiles({ inchify: true })
    if (!smiles) {
      return false
    }

    const { error } = await service<Partial<MaterialBlackList>>(
      'material-black-lists'
    ).create({
      inchified_smiles: smiles,
      reason,
      creator: { id: userId } as UserBasicInfo
    })
    if (error) {
      message.error(error.message)
      return false
    } else {
      message.success(getWord('successfully-added'))
      setSmiles('')
      return true
    }
  }

  const left = (
    <Form.Item label={getWord('reson-add-blacklist')} required>
      <Input value={reason} onChange={(e) => setReason(e.target.value)} />
    </Form.Item>
  )
  const right = (
    <Space>
      <Button onClick={() => history.push('/material/black-list')}>
        {getWord('pages.experiment.label.operation.cancel')}
      </Button>
      <ButtonWithLoading type="primary" onClick={submit}>
        {getWord('confirm')}
      </ButtonWithLoading>
    </Space>
  )

  return (
    <PageContainer content={left} extraContent={right}>
      <LazyKetcherEditor {...props} />
    </PageContainer>
  )
}
