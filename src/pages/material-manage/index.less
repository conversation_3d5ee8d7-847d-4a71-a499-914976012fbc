.materialManage {
  :global {
    // .ant-table-container {
    //   font-size: 14px !important;
    //   // font-family: 'PinFang-Medium' !important;
    //   // font-family: 'NotoSans-Medium' !important;
    //   font-family: unset !important;
    //   line-height: 14px;
    //   // color: red !important;
    //   // background-color: red !important;
    // }
    .ant-pagination {
      padding-bottom: 15px;
    }
  }
  padding-bottom: 10px;
  .materialsFilter {
    min-height: 64px;
    margin-bottom: 2px;
    padding-right: 20px;
    padding-left: 20px;
    background-color: #fff;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    .filterContent {
      align-items: center;
      height: 48px;
    }
    .leftContent {
      display: flex;
      align-items: center;
      span {
        margin-right: 10px;
      }
      .searchSmiles {
        // position: relative;
        display: flex;
        // width: max-content;
        // background-color: red;
        .closeIcon {
          // position: absolute;
          position: relative;
          top: -10px;
          right: 8px;

          // background-color: pink;
        }
      }
    }

    .des {
      display: flex;
      align-items: center;
      height: 32px;
      span {
        margin-right: 10px;
      }
    }
  }
  :global {
    .ant-page-header {
      display: none;
    }
    .ant-form {
      border-radius: 0px !important;
    }
  }
}
