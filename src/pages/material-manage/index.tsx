import useOptions from '@/hooks/useOptions'
import { getWord } from '@/utils'
import cs from 'classnames'

import LazySmileDrawer from '@/components/LazySmileDrawer'
import StatusTip from '@/components/StatusTip'
import { initPagination } from '@/constants'
import {
  MaterialItem,
  MaterialLib,
  MaterialSearchParams,
  defaultPageSize,
  query
} from '@/services/brain'
import { IOption } from '@/types/common'
import { formatYTSTime, getTableScroll, isEN, segmentation } from '@/utils'
import { CloseCircleOutlined } from '@ant-design/icons'
import {
  ActionType,
  PageContainer,
  ProTable,
  ProTableProps
} from '@ant-design/pro-components'
import { history, useSearchParams } from '@umijs/max'
import { Button, ConfigProvider, Select, Space } from 'antd'
import { isNumber } from 'lodash'
import { useEffect, useRef, useState } from 'react'
import BatchBlockModal from './black-list/BatchBlockModal'
import styles from './index.less'
export default function MaterialManage() {
  const countRef = useRef<HTMLDivElement>(null)
  const { materialManageStauts } = useOptions()
  const actionRef = useRef<ActionType>()
  const [choosedMaterialsID, setChoosedMaterialsID] = useState<string>('all')
  const [choosedMaterials, setChoosedMaterials] = useState<MaterialLib>()
  const [materialsData, setMaterialsData] = useState<MaterialLib[]>([])
  const [materialsOptions, setMaterialsOptions] = useState([])
  const [materialTotal, setMaterialTotal] = useState<number>()
  const [blockSmiles, setBlockSmiles] = useState<string[]>([])
  const [openEvent, setOpenEvent] = useState<Record<string, boolean>>({})

  const getFilterOptions = async () => {
    const { data } = await query<MaterialLib>('material-libs', {})
      .populateWith('last_update_user')
      .get()
    setMaterialsData(data)
    let options: IOption[] = []
    data?.map((e) =>
      options.push({
        label: `${e?.name}（${e?.version}）`,
        value: e?.id
      })
    )
    setMaterialsOptions([
      {
        label: getWord('entire-material-lib'),
        value: 'all'
      },
      ...options
    ] as IOption[])
    return null
  }

  const getTotal = async (curMaterialsID: number | string) => {
    const req = await query<MaterialItem>('material-items', {}).paginate(1, 1)
    if (curMaterialsID && curMaterialsID !== 'all') {
      req.filterDeep('material_lib.id', 'eq', curMaterialsID) // 查询原料库
    }
    const { meta, error } = await req.get()
    if (!error) setMaterialTotal(meta?.pagination.total)
  }

  const [pagination, setPagination] = useState<{
    current: number
    pageSize: number
  }>(initPagination)

  const requestList: ProTableProps<
    MaterialItem,
    API.PageParams & MaterialSearchParams
  > = async (params, sort) => {
    // , _, _filter
    const {
      materialLibID,
      in_stock,
      cas_no,
      inchified_smiles,
      purity,
      name_zh,
      name_en
    } = params
    setPagination({ current: params.current, pageSize: params.pageSize })
    const req = query<MaterialItem>('material-items', {}).paginate(
      params.current || 1,
      params.pageSize || defaultPageSize
    )
    if (materialLibID && materialLibID !== 'all')
      req.filterDeep('material_lib.id', 'eq', materialLibID) // 查询原料库
    if (inchified_smiles) req.equalTo('inchified_smiles', inchified_smiles)
    if (purity) req.equalTo('purity', purity)
    if (name_zh) req.equalTo('name_zh', name_zh)
    if (name_en) req.equalTo('name_en', name_en)
    if (!(in_stock[0] && in_stock[1])) {
      if (in_stock[0]) req.equalTo('in_stock', in_stock[0])
      if (in_stock[1]) req.equalTo('in_stock', in_stock[1])
    }
    if (cas_no) req.equalTo('cas_no', cas_no)
    // TODO style 自定义组件 量 req.between('quantity',[1,3]) 自定义组件
    if (params?.maxQuantity && params?.minQuantity) {
      req.between('quantity', [params?.minQuantity, params?.maxQuantity])
    } else if (params?.minQuantity) {
      req.between('quantity', [params?.minQuantity, Infinity])
    } else if (params?.maxQuantity) {
      req.between('quantity', [0, params?.maxQuantity])
    }
    // TODO style 自定义组件 价格 req.between('price',[1,3])
    if (params?.maxPrice && params?.minPrice) {
      req.between('price', [params?.minPrice, params?.maxPrice])
    } else if (params?.minPrice) {
      req.between('price', [params?.minPrice, Infinity])
    } else if (params?.maxPrice) {
      req.between('price', [0, params?.maxPrice])
    }
    /*
    <Space.Compact size="large">
      <Input addonBefore={<SearchOutlined />} placeholder="large size" />
      <Input placeholder="another input" />
    </Space.Compact>
    */
    // TODO 到货时间 req.between('price',[1,3]) min_delivery_days [YY-MM-DD HH:MM:ss
    const { data, meta, error } = await req
      .populateWith('material_lib', ['name', 'version', 'description'])
      .sortBy([
        ...Object.entries(sort || {}).map(([key, order]) => ({
          field: key as keyof MaterialItem,
          order: order === 'ascend' ? ('asc' as const) : ('desc' as const)
        }))
      ])
      .get()
    if (error) throw error
    return { data: data, success: true, total: meta?.pagination.total }
  }

  const [initalTableHeight, setInitalTableHeight] = useState<number>(0)

  useEffect(() => {
    const _initalTableHeight = getTableScroll({ ref: countRef })
    setInitalTableHeight(_initalTableHeight)
    getFilterOptions()
  }, [])

  const columns: (ProColumns<ProjectListItem> &
    ProDescriptionsItemProps<ProjectListItem>)[] = [
    {
      title: getWord('structural'),
      dataIndex: 'canonical_smiles',
      hideInSearch: true,
      width: 180,
      render: (smiles: string) =>
        smiles ? (
          <LazySmileDrawer structure={smiles} className="smilesItem" />
        ) : (
          ''
        )
    },
    {
      title: 'CAS',
      sorter: true,
      width: 80,
      dataIndex: 'cas_no'
    },
    {
      title: getWord('chinese-name'),
      sorter: true,
      dataIndex: 'name_zh',
      width: 120,
      hideInTable: isEN(),
      hideInSearch: isEN()
    },
    {
      title: getWord('english-name'),
      sorter: true,
      dataIndex: 'name_en',
      width: 120
    },
    {
      title: getWord('min-amount'),
      dataIndex: 'minQuantity',
      valueType: 'digit',
      hideInSearch: true,
      hideInTable: true
    },
    {
      title: getWord('max-amount'),
      dataIndex: 'maxQuantity',
      valueType: 'digit',
      hideInSearch: true,
      hideInTable: true
    },
    {
      title: getWord('amount'),
      dataIndex: 'quantity',
      width: 60,
      hideInSearch: true,
      sorter: true,
      render: (_: string, record: MaterialItem) =>
        record?.quantity && record?.unit
          ? `${record?.quantity}${record?.unit}`
          : '-'
    },
    {
      title: getWord('min-price'),
      dataIndex: 'minPrice',
      hideInSearch: true,
      valueType: 'digit',
      hideInTable: true
    },
    {
      title: getWord('max-price'),
      dataIndex: 'maxPrice',
      hideInSearch: true,
      valueType: 'digit',
      hideInTable: true
    },
    {
      title: getWord('price'),
      dataIndex: 'price',
      valueType: 'digit',
      width: 60,
      sorter: true,
      hideInSearch: true,
      render: (_: string, record: MaterialItem) =>
        record?.price ? record?.price.toFixed(2) : '-'
    },
    {
      title: getWord('purity'),
      dataIndex: 'purity',
      width: 60,
      sorter: true
    },
    {
      title: getWord('spot-or-futures'),
      width: 100,
      valueType: 'checkbox',
      dataIndex: 'in_stock',
      sorter: true,
      initialValue: ['true', 'false'],
      valueEnum: {
        true: { text: getWord('spot') },
        false: { text: getWord('futures') }
      }
    },
    /* {
      title: '到货时限', NOTE 本期不做
      dataIndex: 'delivery_date',
      valueType: 'date',
      sorter: true,
      sorter: true
    }, */
    {
      title: getWord('source'),
      dataIndex: 'source',
      width: 110,
      sorter: true,
      hideInSearch: true,
      render: (text: string, { source_link }: MaterialItem) => (
        <a href={source_link}>{text}</a>
      )
    }
  ]

  const customEmpty = (name?: string) => {
    if (name !== 'Table') return
    return <StatusTip des={getWord('noticeIcon.empty')} />
  }

  const [searchParams, setSearchParams] = useSearchParams()
  let curSearchSmiles = searchParams.get('searchSmiles') as string
  return (
    <PageContainer className={cs(styles.materialManage)}>
      <div className={cs(styles.materialsFilter)}>
        <div className={cs(styles.filterContent, 'flex-justify-space-between')}>
          <div className={styles.leftContent}>
            <span>{getWord('raw-material-lib')}</span>
            <Select
              options={materialsOptions}
              value={choosedMaterialsID}
              style={{ width: '198px' }}
              onChange={(e) => {
                getTotal(e)
                const curMaterialsOption = materialsData.find(
                  (cur) => cur?.id === e
                )
                setChoosedMaterialsID(e)
                if (curMaterialsOption) setChoosedMaterials(curMaterialsOption)
                else setChoosedMaterials(null)
              }}
            />
            &nbsp;&nbsp;&nbsp;&nbsp;
            <div className={styles.searchSmiles}>
              <a
                onClick={() => history.push(`/material/manage/search-molecule`)}
              >
                {curSearchSmiles
                  ? `${getWord(
                      'menu.list.material-manage.search-molecule'
                    )}（${curSearchSmiles}）`
                  : getWord('menu.list.material-manage.search-molecule')}
              </a>
              {curSearchSmiles && (
                <CloseCircleOutlined
                  width={15}
                  style={{ color: '#eb2f96' }}
                  className={styles.closeIcon}
                  onClick={() => {
                    searchParams.delete('searchSmiles')
                    setSearchParams(searchParams)
                  }}
                />
              )}
            </div>
          </div>
          <Button className="hidden" onClick={() => {}}>
            {getWord('add-raw-material-lib')}
          </Button>
        </div>
        {choosedMaterials ? (
          <div className={styles.des}>
            {choosedMaterials?.version && (
              <span>
                {getWord('version')} {choosedMaterials?.version}
              </span>
            )}
            {choosedMaterials?.description && (
              <span>
                {getWord('pages.searchTable.titleDesc')}{' '}
                {choosedMaterials?.description}
              </span>
            )}
            <span>
              {getWord('nums-materials')}
              &nbsp;
              {isNumber(materialTotal) && materialTotal >= 0
                ? segmentation(materialTotal)
                : 0}
            </span>
            {choosedMaterials?.status && (
              <span>
                {getWord('status')}{' '}
                {materialManageStauts[choosedMaterials?.status]}
              </span>
            )}
            {choosedMaterials?.last_update_user &&
              choosedMaterials?.last_update_time && (
                <span>
                  {isEN() ? (
                    <>
                      updated by&nbsp;
                      {choosedMaterials?.last_update_user?.username}
                      &nbsp;at&nbsp;
                      {formatYTSTime(choosedMaterials?.last_update_time)}
                    </>
                  ) : (
                    <>
                      最新动态&nbsp;
                      {choosedMaterials?.last_update_user?.username}
                      &nbsp;于 &nbsp;
                      {formatYTSTime(choosedMaterials?.last_update_time)}&nbsp;
                      更新原料信息
                    </>
                  )}
                </span>
              )}
          </div>
        ) : (
          ''
        )}
      </div>
      <div ref={countRef}>
        <ConfigProvider renderEmpty={customEmpty}>
          <ProTable<MaterialItem, API.PageParams & MaterialSearchParams>
            params={{
              materialLibID: choosedMaterialsID,
              inchified_smiles: curSearchSmiles?.replace('+', '%2B')
            }}
            ghost
            request={requestList}
            actionRef={actionRef}
            rowKey="id"
            search={{ labelWidth: isEN() ? 140 : 120 }}
            columns={columns}
            rowSelection={{}}
            tableAlertOptionRender={({ selectedRows, onCleanSelected }) => (
              <Space>
                <a onClick={onCleanSelected}>{getWord('deselected')}</a>
                <a
                  onClick={() => {
                    setBlockSmiles(selectedRows.map((r) => r.inchified_smiles))
                    setOpenEvent({ open: true })
                  }}
                >
                  {getWord('batch-add-blacklist')}
                </a>
              </Space>
            )}
            toolBarRender={false}
            scroll={{ y: initalTableHeight }}
            pagination={pagination}
          />
        </ConfigProvider>
      </div>
      <BatchBlockModal smiles={blockSmiles} openEvent={openEvent} />
    </PageContainer>
  )
}
