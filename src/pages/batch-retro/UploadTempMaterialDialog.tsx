import { useBrainFetch } from '@/hooks/useBrainFetch'
import { query, StaticFile, UploadFile } from '@/services/brain'
import { getWord } from '@/utils'
import { InboxOutlined } from '@ant-design/icons'
import { ModalForm } from '@ant-design/pro-components'
import { useModel } from '@umijs/max'
import { App, Upload } from 'antd'
import { RcFile } from 'antd/es/upload'
import { UploadRequestOption } from 'rc-upload/lib/interface'
import React, { useEffect, useState } from 'react'
import { DownloadButton } from './DownloadButton'
import { deleteFile, startParseTempMaterialFile, uploadFile } from './request'

const { Dragger } = Upload

interface UploadResult {
  file: UploadFile
  fileContent: string | Blob | RcFile
}

const uploader = async ({
  onProgress,
  onError,
  onSuccess,
  filename,
  file
}: UploadRequestOption) => {
  let percent = 0
  const percentUpdaterId = setInterval(() => {
    onProgress?.({ percent: (percent += 1) })
  }, 500)
  const uploaded = await uploadFile(file)
  clearInterval(percentUpdaterId)

  if (!uploaded) {
    onError?.({ name: filename || '', message: 'upload file error' })
    return
  }
  onSuccess?.({ file: uploaded, fileContent: file })
}

const Uploader: React.FC<{
  onFileChange: (file?: UploadResult) => void
}> = ({ onFileChange }) => {
  const { message } = App.useApp()
  const { fetch } = useBrainFetch()
  const [file, setFile] = useState<StaticFile>()

  useEffect(() => {
    fetch(
      query<StaticFile>('static-file')
        .populateWith('temp_materials_template')
        .populateWith('current_temp_material_file')
        .populateWith('updating_temp_material_file')
        .get()
    ).then(({ data }) => {
      setFile(data as unknown as StaticFile)
    })
  }, [])

  const disabled = !!file?.updating_temp_material_file

  return (
    <Dragger
      accept=".xlsx"
      multiple
      disabled={disabled}
      maxCount={1}
      onChange={({ file: { name, status, response } }) => {
        if (status === 'done') {
          message.success({ content: `${name} ${getWord('upload-success')}` })
          onFileChange({
            file: response.file,
            fileContent: response.fileContent
          })
          return
        } else if (status === 'error') {
          message.error({ content: `${name} ${getWord('upload-failed')}` })
        }
        onFileChange()
      }}
      customRequest={uploader}
    >
      <p className="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      {disabled ? (
        <p className="ant-upload-text">
          {getWord('pages.batchRetro.label.parsingTempMaterials')}
        </p>
      ) : (
        <p className="ant-upload-text">{getWord('upload-text')}</p>
      )}
      <p className="ant-upload-hint">
        <DownloadButton
          file={file?.temp_materials_template}
          name={getWord('pages.batchRetro.label.downloadTemplate')}
        />
        <DownloadButton
          file={file?.current_temp_material_file}
          name={getWord('pages.batchRetro.label.downloadCurrentTempMaterials')}
        />
        <DownloadButton
          file={file?.updating_temp_material_file}
          name={getWord('pages.batchRetro.label.downloadUpdatingTempMaterials')}
        />
      </p>
    </Dragger>
  )
}

const UploadTempMaterialFileDialog: React.FC<{
  trigger: JSX.Element
  onFinished?: () => void
}> = ({ trigger, onFinished }) => {
  const { message } = App.useApp()
  const [open, setOpen] = useState<boolean>(false)
  const [result, setResult] = useState<UploadResult>()
  const { initialState } = useModel('@@initialState')
  const { userInfo } = initialState

  const updateResult = async (newResult?: UploadResult) => {
    if (result) {
      await deleteFile(result.file.id)
    }
    setResult(newResult)
  }
  const updateOpen = async (newOpen: boolean) => {
    if (!newOpen) {
      await updateResult()
    }
    setOpen(newOpen)
  }

  return (
    <ModalForm
      trigger={trigger}
      title={getWord('pages.batchRetro.label.updateTempMaterial')}
      open={open}
      onOpenChange={updateOpen}
      modalProps={{ destroyOnClose: true }}
      submitter={{ submitButtonProps: { disabled: !result?.file } }}
      onFinish={async () => {
        if (!result?.file.url || !result.file.id) return
        const task = await startParseTempMaterialFile(
          result.file.id,
          result.fileContent,
          userInfo.id
        )
        if (task === true) {
          message.success({
            content: getWord('pages.batchRetro.label.startUpdateTempMaterial')
          })
          onFinished?.()
          setResult(undefined)
          return true
        }
        console.error(task)
        message.error({
          content: `${getWord(
            'pages.batchRetro.label.failedToStartUpdateTempMaterial'
          )}, ${task}`
        })
        return
      }}
    >
      <Uploader onFileChange={updateResult} />
    </ModalForm>
  )
}

export default UploadTempMaterialFileDialog
