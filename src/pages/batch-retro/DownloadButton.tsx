import { UploadFile } from '@/services/brain'
import { downloadFile } from '@/utils'
import { Button } from 'antd'

export const DownloadButton: React.FC<{ file?: UploadFile; name?: string }> = ({
  file,
  name
}) => {
  if (!file) return null
  return (
    <Button
      type="link"
      onClickCapture={(e) => {
        e.stopPropagation()
        downloadFile(file.url, file.name || '', undefined, undefined, false)
      }}
    >
      {name || file.name}
    </Button>
  )
}
