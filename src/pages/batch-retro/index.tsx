import { BatchRet<PERSON> } from '@/services/brain'
import { getWord } from '@/utils'
import { ActionType, PageContainer, ProTable } from '@ant-design/pro-components'
import { App, Button } from 'antd'
import React, { useRef } from 'react'
import UploadTaskFileDialog from './UploadTaskFileDialog'
import UploadTempMaterialFileDialog from './UploadTempMaterialDialog'
import { columns } from './columns'
import { cancelRetro, fetchBatchRetros } from './request'

export interface BatchRetroProps {
  id?: number
}

const BatchRetroComp: React.FC<BatchRetroProps> = ({}) => {
  const { message } = App.useApp()
  const actionRef = useRef<ActionType>()
  const doCancel = async (retro: BatchRetro) => {
    const result = await cancelRetro(retro)
    if (typeof result !== 'string') {
      message.success({
        content: getWord('pages.batchRetro.label.cancelSuccess')
      })
    } else {
      message.error({
        content: result || getWord('pages.batchRetro.label.cancelFailed')
      })
    }
    actionRef.current?.reload()
  }

  return (
    <PageContainer>
      <ProTable<BatchRetro>
        search={false}
        actionRef={actionRef}
        headerTitle={getWord('pages.batchRetro.label.history')}
        options={false}
        toolBarRender={() => [
          <UploadTempMaterialFileDialog
            key="updateTempMaterial"
            trigger={
              <Button key="uploadTempMaterials">
                {getWord('pages.batchRetro.label.uploadTempMaterials')}
              </Button>
            }
          />,
          <UploadTaskFileDialog
            key="newTask"
            onFinished={() => actionRef.current?.reload()}
            trigger={
              <Button type="primary">
                {getWord('pages.batchRetro.label.newRequest')}
              </Button>
            }
          />
        ]}
        request={fetchBatchRetros}
        columns={columns(doCancel)}
      />
    </PageContainer>
  )
}

export default BatchRetroComp
