import {
  BatchRetro,
  BatchRetroTaskResponse,
  defaultPageSize,
  FileParseResult,
  query,
  service,
  StaticFile,
  UploadFile
} from '@/services/brain'
import { UserRetroSetting } from '@/services/brain/types/user-setting'
import { getEnvConfig, getToken, getWord } from '@/utils'
import { RcFile } from 'antd/es/upload/interface'
import axios from 'axios'
import dayjs from 'dayjs'
import { isNumber } from 'lodash'

const defaultMaterialLibId = [1, 2, 3, 4, 5]
const defaultBatchIdPadding = 1000

interface BatchRetroConfig {
  temp_material_id_padding: number
  temp_material_callback_domain: string
  batch_task_callback_domain: string
}

const getBatchRetroConfig = async (): Promise<BatchRetroConfig> => {
  const { data } = await service<any>('retro-config').select().get()
  const {
    temp_material_id_padding = defaultBatchIdPadding,
    temp_material_callback_domain = '',
    batch_task_callback_domain = ''
  } = data as any
  return {
    temp_material_id_padding: temp_material_id_padding || defaultBatchIdPadding,
    temp_material_callback_domain: temp_material_callback_domain || '',
    batch_task_callback_domain: batch_task_callback_domain || ''
  }
}

export const fetchBatchRetros = async (params: {
  pageSize?: number
  current?: number
}) => {
  const req = query<BatchRetro>('batch-retros')
    .paginate(params.current || 1, params.pageSize || defaultPageSize)
    .populateWith('origin_file', ['*'])
    .populateWith('result_file', ['*'])
    .populateWith('create_user', ['*'])
    .sortBy([
      { field: 'createdAt', order: 'desc' },
      { field: 'updatedAt', order: 'desc' }
    ])

  const { error, data, meta } = await req.get()
  if (error) throw error
  if (!data) throw new Error('No data returned')

  return { data, success: true, total: meta?.pagination.total }
}

export const uploadFile = async (
  file: string | RcFile | Blob
): Promise<UploadFile | undefined> => {
  const formData = new FormData()
  formData.append('files', file)
  formData.append('path', '')

  const res = await axios
    .post<UploadFile[]>(`${getEnvConfig().apiBase}/api/upload`, formData, {
      headers: {
        Authorization: `Bearer ${getToken()}`,
        'Content-Type': 'multipart/form-data'
      }
    })
    .catch(console.error)
  return res?.data[0]
}

export const deleteFile = async (fileId: number) => {
  await service<UploadFile>('upload/files')
    .deleteOne(fileId)
    .catch(console.error)
}

export const parseTaskFile = async (
  filePath: string
): Promise<FileParseResult | undefined> => {
  const res = await axios
    .post<FileParseResult>(
      `${getEnvConfig().apiBase}/api/ai-batch-retro/parse-params`,
      { params_file_path: filePath },
      { headers: { Authorization: `Bearer ${getToken()}` } }
    )
    .catch(console.error)
  return res?.data
}

export const startParseTempMaterialFile = async (
  fileId: number,
  file: string | Blob | RcFile,
  userId: number
): Promise<boolean | string> => {
  const { data } = await query<StaticFile>('static-file')
    .populateWith('updating_temp_material_file')
    .get()
  const { id } =
    (data as unknown as StaticFile)?.updating_temp_material_file || {}
  if (id) {
    return getWord('pages.batchRetro.label.parsingTempMaterials')
  }

  const res = await service<StaticFile>('static-file', {
    method: 'PUT',
    params: { populate: '*' }
  }).create({
    updating_temp_material_file: [fileId],
    updating_material_user: { id: userId }
  } as unknown as StaticFile)
  if (!res?.data?.updating_temp_material_file) {
    return false
  }

  const { temp_material_id_padding, temp_material_callback_domain } =
    await getBatchRetroConfig()

  const formData = new FormData()
  formData.append('materials_file_content', file)
  formData.append('materials_file_id', `${fileId + temp_material_id_padding}`)
  formData.append(
    'result_callback',
    `${temp_material_callback_domain}/api/batch-retros/temp-materials/${fileId}`
  )

  const parseRes = await axios
    .post(
      `${getEnvConfig().apiBase}/api/ai-batch-retro/update-temp-materials`,
      formData,
      { headers: { 'Content-Type': 'multipart/form-data' } }
    )
    .catch(console.error)
  if (parseRes) {
    return true
  }
  return false
}

const doBatchRetroTask = async (
  params_file_path: string,
  batch_id: number,
  material_lib_ids: number[],
  result_callback: string,
  user_id: number
): Promise<BatchRetroTaskResponse | undefined> => {
  const res = await axios
    .post<BatchRetroTaskResponse>(
      `${getEnvConfig().apiBase}/api/ai-batch-retro/create-task`,
      {
        params_file_path,
        batch_id,
        material_lib_ids,
        result_callback,
        user_id
      },
      { headers: { Authorization: `Bearer ${getToken()}` } }
    )
    .catch(console.error)
  return res?.data
}

const doCancelRetroTask = async (
  batchRetroId: number
): Promise<string | true> => {
  const res = await axios
    .post(
      `${
        getEnvConfig().apiBase
      }/api/ai-batch-retro/cancel-task/${batchRetroId}`,
      {},
      { headers: { Authorization: `Bearer ${getToken()}` } }
    )
    .catch(console.error)
  return res?.data ? true : 'failed'
}

export const getSignedUrl = async (fileId: number): Promise<string> => {
  const res = await service<{ temp_files: (number | { url: string })[] }>(
    'temp-file',
    { method: 'PUT', params: { populate: '*' } }
  ).create({ temp_files: [fileId] })
  const file = res.data?.temp_files?.[0]
  return isNumber(file) ? '' : file?.url || ''
}

export const createBatchRetroTask = async (
  fileId: number,
  filePath: string,
  creatorId?: number
): Promise<true | string> => {
  const { data } = await service<BatchRetro>('batch-retros').create({
    total_count: 0,
    status: 'running',
    create_user: { id: creatorId },
    origin_file: fileId
  } as unknown as BatchRetro)
  if (!data?.id) return 'Create batch retro task failed'

  const [
    { data: userConfig },
    { temp_material_id_padding, batch_task_callback_domain }
  ] = await Promise.all([
    service<any>('user-config').select(['retro_params']).get(),
    getBatchRetroConfig()
  ])

  const staticFile = await query<StaticFile>('static-file')
    .populateWith('current_temp_material_file')
    .get()
    .catch(console.error)
  const { current_temp_material_file } = (staticFile?.data ||
    {}) as unknown as StaticFile
  const tempMaterialFileId = current_temp_material_file?.id

  const res = await doBatchRetroTask(
    filePath,
    data.id,
    [
      ...((userConfig as unknown as UserRetroSetting)?.material_lib ||
        defaultMaterialLibId),
      ...(tempMaterialFileId
        ? [tempMaterialFileId + temp_material_id_padding]
        : [])
    ],
    `${batch_task_callback_domain}/api/batch-retros/result/${data.id}`,
    creatorId || 0
  )
  if (!res) {
    await service<BatchRetro>('batch-retros').deleteOne(data.id)
    return 'Create batch retro task failed'
  }
  const { total_count, estimated_completion_in_minutes } = res

  await service<BatchRetro>('batch-retros').update(data.id, {
    total_count,
    status: 'running',
    start_time: new Date(),
    finished_time: dayjs()
      .add(estimated_completion_in_minutes, 'minute')
      .toDate()
  } as unknown as BatchRetro)
  return true
}

export const cancelRetro = async (
  retro: BatchRetro
): Promise<string | BatchRetro> => {
  const res = await doCancelRetroTask(retro.id).catch(console.error)
  if (res !== true) return res || getWord('pages.batchRetro.label.cancelFailed')
  const updateRes = await service<BatchRetro>('batch-retros')
    .update(retro.id, {
      status: 'canceled',
      finished_time: null as unknown as Date
    })
    .catch(console.error)
  return updateRes?.data || getWord('pages.batchRetro.label.cancelFailed')
}
