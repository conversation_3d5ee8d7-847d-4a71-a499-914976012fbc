import { use<PERSON>rainFetch } from '@/hooks/useBrainFetch'
import {
  FileParseResult,
  query,
  StaticFile,
  UploadFile
} from '@/services/brain'
import { downloadFile, getWord, timeForFilename } from '@/utils'
import { InboxOutlined } from '@ant-design/icons'
import { ModalForm } from '@ant-design/pro-components'
import { useModel } from '@umijs/max'
import { Alert, App, Button, Upload } from 'antd'
import { UploadRequestOption } from 'rc-upload/lib/interface'
import React, { useEffect, useState } from 'react'
import { DownloadButton } from './DownloadButton'
import {
  createBatchRetroTask,
  deleteFile,
  getSignedUrl,
  parseTaskFile,
  uploadFile
} from './request'

const { Dragger } = Upload

interface UploadResult extends FileParseResult {
  file: UploadFile
}

const getFilePath = (url: string) => {
  const pathname = new URL(url).pathname
  const segments = pathname.split('/').filter(Boolean)
  return '/' + segments.slice(1).join('/')
}

const uploader = async ({
  onProgress,
  onError,
  onSuccess,
  filename,
  file
}: UploadRequestOption) => {
  let percent = 0
  const percentUpdaterId = setInterval(() => {
    onProgress?.({ percent: (percent += 1) })
  }, 500)
  const uploaded = await uploadFile(file)
  clearInterval(percentUpdaterId)

  if (!uploaded) {
    onError?.({ name: filename || '', message: 'upload file error' })
    return
  }
  onProgress?.({ percent: 50 })

  const parsed = await parseTaskFile(getFilePath(uploaded.url))
  if (!parsed?.success_count) {
    onError?.({ name: filename || '', message: 'upload file error' }, parsed)
    return
  }
  onSuccess?.({ ...parsed, file: uploaded })
}

const Uploader: React.FC<{
  onFileChange: (file?: UploadResult) => void
}> = ({ onFileChange }) => {
  const { message } = App.useApp()
  const { fetch } = useBrainFetch()
  const [file, setFile] = useState<UploadFile>()

  useEffect(() => {
    fetch(
      query<StaticFile>('static-file')
        .populateWith('batch_retro_template')
        .get()
    ).then(({ data }) => {
      setFile((data as unknown as StaticFile)?.batch_retro_template)
    })
  }, [])

  return (
    <Dragger
      accept=".xlsx"
      multiple
      maxCount={1}
      onChange={({ file: { name, status, response } }) => {
        if (status === 'done') {
          message.success({ content: `${name} ${getWord('upload-success')}` })
          onFileChange(response)
          return
        } else if (status === 'error') {
          message.error({ content: `${name} ${getWord('upload-failed')}` })
        }
        onFileChange()
      }}
      customRequest={uploader}
    >
      <p className="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p className="ant-upload-text">{getWord('upload-text')}</p>
      {file && (
        <p className="ant-upload-hint">
          <DownloadButton
            file={file}
            name={getWord('pages.batchRetro.label.downloadTemplate')}
          />
        </p>
      )}
    </Dragger>
  )
}

const UploadTaskFileDialog: React.FC<{
  trigger: JSX.Element
  onFinished?: () => void
}> = ({ trigger, onFinished }) => {
  const { message } = App.useApp()
  const [open, setOpen] = useState<boolean>(false)
  const [result, setResult] = useState<UploadResult>()
  const { initialState } = useModel('@@initialState')

  const updateResult = async (newResult?: UploadResult) => {
    if (result) {
      await deleteFile(result.file.id)
      if (result.error_file_id) {
        await deleteFile(result.error_file_id)
      }
    }
    setResult(newResult)
  }
  const updateOpen = async (newOpen: boolean) => {
    if (!newOpen) {
      await updateResult()
    }
    setOpen(newOpen)
  }

  return (
    <ModalForm
      trigger={trigger}
      title={getWord('pages.batchRetro.label.newBatchRetroRequest')}
      open={open}
      onOpenChange={updateOpen}
      modalProps={{ destroyOnClose: true }}
      submitter={{
        submitButtonProps: {
          disabled: !(result?.file && result?.success_count)
        }
      }}
      onFinish={async () => {
        if (!result?.file.url || !result.file.id) return
        const task = await createBatchRetroTask(
          result.file.id,
          getFilePath(result?.file.url),
          initialState?.userInfo?.id
        )
        if (task === true) {
          if (result.error_file_id) {
            await deleteFile(result.error_file_id)
          }
          message.success({
            content: getWord('pages.batchRetro.label.createTaskSuccess')
          })
          onFinished?.()
          setResult(undefined)
          return true
        }
        console.error(task)
        message.error({
          content: getWord('pages.batchRetro.label.createTaskError')
        })
        return
      }}
    >
      <Uploader onFileChange={updateResult} />
      {result?.success_count && (
        <>
          <br />
          <Alert
            message={getWord(
              'pages.batchRetro.label.uploadSuccessfullyRecordsInfo'
            ).replace('$n', result.success_count.toString())}
            type="success"
          />
        </>
      )}
      {!!result?.error_count && (
        <>
          <br />
          <Alert
            message={getWord(
              'pages.batchRetro.label.uploadFailedRecordsInfo'
            ).replace('$n', result.error_count.toString())}
            type="error"
            action={
              result.error_file_id ? (
                <Button
                  size="small"
                  type="link"
                  onClick={async () => {
                    if (!result.error_file_id) return
                    const url = await getSignedUrl(result.error_file_id)
                    if (!url) return

                    const filename = `${getWord(
                      'pages.batchRetro.label.failedRecordsFilenamePrefix'
                    )}_${timeForFilename()}.xlsx`
                    downloadFile(url, filename, undefined, undefined, false)
                  }}
                >
                  {getWord('pages.batchRetro.label.viewFailedRecords')}
                </Button>
              ) : null
            }
            closable={!!result?.success_count}
          />
        </>
      )}
    </ModalForm>
  )
}

export default UploadTaskFileDialog
