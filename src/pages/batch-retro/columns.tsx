import { BatchRetro } from '@/services/brain'
import { downloadFile, getWord, timeForFilename } from '@/utils'
import { ProColumns } from '@ant-design/pro-components'
import { ReactNode } from 'react'
import ButtonWithConfirm from '../projects/components/ButtonWithConfirm'

const batchRetroStatusEnums = {
  running: getWord('pages.batchRetro.label.status.running'),
  finished: getWord('pages.batchRetro.label.status.finished'),
  canceled: getWord('pages.batchRetro.label.status.canceled')
}

const getButtons = (
  retro: BatchRetro,
  doCancel: (retro: BatchRetro) => void
): Record<'originBtn' | 'resultBtn' | 'cancelBtn', ReactNode> => {
  const { id, origin_file, result_file } = retro
  const originBtn = origin_file?.url ? (
    <a
      onClick={() =>
        downloadFile(
          origin_file.url as string,
          `${getWord(
            'pages.batchRetro.label.originFilePrefix'
          )}_${id}_${timeForFilename()}.xlsx`,
          undefined,
          undefined,
          false
        )
      }
    >
      {getWord('pages.batchRetro.label.originDownloadButton')}
    </a>
  ) : null
  const resultBtn = result_file?.url ? (
    <a
      key="result"
      onClick={() =>
        downloadFile(
          result_file.url as string,
          `${getWord(
            'pages.batchRetro.label.resultFilePrefix'
          )}_${id}_${timeForFilename()}.xlsx`,
          undefined,
          undefined,
          false
        )
      }
    >
      {getWord('pages.batchRetro.label.resultDownloadButton')}
    </a>
  ) : null
  const cancelBtn = (
    <ButtonWithConfirm
      type="link"
      key="cancel"
      onConfirm={() => doCancel(retro)}
      buttonText={getWord('pages.batchRetro.label.cancelTask')}
      title={getWord('pages.batchRetro.label.confirmCancel')}
      description={getWord('pages.batchRetro.label.confirmCancelTip')}
    />
  )

  return { originBtn, resultBtn, cancelBtn }
}

const buttonCols = (
  retro: BatchRetro,
  doCancel: (retro: BatchRetro) => void
): ReactNode[] => {
  const { originBtn, resultBtn, cancelBtn } = getButtons(retro, doCancel)
  if (!originBtn) return []

  const cols: ReactNode[] = [originBtn]
  switch (retro.status) {
    case 'running':
      cols.push(cancelBtn)
      break
    case 'finished':
      cols.push(resultBtn)
      break
    default:
      break
  }
  return cols
}

export const columns = (
  doCancel: (retro: BatchRetro) => void
): ProColumns<BatchRetro>[] => [
  {
    title: getWord('pages.batchRetro.label.total_count'),
    dataIndex: 'total_count',
    valueType: 'digit'
  },
  {
    title: getWord('pages.batchRetro.label.completed_route_count'),
    dataIndex: 'completed_route_count',
    valueType: 'digit'
  },
  {
    title: getWord('pages.batchRetro.label.partial_route_count'),
    dataIndex: 'partial_route_count',
    valueType: 'digit'
  },
  {
    title: getWord('pages.batchRetro.label.no_route_count'),
    dataIndex: 'no_route_count',
    valueType: 'digit'
  },
  {
    title: getWord('pages.batchRetro.label.start_time'),
    dataIndex: 'start_time',
    valueType: 'dateTime'
  },
  {
    title: getWord('pages.batchRetro.label.finished_time'),
    dataIndex: 'finished_time',
    valueType: 'dateTime'
  },
  {
    title: getWord('pages.batchRetro.label.starter'),
    dataIndex: 'create_user',
    renderText: (_, r) => r.create_user?.username,
    valueType: 'text'
  },
  {
    title: getWord('pages.batchRetro.label.status'),
    dataIndex: 'status',
    valueType: 'radio',
    valueEnum: batchRetroStatusEnums
  },
  {
    title: getWord('pages.experiment.label.operation'),
    dataIndex: 'option',
    valueType: 'option',
    hideInDescriptions: true,
    render: (_, retro) => buttonCols(retro, doCancel)
  }
]
