import type { IFormData } from '@/components/SearchForm/index.d'
import { getWord } from '@/utils'
export const queryData: IFormData[] = [
  {
    label: '实验需求状态',
    ctype: 'select',
    key: 'status',
    enums: [
      {
        label: getWord('pages.projectTable.statusLabel.cancelled'),
        value: 'canceled'
      },
      {
        label: getWord('experiment-to-be-ready'),
        value: 'created'
      },
      {
        label: getWord('experiment-prepared'),
        value: 'ready'
      },
      {
        label: getWord('experiment-ongoing'),
        value: 'running'
      },
      {
        label: getWord('experiment-completed'),
        value: 'finished'
      }
    ],
    placeholder: getWord('select-tip'),
    XL: { col: 5, labelWidth: 10, wrapperWidth: 14 }
  },
  {
    label: '反应结构式',
    ctype: 'input',
    key: 'rxn',
    enums: [],
    placeholder: getWord('input-tip'),
    XL: { col: 5, labelWidth: 10, wrapperWidth: 14 }
  },
  {
    label: '实验流程名称',
    ctype: 'select',
    key: 'experiment_design_no',
    placeholder: getWord('select-tip'),
    XL: { col: 6, labelWidth: 10, wrapperWidth: 14 }
  }
]
