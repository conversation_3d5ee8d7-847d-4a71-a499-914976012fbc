import { ReactComponent as WarnIcon } from '@/assets/svgs/warn.svg'
import ModalBase from '@/components/ModalBase'
import {
  apiCreateExperimentPlan,
  apiUpdateExperimentPlan,
  parseResponseResult
} from '@/services'
import type { IOption } from '@/types/common'
import { formatYTSTime, getWord, isEN } from '@/utils'
import {
  Col,
  DatePicker,
  Form,
  Input,
  Row,
  Select,
  Typography,
  message
} from 'antd'
import type { DatePickerProps, RangePickerProps } from 'antd/es/date-picker'
import dayjs from 'dayjs'
import { isEmpty } from 'lodash'
import { ReactElement, useEffect } from 'react'
import { useSelector } from 'umi'
import { titleDes } from '../enum'
import type { CreateModalProps, ICreateModalForm } from './index.d'
import styles from './index.less'
/* TODO style */
const OperateModal = ({
  openEvent,
  smiles,
  operateInfo,
  refreshRequest
}: CreateModalProps): ReactElement => {
  const [form] = Form.useForm<ICreateModalForm>()
  const { operateType } = operateInfo
  const enumState = useSelector((state: any) => state?.enum)
  const { experimentDesignList } = enumState
  const isCancel: boolean = operateType === 'cancel'
  const onConfirm = async () => {
    const values = await form.validateFields()
    const isCreate = operateType === 'create'
    let res,
      inputValues = {
        name: values?.name,
        experiment_design_no: values?.experiment_design_no,
        earliest_start_time: values?.earliest_start_time
          ? formatYTSTime(values?.earliest_start_time)
          : undefined,
        latest_start_time: values?.latest_start_time
          ? formatYTSTime(values?.latest_start_time)
          : undefined,
        rxn: smiles
      }
    if (isCreate) {
      res = await apiCreateExperimentPlan({
        data: inputValues
      })
    } else {
      let data = isCancel
        ? {
            id: operateInfo?.itemInfo?.id,
            status: 'canceled',
            cancel_reason: values?.cancel_reason
          }
        : {
            ...inputValues,
            id: operateInfo?.itemInfo?.id,
            status: operateInfo?.itemInfo?.status
          }
      res = await apiUpdateExperimentPlan({
        data
      })
    }
    if (parseResponseResult(res).ok) {
      message.success(
        `${
          isCreate
            ? getWord('pages.projectTable.statusChangeLabel.created')
            : getWord('pages.experiment.label.operation')
        }${isEN() ? ' ' : ''}${getWord(
          'pages.experiment.statusLabel.success'
        )}～`
      )
      refreshRequest()
    } else {
      throw 'request error'
    }
  }

  const onChangeStartTime = (
    earliestStartTime: DatePickerProps['value'] | RangePickerProps['value']
  ) => {
    const latestStartTime = form.getFieldValue('latest_start_time')
    if (latestStartTime) {
      const isStartAferEnd: boolean = dayjs(earliestStartTime).isAfter(
        latestStartTime,
        'day'
      )
      if (isStartAferEnd) form.setFieldValue('latest_start_time', undefined)
    }
  }

  useEffect(() => {
    if (openEvent?.open && operateInfo?.itemInfo) {
      form.setFieldsValue({
        experiment_design_no: operateInfo?.itemInfo?.experiment_design_no,
        name: operateInfo?.itemInfo?.name,
        earliest_start_time: isEmpty(operateInfo?.itemInfo?.earliest_start_time)
          ? undefined
          : dayjs(formatYTSTime(operateInfo?.itemInfo?.earliest_start_time)),
        latest_start_time: isEmpty(operateInfo?.itemInfo?.latest_start_time)
          ? undefined
          : dayjs(formatYTSTime(operateInfo?.itemInfo?.latest_start_time))
      })
    }
  }, [openEvent])

  /* TODO style */

  const disabledStartDate = (current) => {
    return current && current <= dayjs().add(-1, 'days')
  }

  function disabledEndDate(current: any) {
    return form.getFieldsValue()['earliest_start_time']
      ? current &&
          current <= dayjs(form.getFieldsValue()['earliest_start_time'])
      : null
  }
  return (
    <ModalBase
      title={
        isCancel ? (
          <>
            {titleDes[operateType]}
            <WarnIcon className={styles.warnIcon} />
          </>
        ) : (
          titleDes[operateType]
        )
      }
      openEvent={openEvent}
      onConfirm={onConfirm}
      afterClose={() => form.resetFields()}
    >
      <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
        {isCancel && (
          <Form.Item
            label={getWord('cancel-reason')}
            name="cancel_reason"
            rules={[{ required: true }]}
          >
            <Input.TextArea
              allowClear
              autoSize={{ minRows: 2, maxRows: 5 }}
              placeholder={getWord('input-cancel-reason')}
            />
          </Form.Item>
        )}
        <Form.Item
          label="实验流程名称"
          name="experiment_design_no"
          rules={[{ required: true }]}
        >
          <Select
            options={experimentDesignList as IOption[]}
            placeholder="请选择实验流程名称"
            showSearch
            optionFilterProp="label"
            disabled={isCancel}
          />
        </Form.Item>
        <Form.Item label={getWord('pages.experiment.label.name')} name="name">
          <Input
            placeholder={getWord('enter-ex-name')}
            maxLength={15}
            showCount
            allowClear
            disabled={isCancel}
          />
        </Form.Item>
        {/* TODO 物料表待接口逻辑支持后API integration and testing */}
        {/* FIXME 物料表要支持编辑  */}
        <Typography.Title
          level={4}
          style={{
            paddingLeft: '22px'
          }}
        >
          排程信息
        </Typography.Title>
        <Row>
          <Col span={12}>
            <Form.Item
              labelCol={{ span: 12 }}
              wrapperCol={{ span: 12 }}
              label="实验最早开始时间"
              name="earliest_start_time"
            >
              <DatePicker
                disabled={isCancel}
                disabledDate={disabledStartDate}
                onChange={onChangeStartTime}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              labelCol={{ span: 12 }}
              wrapperCol={{ span: 12 }}
              label="实验最晚开始时间"
              name="latest_start_time"
            >
              <DatePicker disabled={isCancel} disabledDate={disabledEndDate} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </ModalBase>
  )
}
export default OperateModal
