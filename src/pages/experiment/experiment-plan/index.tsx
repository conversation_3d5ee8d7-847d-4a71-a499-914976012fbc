import { getWord } from '@/utils'
import CustomTable from '@/components/CustomTable'
import SearchForm from '@/components/SearchForm'
import { EXPERIMENT_PLANS, initFilter } from '@/constants'
import useFetchData from '@/hooks/useFetchData'
import type { ItemType } from '@/types/common'
import { decodeUrl } from '@/utils'
import { PageContainer } from '@ant-design/pro-components'
import type { ExperimentPlanModel } from '@types'
import { Breadcrumb, Space } from 'antd'
import cs from 'classnames'
import { isArray, isEmpty } from 'lodash'
import { useCallback, useEffect, useState } from 'react'
import { useDispatch, useSearchParams, useSelector } from 'umi'
import OperateModal from './OperateModal'
import { columns } from './column'
import { titleDes } from './enum'
import type { IOperateInfo } from './index.d'
import styles from './index.less'
import { queryData } from './query-config'
// import MaterialList from '@/components/MaterialList'
export default function ExperimentPlan() {
  const dispatch = useDispatch()
  const enumState = useSelector((state) => state?.enum)
  const [queryParams, setQueryParams] = useState<any>(initFilter)
  const [refresh, setRefresh] = useState(false)
  const [openEvent, setOpenEvent] = useState<{ open?: boolean }>({})
  const [operateInfo, setOperateInfo] = useState<IOperateInfo>({
    operateType: 'create'
  })
  const { loading, listData, total } = useFetchData(
    queryParams,
    EXPERIMENT_PLANS,
    refresh
  )
  const tableConfig = {
    loading,
    bordered: true,
    dataSource: listData,
    pagination: {
      total,
      current: queryParams.page_no,
      pageSize: queryParams.page_size,
      showTotal: () => `共${total}条记录`,
      showQuickJumper: true,
      showSizeChanger: true
    }
  }

  const getExperimentDesignList = async () => {
    await dispatch({ type: 'enum/queryExperimentDesignList' })
  }

  useEffect(() => {
    getExperimentDesignList()
  }, [])

  const { experimentDesignList } = enumState

  const [searchParams] = useSearchParams()
  let experimentDesignNo = searchParams.get('experiment_design_no')
  useEffect(() => {
    if (!experimentDesignNo) return
    setQueryParams({
      ...queryParams,
      experiment_design_no: experimentDesignNo
        ? JSON.parse(decodeUrl(experimentDesignNo))
        : undefined
    })
  }, [])

  useEffect(() => {
    if (loading === false) setRefresh(false)
  }, [loading])

  const refreshRequest = () => setRefresh(true)

  const operate = () => {
    return [
      {
        title: getWord('pages.experiment.label.operation'),
        dataIndex: 'e',
        align: 'center',
        fixed: 'right',
        width: 160,
        render: (_, record: ExperimentPlanModel) => {
          const canShow = ['created', 'prepared'].includes(record?.status)
          const handleExperimentPlan = (operateType: 'amend' | 'cancel') => {
            setOperateInfo({
              itemInfo: record,
              operateType
            })
            setOpenEvent({ open: true })
          }
          return (
            /* 实验需求状态=“未ready” or “已ready”，点击取消实验需求 */
            <Space size="small">
              {canShow && (
                <>
                  <a onClick={() => handleExperimentPlan('amend')}>
                    {titleDes['amend']}
                  </a>
                  <a onClick={() => handleExperimentPlan('cancel')}>
                    {titleDes['cancel']}
                  </a>
                </>
              )}
            </Space>
          )
        }
      }
    ]
  }

  const handleQueryData = useCallback(() => {
    if (!isEmpty(experimentDesignList)) {
      queryData.forEach((item) => {
        if (item?.key === 'experiment_design_no')
          item.enums = experimentDesignList
      })
    }
    return queryData
  }, [experimentDesignList])

  return (
    <PageContainer
      breadcrumbRender={({ breadcrumb }) => {
        let routes: ItemType[] = breadcrumb?.items as ItemType[]
        return isArray(routes) && !isEmpty(routes) ? (
          <Breadcrumb>
            {routes.map((item: ItemType, index: number) => {
              return (
                <Breadcrumb.Item
                  onClick={(event) => {
                    if (index === 0) event.preventDefault()
                  }}
                  key={item?.linkPath}
                  href={item?.linkPath as string}
                >
                  {item.breadcrumbName}
                </Breadcrumb.Item>
              )
            })}
          </Breadcrumb>
        ) : (
          ''
        )
      }}
      className={cs(styles.experimentPlan)}
    >
      <SearchForm
        formData={handleQueryData()}
        initData={{
          experiment_design_no: experimentDesignNo
            ? JSON.parse(decodeUrl(experimentDesignNo))
            : undefined
        }}
        onSubmit={(values: any) =>
          setQueryParams({ ...queryParams, ...values, pageNo: 1 })
        }
        onReset={() => setQueryParams(initFilter)}
        btnGroupsConfig={[
          {
            clickFn: () => {
              setOperateInfo({
                operateType: 'create'
              })
              setOpenEvent({ open: true })
            },
            text: `${titleDes['create']}`
          }
        ]}
      />
      <OperateModal
        operateInfo={operateInfo}
        openEvent={openEvent}
        refreshRequest={refreshRequest}
      />
      <CustomTable
        {...tableConfig}
        columns={[...columns, ...operate()]}
        onChange={(current, pageSize) => {
          setQueryParams({
            ...queryParams,
            page_no: current,
            page_size: pageSize
          })
        }}
      />
    </PageContainer>
  )
}
