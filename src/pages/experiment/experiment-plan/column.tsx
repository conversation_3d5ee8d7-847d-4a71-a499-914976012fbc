import LazySmileDrawer from '@/components/LazySmileDrawer'
import { statusColor } from '@/constants'
import {
  encodeString,
  formatYMDTime,
  formatYTSTime,
  getWord,
  toExperimentDetail
} from '@/utils'
import type { ExperimentPlanModel } from '@types'
import { Tag } from 'antd'
import type { ColumnsType } from 'antd/lib/table'
import type { Dayjs } from 'dayjs'
import { history } from 'umi'
export const columns: ColumnsType<ExperimentPlanModel> = [
  {
    title: getWord('pages.experiment.label.name'),
    dataIndex: 'name',
    align: 'left',
    fixed: 'left',
    width: 180,
    render: (_text, record) =>
      ['created', 'prepared', 'canceled'].includes(record?.status) ? (
        record?.name
      ) : (
        <a
          type="link"
          onClick={() => toExperimentDetail(record?.experiment_no)}
        >
          {record?.name}
        </a>
      )
  },
  {
    title: getWord('pages.experiment.label.no'),
    dataIndex: 'experiment_no',
    align: 'left',
    width: 230
  },
  {
    title: '实验计划状态',
    dataIndex: 'status',
    align: 'left',
    width: 130,
    render: (text) => {
      const experimentPlanStatusDes = {
        created: getWord('experiment-to-be-ready'),
        ready: getWord('experiment-prepared'),
        canceled: getWord('pages.projectTable.statusLabel.cancelled'),
        running: getWord('experiment-ongoing'),
        finished: getWord('experiment-completed')
      }
      return (
        <Tag color={statusColor[text] as string}>
          {experimentPlanStatusDes[text]}
        </Tag>
      )
    }
  },
  {
    title: getWord('reaction'),
    dataIndex: 'rxn',
    align: 'left',
    width: 200,
    render: (text) =>
      text && <LazySmileDrawer structure={text} className="smilesItem" />
  },
  {
    title: '实验流程名称',
    dataIndex: 'experiment_design',
    align: 'left',
    width: 150,
    render: (_text, record) =>
      record?.experiment_design?.name ? (
        <a
          onClick={() =>
            history.push(
              `/experimental-procedure/detail/${encodeString(
                JSON.stringify(record?.experiment_design?.id)
              )}?type=reading`
            )
          }
        >
          {record?.experiment_design?.name}
        </a>
      ) : (
        ''
      )
  },
  {
    title: getWord('creation-time'),
    dataIndex: 'created_date',
    align: 'left',
    width: 160,
    render: (text: Dayjs) => (text ? formatYTSTime(text) : '')
  },
  {
    title: '计划最早开始时间',
    dataIndex: 'earliest_start_time',
    align: 'left',
    width: 100,
    render: (text: Dayjs) => (text ? formatYMDTime(text) : '')
  }
]
