import CustomTable from '@/components/CustomTable'
import SearchForm from '@/components/SearchForm'
import { EXPERIMENT_SEARCH, initFilter } from '@/constants'
import useFetchData from '@/hooks/useFetchData'
import type { ItemType } from '@/types/common'
import { PageContainer } from '@ant-design/pro-components'
import { Breadcrumb } from 'antd'
import cs from 'classnames'
import { isArray, isEmpty } from 'lodash'
import { useState } from 'react'
import { columns } from './column'
import styles from './index.less'
import { queryData } from './query-config'
export default function ExperimentExecute() {
  const [queryParams, setQueryParams] = useState<any>(initFilter)
  const { loading, listData, total } = useFetchData(
    queryParams,
    EXPERIMENT_SEARCH
  )

  const tableConfig = {
    loading,
    bordered: true,
    dataSource: listData,
    pagination: {
      total,
      current: queryParams.page_no,
      pageSize: queryParams.page_size,
      showTotal: () => `共${total}条记录`,
      showQuickJumper: true,
      showSizeChanger: true
    }
  }

  return (
    <PageContainer
      breadcrumbRender={({ breadcrumb }) => {
        let routes: ItemType[] = breadcrumb?.items as ItemType[]
        return isArray(routes) && !isEmpty(routes) ? (
          <Breadcrumb>
            {routes.map((item: ItemType, index: number) => {
              return (
                <Breadcrumb.Item
                  onClick={(event) => {
                    if (index === 0) event.preventDefault()
                  }}
                  key={item?.linkPath}
                  href={item?.linkPath as string}
                >
                  {item.breadcrumbName}
                </Breadcrumb.Item>
              )
            })}
          </Breadcrumb>
        ) : (
          ''
        )
      }}
      className={cs(styles.experimentExecute)}
    >
      <SearchForm
        formData={queryData}
        onSubmit={(values: any) =>
          setQueryParams({ ...queryParams, ...values, pageNo: 1 })
        }
        onReset={() => setQueryParams(initFilter)}
      />
      {/* FIXME 接口响应字段缺少暂停时间字段 */}
      <CustomTable
        {...tableConfig}
        columns={columns}
        rowKey="experiment_design_no"
        onChange={(current, pageSize) => {
          setQueryParams({
            ...queryParams,
            page_no: current,
            page_size: pageSize
          })
        }}
      />
    </PageContainer>
  )
}
