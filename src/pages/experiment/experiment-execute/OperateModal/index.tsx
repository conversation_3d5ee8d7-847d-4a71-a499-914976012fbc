import { ReactComponent as WarnIcon } from '@/assets/svgs/warn.svg'
import ModalBase from '@/components/ModalBase'
import { apiUpdateExperimentStatus, parseResponseResult } from '@/services'
import { decodeUrl, getWord } from '@/utils'
import type { RadioChangeEvent } from 'antd'
import { Form, Input, Radio, message } from 'antd'
import { ReactElement, useState } from 'react'
import type { CreateModalProps, OperateModalForm } from '.'
import { titleDes } from './enum'
import styles from './index.less'
const OperateModal = ({
  openEvent,
  id,
  operateInfo,
  refreshRequest
}: CreateModalProps): ReactElement => {
  const [experimentResult, setExperimentResult] = useState()
  const [form] = Form.useForm<OperateModalForm>()
  const { operateType } = operateInfo
  const isReport = operateType === 'report'
  const isCancelOperate = operateType === 'canceled'

  const onConfirm = async () => {
    const values = await form.validateFields()
    const res = await apiUpdateExperimentStatus({
      data: {
        experiment_no: JSON.parse(decodeUrl(id)),
        status: isReport ? values?.status : operateType,
        reason: values?.reason
      }
    })
    if (parseResponseResult(res).ok) {
      message.success(`${getWord('operate-success')}～`)
      refreshRequest()
    } else {
      throw 'request error'
    }
  }
  const onChange = (e: RadioChangeEvent) => {
    console.log('radio checked', e.target.value)
    setExperimentResult(e.target.value)
  }
  return (
    <ModalBase
      title={
        isCancelOperate ? (
          <>
            {titleDes[operateType]}
            <WarnIcon className={styles.warnIcon} />
          </>
        ) : (
          titleDes[operateType]
        )
      }
      openEvent={openEvent}
      onConfirm={onConfirm}
      afterClose={() => form.resetFields()}
    >
      <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
        {isReport && (
          <Form.Item
            label={getWord('conclusion')}
            name="status"
            rules={[{ required: true }]}
          >
            <Radio.Group onChange={onChange}>
              <Radio value="success">
                {getWord('app.general.message.success')}
              </Radio>
              <Radio value="failed">
                {getWord('component.notification.statusValue.failed')}
              </Radio>
            </Radio.Group>
          </Form.Item>
        )}
        {experimentResult !== 'success' && (
          <>
            {isCancelOperate ? (
              <div className={styles.cancelTip}>
                实验一旦取消，即实验结果为失败！
              </div>
            ) : (
              ''
            )}

            <Form.Item
              label={
                isCancelOperate ? getWord('cancel-reason') : getWord('reason')
              }
              name="reason"
              rules={[{ required: true }]}
            >
              <Input.TextArea
                allowClear
                autoSize={{ minRows: 2, maxRows: 5 }}
                placeholder={getWord('enter-reason')}
              />
            </Form.Item>
          </>
        )}
      </Form>
    </ModalBase>
  )
}

export default OperateModal
