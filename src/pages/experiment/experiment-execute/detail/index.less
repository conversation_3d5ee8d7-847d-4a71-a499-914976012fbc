@import '@/style/variables.less';
.experimentExecuteDetail {
  position: relative;
  min-height: @main-content-height;
  overflow-y: scroll !important;
  .operate {
    position: absolute;
    top: 10px;
    right: 16px;
  }
  .experimentExecuteInfo {
    height: 150px;
    margin: 0px 0px 30px;
    background: #ffffff;
    border-radius: 10px;
    box-shadow: 0px 0px 8px 0px rgba(187, 187, 187, 0.5);
    .title {
      padding-top: 15px;
      font-size: 20px;
      text-align: center;
    }
    .detail {
      padding: 0px 30px;
      font-size: 13px;
      :global {
        .ant-col {
          margin-top: 15px;
        }
      }
    }
  }
}
