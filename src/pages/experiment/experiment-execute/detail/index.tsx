import BpmnEditor from '@/components/BpmnEditor'
import LoadingTip from '@/components/LoadingTip'
import {
  apiExperimentDetail,
  apiTaskStatus,
  parseResponseResult
} from '@/services'
import { OperationResponse } from '@/types/models/operation-response'
import { decodeUrl, getWord } from '@/utils'
import { PageContainer } from '@ant-design/pro-components'
import type { ExperimentDetail } from '@types'
import { useModel, useParams } from '@umijs/max'
import { Button, Col, Row, Space } from 'antd'
import cs from 'classnames'
import { isEmpty } from 'lodash'
import { useEffect, useRef, useState } from 'react'
import OperateModal from '../OperateModal'
import type { IOperateInfo, IRouteParams } from './index.d'
import styles from './index.less'

let timeoutId: any
export default function ExperimentExecuteDetail() {
  const { experimentalNo } = useParams() as IRouteParams

  const { queryTaskList, taskList } = useModel('task')
  const [operateInfo, setOperateInfo] = useState<IOperateInfo>({
    operateType: 'hold'
  })
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [operationProcess, setOperationProcess] = useState<OperationResponse>()
  const [openEvent, setOpenEvent] = useState<{ open?: boolean }>({})
  const [expErimentInfo, setExpErimentInfo] = useState<ExperimentDetail>(null)

  const ref = useRef(expErimentInfo)
  /* 流程进度跟踪： 失败和成功需要在流程图中的task展示；显示每个任务的状态（running，complete ， faile）,其他情况均为todo */
  const getProcessStatus = async (curExperimentNo?: string) => {
    let curNo = curExperimentNo || ref?.current?.experiment_no
    if (!curNo) return
    const res = await apiTaskStatus({
      routeParams: curExperimentNo || ref?.current?.experiment_no
    })
    if (parseResponseResult(res).ok) {
      setOperationProcess(res?.data) // mockOperationStatusData
      if (['completed', 'canceled'].includes(res?.data?.status)) {
        clearTimeout(timeoutId)
      } else {
        timeoutId = setTimeout(() => {
          getProcessStatus()
        }, 10000)
      }
    }
  }

  const getDetailInfo = async () => {
    /* FIXME 【P1】uat-新的实验流程，保存后点击启动实验，无法跳转到实验详情页面——白屏 https://c12ai.atlassian.net/jira/software/projects/LAB/boards/21/backlog?assignee=638448199341d1f13606f856&selectedIssue=LAB-335 */
    const res = await apiExperimentDetail({
      routeParams: JSON.parse(decodeUrl(experimentalNo as string))
    })
    if (parseResponseResult(res).ok) {
      setExpErimentInfo(res?.data)
      getProcessStatus(res?.data?.experiment_no) // first time get operate status
    }
  }

  useEffect(() => {
    if (!experimentalNo) return
    getDetailInfo().then(() => {
      setIsLoading(false)
    })
  }, [experimentalNo])

  useEffect(() => {
    ref.current = expErimentInfo
  })

  const handleOperate = (type: string) => {
    setOperateInfo({
      operateType: type
    })
    setOpenEvent({ open: true })
  }

  const renderText = (value: string | number) => value || '暂无'

  useEffect(() => {
    queryTaskList()
    return () => window.clearTimeout(timeoutId)
  }, [])

  const experimentExecuteDes = {
    running: getWord('component.notification.statusValue.running'),
    hold: getWord('experiment-pending'),
    canceled: getWord('pages.projectTable.statusLabel.cancelled'),
    completed: getWord('component.notification.statusValue.success'),
    success: getWord('app.general.message.success'),
    incident: getWord('experiment-exception'),
    failed: getWord('component.notification.statusValue.failed')
  }

  return (
    <PageContainer className={cs(styles.experimentExecuteDetail)}>
      <OperateModal
        operateInfo={operateInfo}
        openEvent={openEvent}
        id={experimentalNo}
        refreshRequest={getDetailInfo}
      />
      {!isLoading ? (
        <>
          <Space className={styles.operate} wrap>
            {expErimentInfo?.status === 'hold' ? (
              <Button
                style={{ color: 'white', backgroundColor: '#0baa50' }}
                onClick={() => handleOperate('running')}
              >
                恢复
              </Button>
            ) : 'running' === expErimentInfo?.status ? (
              <Button type="primary" onClick={() => handleOperate('hold')}>
                {getWord('pages.experiment.label.operation.pause')}
              </Button>
            ) : (
              ''
            )}
            {['running', 'hold'].includes(expErimentInfo?.status) ? (
              <Button
                type="primary"
                danger
                onClick={() => handleOperate('failed')}
              >
                {getWord('pages.experiment.label.operation.stop')}
              </Button>
            ) : (
              ''
            )}
            {expErimentInfo?.status === 'created' ? (
              <Button danger onClick={() => handleOperate('canceled')}>
                {getWord('pages.experiment.label.operation.cancel')}
              </Button>
            ) : (
              ''
            )}
            {/* <Button onClick={() => handleOperate('report')}>结果反馈</Button> */}
          </Space>
          <div className={styles.experimentExecuteInfo}>
            <div className={styles.title}>
              {expErimentInfo?.experiment_name}
            </div>
            <div className={styles.detail}>
              <Row>
                <Col span="6">
                  <span>
                    {getWord('pages.experiment.label.no')} :
                    {renderText(expErimentInfo?.experiment_no)}
                  </span>
                </Col>
                <Col span="6">
                  <span>
                    {getWord('progress')} :
                    {renderText(expErimentInfo?.progress)}
                  </span>
                </Col>
                <Col span="6">
                  <span>
                    {getWord('yield')} :
                    {renderText(expErimentInfo?.predict_yield)}
                  </span>
                </Col>
                <Col span="6">
                  <span>
                    {getWord('estimated-completed-date')} :
                    {renderText(expErimentInfo?.predict_end_date)}
                  </span>
                </Col>
                <Col span="6">
                  <span>
                    {getWord('pages.experiment.label.status')} :{' '}
                    {expErimentInfo?.status
                      ? experimentExecuteDes[expErimentInfo?.status]
                      : '暂无'}
                  </span>
                </Col>
              </Row>
            </div>
          </div>
          {isEmpty(taskList) ? (
            'loading...'
          ) : (
            <BpmnEditor
              experimentalNo={experimentalNo}
              panelDatas={{
                experimentDesignNo: expErimentInfo?.experiment_design_no,
                workflow: expErimentInfo?.workflow,
                readOnly: true
              }}
              operationProcess={operationProcess?.tasks}
              taskList={taskList}
            />
          )}
        </>
      ) : (
        <LoadingTip />
      )}
    </PageContainer>
  )
}
