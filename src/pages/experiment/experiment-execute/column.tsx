import { statusColor } from '@/constants'
import { formatYTSTime, getWord, toExperimentDetail } from '@/utils'
import type { ExperimentVo } from '@types'
import { Tag } from 'antd'
import type { ColumnsType } from 'antd/lib/table'
import type { Dayjs } from 'dayjs'
export const columns: ColumnsType<ExperimentVo> = [
  {
    title: '实验流程名称',
    dataIndex: 'design_name',
    align: 'left',
    width: 180
  },
  {
    title: getWord('pages.experiment.label.name'),
    dataIndex: 'experiment_name',
    align: 'left',
    width: 180
  },
  {
    title: getWord('pages.experiment.label.status'),
    dataIndex: 'status',
    align: 'center',
    width: 150,
    render: (text) => {
      const experimentExecuteDes = {
        running: getWord('component.notification.statusValue.running'),
        hold: getWord('experiment-pending'),
        canceled: getWord('pages.projectTable.statusLabel.cancelled'),
        completed: getWord('component.notification.statusValue.success'),
        success: getWord('app.general.message.success'),
        incident: getWord('experiment-exception'),
        failed: getWord('component.notification.statusValue.failed')
      }
      return (
        text && (
          <Tag color={statusColor[text] as string}>
            {experimentExecuteDes[text]}
          </Tag>
        )
      )
    }
  },
  {
    title: getWord('experiment-actual-start-time'),
    dataIndex: 'start_time',
    align: 'left',
    width: 150,
    render: (text: Dayjs) => (text ? formatYTSTime(text) : '')
  },
  {
    title: getWord('experiment-actual-end-time'),
    dataIndex: 'end_time',
    width: 150,
    align: 'left',
    render: (text: Dayjs) => (text ? formatYTSTime(text) : '')
  },
  {
    title: getWord('pages.experiment.label.operation'),
    align: 'center',
    fixed: 'right',
    width: 120,
    render: (_, { experiment_no }) => {
      return <a onClick={() => toExperimentDetail(experiment_no)}>实验详情</a>
    }
  }
]
