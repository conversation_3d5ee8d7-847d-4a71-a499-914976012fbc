import type { IFormData } from '@/components/SearchForm/index.d'
import { getWord } from '@/utils'
export const queryData: IFormData[] = [
  {
    label: getWord('pages.experiment.label.status'),
    ctype: 'select',
    key: 'status',
    enums: [
      {
        label: getWord('component.notification.statusValue.running'),
        value: 'running'
      },
      {
        label: getWord('experiment-exception'),
        value: 'incident'
      },
      {
        label: getWord('experiment-pending'),
        value: 'hold'
      },
      {
        label: getWord('component.notification.statusValue.failed'),
        value: 'failed'
      },
      {
        label: getWord('component.notification.statusValue.success'),
        value: 'completed'
      },
      {
        label: getWord('app.general.message.success'),
        value: 'success'
      }
    ],
    placeholder: getWord('input-tip'),
    XL: { col: 5, labelWidth: 7, wrapperWidth: 17 }
  },
  {
    label: getWord('reaction-no'),
    ctype: 'input',
    key: 'rxn_no',
    placeholder: getWord('select-tip'),
    XL: { col: 7, labelWidth: 6, wrapperWidth: 17 }
  },
  {
    label: getWord('pages.experiment.label.name'),
    ctype: 'input',
    key: 'experiment_name',
    placeholder: getWord('input-tip'),
    XL: { col: 5, labelWidth: 7, wrapperWidth: 17 }
  }
]
