import { service } from '@/services/brain'
import { useEffect, useState } from 'react'

const useNextProjectNo = () => {
  const [nextProjectNo, setNextProjectNo] = useState<string>('')
  const fetchNextProjectNo = async () => {
    const { data } = await service<string>('projects/next-no').select().get()
    return (data as unknown as string) || ''
  }

  useEffect(() => {
    fetchNextProjectNo().then((no) => setNextProjectNo?.(no))
  }, [])

  return nextProjectNo
}

export default useNextProjectNo
