import { ProjectRole, query } from '@/services/brain'
import { useEffect, useState } from 'react'

const useProjectRoles = () => {
  const [allRoles, setAllRoles] = useState<ProjectRole[]>([])
  const fetchRoleOptions = async () => {
    const { data } = await query<ProjectRole>('project-roles')
      .paginate(1, 1000)
      .get()
    return data || []
  }

  useEffect(() => {
    fetchRoleOptions().then((roles) => setAllRoles?.(roles))
  }, [])

  return allRoles
}

export default useProjectRoles
