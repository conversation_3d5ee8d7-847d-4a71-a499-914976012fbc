import { use<PERSON>rainFetch } from '@/hooks/useBrainFetch'
import useOptions from '@/hooks/useOptions'
import {
  Project,
  ProjectMember,
  ProjectRole,
  query,
  service
} from '@/services/brain'
import { UserBasicInfo } from '@/types/Common'
import { getWord, isPartenerMode } from '@/utils'
import {
  EditableProTable,
  ProColumns,
  ProDescriptions,
  ProDescriptionsItemProps,
  ProDescriptionsProps
} from '@ant-design/pro-components'
import { useModel } from '@umijs/max'
import { ModalProps, message } from 'antd'
import dayjs, { Dayjs } from 'dayjs'
import React, { useState } from 'react'
import useProjectRoles from '../hooks/useProjectRoles'
import {
  ProjectListItem,
  productManagerRoleCode,
  projectTypeEnums
} from '../utils'
import ModelWithTrigger from './ModelWithTrigger'

export interface MemberEditorProps {
  projectId: number
  members: Project['project_members']
  onUpdated?: () => void
}

interface ProjectMemberFormItem extends Omit<ProjectMember, 'user_id'> {
  user_id: number
}

const editable = (member: ProjectMemberFormItem, currentUserId?: number) =>
  !(
    member.user_id === currentUserId &&
    member.role?.code === productManagerRoleCode
  )

const memberEditorColumns: (
  currentUser?: UserBasicInfo,
  defaultUserOptions?: { value: number; label: string }[]
) => ProColumns<ProjectMemberFormItem>[] = (
  currentUser,
  defaultUserOptions
) => [
  {
    title: getWord('member'),
    dataIndex: 'user_id',
    valueType: 'select',
    request: async ({ keyWords }) => {
      if (!keyWords?.length) {
        return defaultUserOptions || []
      }
      const { data } = await service<UserBasicInfo>(
        `users/search?q=${keyWords}`
      )
        .select()
        .get()

      return (
        data?.map((u) => ({
          value: u.id,
          label: `${u.username}`
        })) || []
      )
    },
    renderText: (_, record) => {
      return record?.user_info?.username
    },
    formItemProps: {
      rules: [{ required: true }]
    },
    fieldProps: {
      showSearch: true
    }
  },
  {
    title: getWord('role'),
    dataIndex: ['role', 'code'],
    valueType: 'select',
    request: async () => {
      const { data } = await query<ProjectRole>('project-roles')
        .paginate(1, 1000)
        .get()
      return (data || []).map((r) => ({ value: r.code, label: r.name_zh }))
    },
    formItemProps: { rules: [{ required: true }] }
  },
  {
    title: getWord('pages.experiment.label.operation'),
    valueType: 'option',
    render: (_, record, __, action) => {
      if (editable(record, currentUser?.id)) {
        return [
          <a key="editable" onClick={() => action?.startEditable?.(record.id)}>
            {getWord('edit')}
          </a>
        ]
      }
      return null
    }
  }
]

const MemberEditor: React.FC<MemberEditorProps> = ({
  projectId,
  members,
  onUpdated
}) => {
  const { editableConfig } = useOptions()
  const allRoles = useProjectRoles()
  const { initialState } = useModel('@@initialState')
  const optionIds = new Set(members?.map((m) => m.user_info?.id))
  const options = members
    ?.map((m) => m.user_info)
    .reduce<{ value: number; label: string }[]>((acc, cur) => {
      if (cur?.id && optionIds.has(cur.id)) {
        acc.push({ value: cur.id, label: cur.username })
        optionIds.delete(cur.id)
      }
      return acc
    }, [])

  return (
    <EditableProTable<ProjectMemberFormItem>
      rowKey="id"
      headerTitle={getWord('team-allocation')}
      recordCreatorProps={{
        creatorButtonText: getWord('add-member'),
        record: () =>
          ({
            id: (Math.random() * 1000000).toFixed(0)
          } as unknown as ProjectMemberFormItem)
      }}
      columns={memberEditorColumns(initialState?.userInfo, options)}
      value={members?.map((m) => ({
        ...m,
        user_id: Number.parseInt(m.user_id)
      }))}
      editable={{
        ...editableConfig,
        type: 'single',
        onDelete: async (_, row) => {
          const { error } = await service<ProjectMember>(
            'project-members'
          ).deleteOne(row.id)
          if (error) throw error
          onUpdated?.()
          return true
        },
        onSave: async (_, data, __, newLine) => {
          const userId =
            typeof data.user_id === 'number' ? data.user_id : data.user_info.id
          const roleId = allRoles.find((r) => r.code === data.role.code)?.id
          if (!roleId || !userId) return
          if (newLine || typeof data.id === 'string') {
            const { error } = await service<ProjectMember>(
              'project-members'
            ).create({
              project: { id: projectId } as ProjectMember['project'],
              role: { id: roleId } as ProjectMember['role'],
              user_id: `${userId}`
            } as ProjectMember)
            if (error?.message) message.error(error?.message)
            if (error) throw error
          } else {
            const { error } = await service<ProjectMember>(
              'project-members'
            ).update(data.id, {
              role: { id: roleId } as ProjectMember['role'],
              user_id: `${userId}`
            })
            if (error?.message) message.error(error?.message)
            if (error) throw error
          }
          onUpdated?.()
          return true
        }
      }}
    ></EditableProTable>
  )
}

const projectConfigColumns: (ProColumns<ProjectListItem> &
  ProDescriptionsItemProps<ProjectListItem>)[] = [
  {
    title: getWord('pages.projectTable.label.no'),
    dataIndex: 'no',
    sorter: true,
    editable: false,
    order: 10
  },
  {
    title: getWord('pages.projectTable.label.name'),
    dataIndex: 'name',
    editable: false,
    sorter: true,
    order: 10
  },
  ...(isPartenerMode()
    ? []
    : [
        {
          title: getWord('pages.projectTable.label.customer'),
          dataIndex: 'customer',
          editable: false,
          sorter: true,
          order: 10
        }
      ]),
  {
    title: getWord('pages.projectTable.label.type'),
    dataIndex: 'type',
    editable: false,
    order: 5,
    valueEnum: projectTypeEnums,
    valueType: 'select'
  },
  {
    title: getWord('pages.projectTable.label.deliveryDate'),
    valueType: 'date',
    dataIndex: 'delivery_date',
    fieldProps: {
      disabledDate: (current: Dayjs) =>
        current && current <= dayjs().startOf('day')
    }
  }
]
export interface ConfigModelProps {
  projectId: number
  trigger?: JSX.Element
  columns?: ProDescriptionsProps<ProjectListItem>['columns']
  modelProps?: ModalProps
  refreshEvent?: () => void
}

const ConfigModel: React.FC<ConfigModelProps> = ({
  trigger,
  projectId,
  columns = projectConfigColumns,
  modelProps,
  refreshEvent
}) => {
  const { editableConfig } = useOptions()
  const [project, setProject] = useState<Project>()
  const { fetch } = useBrainFetch()
  const getInfo = async (id: number): Promise<Project | undefined> => {
    const { data } = await fetch(
      service<Project>('projects')
        .selectManyByID([id])
        .populateWith('project_members', ['user_id', 'role'], true)
        .get()
    )
    return data?.[0]
  }

  return (
    <ModelWithTrigger
      trigger={trigger}
      footer={null}
      width={800}
      {...modelProps}
    >
      <ProDescriptions<ProjectListItem>
        title={project?.name || project?.no}
        column={{ xs: 1, md: 2 }}
        editable={{
          ...editableConfig,
          onSave: async (keypath, newInfo) => {
            const { error } = await service<Project>('projects').update(
              newInfo.id,
              { [keypath as string]: newInfo[keypath as keyof Project] }
            )
            if (error) throw error
            if (refreshEvent) refreshEvent()
            return true
          }
        }}
        request={async () => {
          const data = await getInfo(projectId)
          if (!data) {
            return { success: false }
          }
          setProject(data)
          return { success: true, data }
        }}
        columns={columns}
      />
      <MemberEditor
        members={project?.project_members}
        projectId={projectId}
        onUpdated={async () => {
          setProject(await getInfo(projectId))
          if (refreshEvent) refreshEvent()
        }}
      />
    </ModelWithTrigger>
  )
}

export default ConfigModel
