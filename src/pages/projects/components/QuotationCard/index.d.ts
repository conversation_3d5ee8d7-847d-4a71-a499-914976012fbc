export interface OtherCosts {
  name: string
  amount: number
  remark: string
}
export interface QuoteDetail {
  id: number
  status: string // 'editing'
  target_weight: number
  FTE_unit_price: number
  project_route_id: string
  project_compound_id: string
  quotation_summary: string
  purity: string
  other_costs: OtherCosts[]
}

export interface Quote {
  input_smiles: string
  no: string
  id: number
  no: string
  status: MoleculeStatus
  has_conformed_route?: boolean
  quotes: QuoteDetail[]
}
export interface QuotationCardProps {
  quote: Quote
}
