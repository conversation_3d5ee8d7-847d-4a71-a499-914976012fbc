@import '@/style/variables.less';
.quotationCard {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 15px;
  padding: 5px 0px 5px 20px;
  text-align: center;
  background-color: #fff;
  border: 1px solid @border-color-base;
  border-radius: 18px;
  .molecule {
    flex: 0 0 300px;
    padding-top: 5px;
    border-right: 1px dashed @border-color-base;
    .no {
      height: 22px;
      text-align: left !important;
    }
    .smilesContent {
      position: relative;
      top: -11px;
      .structure {
        width: 300px;
        height: auto;
        padding: 0px 10px;
      }
      .addButton {
        margin: 5px auto 10px;
      }
    }
  }
  .quotationList {
    flex: 1;
    max-height: 510px;
    padding-right: 20px;
    // background: yellow;
    overflow-y: scroll;
  }
}
