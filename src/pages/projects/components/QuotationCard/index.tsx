import { getWord } from '@/utils'
// import { quotationData } from '@/mock' quotes
import LazySmileDrawer from '@/components/LazySmileDrawer'
import { service } from '@/services/brain'
import { isValidArray } from '@/utils'
import { Button, Popover, Typography, message } from 'antd'
import cs from 'classnames'
import { cloneDeep } from 'lodash'
import { useEffect, useState } from 'react'
import { useAccess, useParams } from 'umi'
import PriceSummary from '../PriceSummary'
import type { QuotationCardProps } from './index.d'
import styles from './index.less'
export default function QuotationCard(props: QuotationCardProps) {
  const { quote } = props
  const { id: projectId } = useParams<{
    id: string
    compoundId: string
  }>()
  const [curQuotes, setCurQuotes] = useState()
  const access = useAccess()
  useEffect(() => {
    setCurQuotes(quote?.quotes)
  }, [quote?.quotes])

  const AddQuoteButton = ({
    disabled,
    moleculeId
  }: {
    disabled: boolean
    moleculeId: string
  }) => (
    <Button
      type="primary"
      shape="round"
      className={styles.addButton}
      disabled={disabled}
      onClick={() =>
        window.open(
          `/projects/${projectId}/quotation-records/${moleculeId}/quote-info?type=create&compound_no=${quote?.no}`,
          '_blank'
        )
      }
    >
      {getWord('menu.list.project-list.detail.addQuote')}
    </Button>
  )

  const delQuote = async (id: number) => {
    const { error } = await service(`quotes`).deleteOne(id)
    if (error?.message) return message.error(error?.message)
    message.success('删除成功')
  }

  return (
    <div className={cs(styles.quotationCard)}>
      <div className={styles.molecule}>
        <div className={styles.no}>
          {getWord('molecules-no')}
          <Typography.Text style={{ width: 230 }} ellipsis={{ tooltip: true }}>
            {quote?.no}
          </Typography.Text>
        </div>
        <div className="flex-align-items-center" style={{ height: '100%' }}>
          <div className={cs(styles.smilesContent)}>
            {quote?.input_smiles ? (
              <LazySmileDrawer
                structure={quote?.input_smiles}
                className={cs(styles.structure, 'enablePointer')}
              />
            ) : (
              ''
            )}
            {access?.authCodeList?.includes('quotation-records.addQuote') ? (
              quote?.has_conformed_route ? (
                <AddQuoteButton disabled={false} moleculeId={quote?.id} />
              ) : (
                <Popover content="该分子没有已确认路线，无法报价，请先确认路线~">
                  <div>
                    <AddQuoteButton disabled={true} />
                  </div>
                </Popover>
              )
            ) : (
              ''
            )}
          </div>
        </div>
      </div>
      <div className={styles.quotationList}>
        {isValidArray(curQuotes)
          ? curQuotes?.map((item: any) => (
              <PriceSummary
                key={item?.id}
                priceInfo={item}
                compound_no={quote?.no}
                delQuote={async () => {
                  await delQuote(item?.id)
                  let _curQuotes = cloneDeep(curQuotes).filter(
                    (v) => v?.id !== item?.id
                  )
                  setCurQuotes(_curQuotes)
                }}
              />
            ))
          : ''}
      </div>
    </div>
  )
}
