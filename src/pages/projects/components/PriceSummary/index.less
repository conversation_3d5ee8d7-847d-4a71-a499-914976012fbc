@import '@/style/variables.less';
.confirmedBorder {
  border: 1px solid @color-design-a !important;
}
.editBorder {
  border: 1px solid #f5b544 !important;
}
.priceSummary {
  height: auto;
  min-height: 100px;
  margin: 10px 0px 0 10px;
  border: 1px solid @color-text-a;
  border-radius: 10px;
  p {
    color: @color-text-a;
  }
  span {
    color: @color-text-d;
  }
  .content {
    min-height: 100px;
    padding: 10px;
  }
  .leftContent {
    flex: 1;
    padding: 12px 0;
    .selectBox {
      width: 36px;
    }
    .quoteItemInfo {
      .line {
        display: flex;
        width: 100%;
        :global {
          .ant-tag {
            display: flex;
            align-items: center;
            max-height: 24px;
          }
        }
        p {
          margin-right: 18px;
        }
      }
    }
  }

  .rightContent {
    position: relative;
    flex: 0 0 200px;
    // background: lightblue;
    .status {
      position: absolute;
      right: 0px;
    }
    .operateContent {
      position: absolute;
      right: 10px;
      bottom: 0px;
      // width: 60px;
    }
  }
}
