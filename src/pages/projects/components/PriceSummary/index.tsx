import useDownloadFile from '@/hooks/useFileDownload'
import {
  getEnvConfig,
  getWord,
  isConfirmedMolecule,
  isValidArray
} from '@/utils'
import { formatYMDHMTime } from '@/utils/time'
import { Checkbox, Popconfirm, Space, Tag } from 'antd'
import cs from 'classnames'
import { useAccess, useModel, useParams } from 'umi'
import type { PriceSummaryProps } from './index.d'
import styles from './index.less'

export default function PriceSummary(props: PriceSummaryProps) {
  const { priceInfo, compound_no } = props
  const { downloadFile, fileDownloading } = useDownloadFile()

  /* TODO prd issue 报价内容是否有长度限制？  */
  const { id: projectId } = useParams<{
    id: string
  }>()
  const { quoteCheckedListChange, quoteCheckedList, isCheckedAll } =
    useModel('quotation')
  const access = useAccess()
  const isEdit = priceInfo?.status === 'editing'
  return (
    <section
      className={cs(styles.priceSummary, {
        [styles['confirmedBorder']]: isConfirmedMolecule(priceInfo?.status),
        [styles['editBorder']]: isEdit
      })}
    >
      <article className={cs(styles.content, 'flex-justify-space-between ')}>
        <div className={cs(styles.leftContent, 'flex-align-space-between')}>
          <div className={cs(styles.selectBox, 'flex-center')}>
            <Checkbox
              disabled={priceInfo.status !== 'confirmed'}
              onChange={(e) => quoteCheckedListChange(e, priceInfo?.id)}
              checked={
                (priceInfo.status === 'confirmed' && isCheckedAll) ||
                (isValidArray(quoteCheckedList)
                  ? quoteCheckedList.includes(priceInfo?.id)
                  : false)
              }
            />
          </div>
          <div className={cs(styles.quoteItemInfo)}>
            <div className={styles.line}>
              {isConfirmedMolecule(priceInfo?.status) && (
                <Tag color="#108ee9">
                  {getWord('pages.reaction.statusLabel.confirmed')}
                </Tag>
              )}
              {priceInfo?.status === 'editing' && (
                <Tag color="gold" style={{ color: 'orange' }}>
                  {getWord('pages.reaction.statusLabel.editing')}
                </Tag>
              )}
              <Space>
                <p>
                  <span>{getWord('expected-quantity')}</span>
                  {`${priceInfo?.target_weight}${priceInfo?.target_unit}`}
                </p>
                {priceInfo?.purity ? (
                  <p>
                    <span>{getWord('purity')}：</span>
                    {priceInfo?.purity}%
                  </p>
                ) : (
                  ''
                )}
                <p>
                  <span>{getWord('route-id')}：</span>
                  {priceInfo?.project_route_id}
                </p>
                {/*  TODO https://brain-dev.labwise.cn/api/quotes?project_id=26&pagination[page]=1&pagination[pageSize]=10 响应字段 quotes中需要加一下 创建报价人 及 报价创建时间 字段；@段源 */}
              </Space>
            </div>
            <div className={styles.line}>
              {priceInfo?.creator ? (
                <p>
                  <span>{getWord('created-by')}</span>
                  {priceInfo?.creator}
                </p>
              ) : (
                ''
              )}
              {priceInfo?.createdAt ? (
                <p>
                  <span>{getWord('created-at')}</span>
                  {formatYMDHMTime(priceInfo?.createdAt)}
                </p>
              ) : (
                ''
              )}
            </div>
            {priceInfo?.quotation_summary ? (
              <div className={styles.line}>
                <p>
                  <span>{getWord('quote-sum')}</span>
                  {priceInfo?.quotation_summary}
                </p>
              </div>
            ) : (
              ''
            )}
          </div>
        </div>
        <div className={cs(styles.rightContent, 'flex-align-space-between')}>
          <div className={styles.status}>
            {access?.authCodeList?.includes(
              'quotation-records.button.viewDetail'
            ) && (
              <>
                <a
                  onClick={() =>
                    window.open(
                      `/projects/${projectId}/quotation-records/${priceInfo?.id}/quote-info?type=editor&compound_no=${compound_no}`,
                      '_blank'
                    )
                  }
                >
                  {getWord('pages.projectTable.actionLabel.viewDetail')}
                </a>
                &nbsp;&nbsp;
              </>
            )}
          </div>
          <div className={styles.operateContent}>
            {access?.authCodeList?.includes('quotation-records.download') &&
              priceInfo.status === 'confirmed' && (
                <>
                  <a
                    className={fileDownloading ? 'disabledTip' : ''}
                    onClick={async () =>
                      await downloadFile(
                        `${getEnvConfig().apiBase}/api/quote/download/${
                          priceInfo?.id
                        }`,
                        `${priceInfo?.id}_${priceInfo?.target_weight}${priceInfo?.target_unit}`
                      )
                    }
                  >
                    {getWord('download-quatation')}
                  </a>
                  &nbsp;&nbsp;
                </>
              )}
            {access?.authCodeList?.includes(
              'quotation-records.button.delDetail'
            ) && (
              <Popconfirm
                title={getWord('delete-quotation')}
                okText={getWord('pages.route.edit.label.confirm')}
                cancelText={getWord('pages.experiment.label.operation.cancel')}
                onConfirm={props?.delQuote}
              >
                <a>{getWord('del')}</a>
              </Popconfirm>
            )}
          </div>
        </div>
      </article>
    </section>
  )
}
