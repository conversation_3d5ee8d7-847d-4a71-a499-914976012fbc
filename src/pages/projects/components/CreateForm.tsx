import { Project, service } from '@/services/brain'
import { UserBasicInfo } from '@/types/Common'
import { getWord, isEN, isPartenerMode } from '@/utils'
import { CloseCircleOutlined, PlusOutlined } from '@ant-design/icons'
import {
  FormListActionType,
  ModalForm,
  ProFormDatePicker,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
  ProFormText
} from '@ant-design/pro-components'
import { useAccess, useModel } from '@umijs/max'
import { App, Button, Col, Form, Row } from 'antd'
import dayjs from 'dayjs'
import { useEffect, useRef, useState } from 'react'
import useNextProjectNo from '../hooks/useProjectDefaultNo'
import useProjectRoles from '../hooks/useProjectRoles'
import { productManagerRoleCode, projectTypeEnums } from '../utils'
import './index.less'

interface CreateFormProps {
  onSuccess?: () => void
  openEvent?: { open: boolean }
}

export const CreateForm: React.FC<CreateFormProps> = ({
  onSuccess,
  openEvent
}) => {
  const access = useAccess()

  const { message, notification } = App.useApp()
  const [open, setOpen] = useState<boolean>(false)
  const actionRef =
    useRef<FormListActionType<{ user_id: string; role: string }>>()
  const allRoles = useProjectRoles()
  const nextNo = useNextProjectNo()
  const { initialState } = useModel('@@initialState')
  const { userInfo } = initialState

  useEffect(() => {
    if (openEvent?.open) {
      setOpen(true)
    }
  }, [openEvent])

  return (
    <ModalForm<Project>
      className="create-form-root"
      open={open}
      onOpenChange={setOpen}
      title={getWord('pages.projectTable.modelLabel.newProject')}
      trigger={
        access?.authCodeList?.includes('projects.button.add') ? (
          <Button
            type="primary"
            key="primary"
            title={getWord('no-permission-tip')}
          >
            <PlusOutlined />{' '}
            {getWord('pages.projectTable.modelLabel.newProject')}
          </Button>
        ) : (
          <></>
        )
      }
      autoFocusFirstInput
      layout="horizontal"
      grid
      labelCol={{ span: 6 }}
      validateMessages={{
        required: '${label}' + `${isEN() ? ' ' : ''}${getWord('is-required')}`
      }}
      modalProps={{ destroyOnClose: true, centered: true }}
      submitTimeout={2000}
      onFinish={async (project) => {
        const { error } = await service<Project>('projects').create({
          ...project,
          customer: isPartenerMode() ? '-' : project.customer
        })
        if (error) {
          const e = error.details.errors[0]
          let description = error.message
          if (
            e.message === 'This attribute must be unique' &&
            e.path.includes('no')
          ) {
            description = getWord('project-ID-duplicated')
          }
          notification.error({
            message: getWord('project-create-failed'),
            description
          })
          return false
        }

        message.success(
          `${getWord('project')}${project.name || project.no}${getWord(
            'created-succeed'
          )}`
        )
        onSuccess?.()
        return true
      }}
    >
      <ProFormGroup title={getWord('pages.project.settings')}>
        <ProFormText
          colProps={{ md: 12 }}
          name="no"
          label={getWord('project-ID')}
          required
          initialValue={nextNo}
          rules={[{ required: true }]}
        />
        <ProFormText
          colProps={{ md: 12 }}
          name="name"
          label={getWord('project-name')}
        />
        {isPartenerMode() ? null : (
          <ProFormText
            colProps={{ md: 12 }}
            name="customer"
            label={getWord('pages.projectTable.label.customer')}
            rules={[{ required: true }]}
          />
        )}
        <ProFormSelect
          colProps={{ md: 12 }}
          valueEnum={projectTypeEnums}
          name="type"
          label={getWord('pages.projectTable.label.type')}
          initialValue={isPartenerMode() ? 'personal' : undefined}
          required
          rules={[{ required: true }]}
        />
        <ProFormDatePicker
          colProps={{ md: 12 }}
          name="delivery_date"
          label={getWord('project-delivery-date')}
          fieldProps={{
            disabledDate: (current) =>
              current && current <= dayjs().startOf('day')
          }}
        />
      </ProFormGroup>
      <ProFormGroup title={getWord('team-allocation')}>
        <Row className="member-title">
          <Col span={12}>
            <Form.Item
              colon={false}
              required
              label={getWord('member')}
              labelCol={{ span: 24 }}
            />
          </Col>
          <Col span={12}>
            <Form.Item colon={false} required label={getWord('role')} />
          </Col>
        </Row>
        <ProFormList
          className="member-list"
          name="project_members"
          deleteIconProps={{ Icon: CloseCircleOutlined }}
          copyIconProps={false}
          colProps={{ span: 24 }}
          min={1}
          creatorButtonProps={{
            creatorButtonText: getWord('add-member')
          }}
          actionRef={actionRef}
          actionRender={(field, _, doms) => {
            if (field.key === 0) return []
            return doms
          }}
          actionGuard={{
            beforeRemoveRow: (index) => {
              const i = typeof index === 'object' ? index[0] : index
              const role = actionRef.current?.get(i)?.role
              if (!role) return true

              const allItems = actionRef.current
                ?.getList()
                ?.filter((v) => v.role === role)
              if (role === productManagerRoleCode) {
                if (!allItems?.length || allItems.length <= 1) {
                  message.error(
                    <span>
                      项目中至少需要一个
                      {getWord(`pages.projectTable.label.${role}`)}
                    </span>
                  )
                  return false
                }
              }
              return true
            }
          }}
          initialValue={[
            {
              user_id: `${userInfo?.id}`,
              role: allRoles.find((r) => r.code === productManagerRoleCode)?.id
            }
          ]}
        >
          {(_, index) => (
            <ProFormGroup labelLayout="inline">
              <ProFormSelect
                name="user_id"
                label={getWord('member')}
                colProps={{ md: 12 }}
                required
                rules={[{ required: true }]}
                showSearch
                debounceTime={300}
                request={async ({ keyWords }) => {
                  if (!keyWords?.length) {
                    return [
                      { value: `${userInfo?.id}`, label: userInfo?.username }
                    ]
                  }
                  const { data } = await service<UserBasicInfo>(
                    `users/search?q=${keyWords}`
                  )
                    .select()
                    .get()
                  return (
                    data?.map((u) => ({
                      value: `${u.id}`,
                      label: `${u.username}`
                    })) || []
                  )
                }}
              />
              <ProFormSelect
                colProps={{ md: 12 }}
                name="role"
                label={getWord('role')}
                required
                disabled={index < 1}
                rules={[{ required: true }]}
                request={async () =>
                  allRoles.map((role) => ({
                    value: role.id,
                    label: role.name_zh
                  }))
                }
              />
            </ProFormGroup>
          )}
        </ProFormList>
      </ProFormGroup>
    </ModalForm>
  )
}

export default CreateForm
