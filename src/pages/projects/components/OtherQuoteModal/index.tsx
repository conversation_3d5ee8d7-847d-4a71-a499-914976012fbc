import { AmendOtherQuote, OtherQuote } from '@/models/quotation'
import { getWord } from '@/utils'
import {
  ModalForm,
  ProFormDigit,
  ProFormText
} from '@ant-design/pro-components'
import { Button, Col, Row, Select } from 'antd'
import { useEffect, useState } from 'react'
import {  useAccess } from 'umi'
import type { OtherQuoteModalProps } from './index.d'
export default function OtherQuoteModal(props: OtherQuoteModalProps) {
  const { form, onFinish } = props

  useEffect(() => {
    form.setFieldsValue(props?.initalValues)
  }, [props?.initalValues])
  const [weighUnit, setWeighUnit] = useState<'mg' | 'g'>('g')
  const access = useAccess()
  return (
    <ModalForm<OtherQuote>
      layout="horizontal"
      width={680}
      title={getWord('other-weight-quotate')}
      trigger={
        access?.authCodeList?.includes(
          'quotation-records.button.otherWeightQuotate'
        ) ? (
          <Button>
            {getWord('other-weight-quotate')}
          </Button>
        ) : (
          <></>
        )
      }
      form={form}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: () => console.log('run')
      }}
      submitTimeout={2000}
      onFinish={async (values) => {
        onFinish({
          target_weight: values?.target_weight,
          target_unit: weighUnit
        } as AmendOtherQuote)
        return true
      }}
    >
      <Row>
        <Col span={12}>
          <ProFormText
            name="compound_no"
            label={getWord('molecules-no')}
            rules={[{ required: true }]}
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 14 }}
            disabled
          />
        </Col>
        <Col span={12}>
          <ProFormDigit
            name="target_weight"
            label={getWord('weight')}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            addonAfter={
              <Select
                value={weighUnit}
                style={{ width: '60px' }}
                onChange={(e) => setWeighUnit(e)}
              >
                <Select.Option value="g">g</Select.Option>
                <Select.Option value="mg">mg</Select.Option>
              </Select>
            }
            rules={[{ required: true }]}
            min={0.1}
          />
        </Col>
      </Row>
      <Row>
        <Col span={12}>
          <ProFormDigit
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 14 }}
            name="purity"
            rules={[{ required: true }]}
            label={`${getWord('purity')}(%)`}
            min={0.1}
            max={100}
            disabled
          />
        </Col>
        <Col span={12}>
          <ProFormDigit
            name="ratio"
            label={getWord('coefficient')}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 13 }}
            rules={[{ required: true }]}
            min={0.1}
            max={100}
            disabled
          />
        </Col>
      </Row>
    </ModalForm>
  )
}
