@import '@/style/variables.less';
.moleculeCard {
  flex: 1 0 25%;
  min-width: 256px;
  min-height: 426px;
  margin-top: 10px;
  .desItem {
    min-width: 206px;
    padding-bottom: 8px;
    overflow-x: hidden;
    :global {
      .ant-tag {
        margin-inline-end: 0px !important;
      }
    }
  }
  .routesAmount {
    .desItem;
    padding-bottom: 0px;
  }
  :global {
    .ant-card-head {
      padding: 0 12px;
    }
    .ant-card-body {
      padding: 4px 16px 2px;
    }
  }
  .moleculeNo {
    color: black;
    font-weight: 500;
    font-size: 16px;
  }
  .routesNum {
    height: 22px;
    margin-top: -10px;
    margin-bottom: 10px;
  }
  .labelItem {
    width: max-content;
    min-width: 48px;
    color: #626262;
    font-weight: 400;
    font-size: 14px;
  }
  .labelItem_en {
    .labelItem;
    min-width: 48px;
  }
  .valueItem {
    color: @brand-primary;
  }
  .normalText {
    color: black;
  }
  .created {
    border: 1px solid @color-border-created;
  }
  .designing {
    border: 1px solid @color-border-designing;
  }
  .synthesizing {
    border: 1px solid @color-border-synthsizing;
  }
  .finished {
    border: 1px solid @color-border-finished;
  }
  .canceled {
    border: 1px solid @color-border-canceled;
  }
}
