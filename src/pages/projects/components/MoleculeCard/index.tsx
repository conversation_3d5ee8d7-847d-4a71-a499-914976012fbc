import LazySmileDrawer from '@/components/LazySmileDrawer'
import { priorityOptions } from '@/constants/options'
import useOptions from '@/hooks/useOptions'
import {
  MoleculeStatusUpdateParams,
  ProjectCompound,
  ProjectCompoundStatus,
  service
} from '@/services/brain'
import { getWord, isEN } from '@/utils'
import { Card, Col, Row, Select, Tag, Typography, message } from 'antd'
import cs from 'classnames'
import { history, useModel, useParams } from 'umi'
import type { MoleculeCardProps } from './index.d'
import styles from './index.less'

export default function MoleculeCard(props: MoleculeCardProps) {
  const { detailData } = props
  const { userList } = useModel('project')
  const { id: projectId } = useParams<{ id: string }>()
  const { moleculeStatusOptions, typeMap } = useOptions()
  const handleOption = (status: ProjectCompoundStatus) => {
    function filterOption(compareKeys: string[]) {
      return moleculeStatusOptions.filter((e) => compareKeys.includes(e.value))
    }
    switch (status) {
      case 'created':
        return filterOption(['created', 'designing', 'canceled'])
      case 'designing':
        return filterOption(['designing', 'canceled', 'synthesizing'])
      case 'synthesizing':
        return filterOption(['synthesizing', 'canceled', 'finished'])
      case 'finished':
        return filterOption(['finished'])
      default:
        return filterOption(['canceled'])
    }
    return moleculeStatusOptions
  }

  return (
    <div>
      <Row gutter={16}>
        {detailData.map((curData: ProjectCompound, index: number) => {
          return (
            <Col
              lg={6}
              md={8}
              key={`${index}-detailCard`}
              className={cs(styles.moleculeCard)}
            >
              <Card
                className={cs(
                  'enablePointer',
                  curData.status ? styles[curData.status] : null
                )}
                bordered={false}
                onClick={() =>
                  history.push(
                    `/projects/${projectId}/compound/${curData?.id}?page=1&pageSize=10`
                  )
                }
              >
                <LazySmileDrawer
                  structure={curData?.compound?.smiles || ''}
                  className={cs(styles.structure, 'enablePointer')}
                  height={300}
                />
                <div
                  className={cs(
                    'flex-justify-space-between flex-align-items-center',
                    styles.desItem
                  )}
                >
                  <Typography.Text
                    style={{ width: 172 }}
                    className={styles.moleculeNo}
                    ellipsis={{ tooltip: curData?.no }}
                  >
                    {curData?.no}
                  </Typography.Text>
                  <Tag color="green">{typeMap[curData?.type]}</Tag>
                </div>
                <div
                  className={cs(
                    styles.routesNum,
                    styles.labelItem,
                    'display-flex'
                  )}
                >
                  <div>
                    {getWord('aiGenerated')}{' '}
                    {curData?.retro_backbones_number || 0} {isEN() ? '' : '条'}
                  </div>
                  &nbsp;&nbsp;•&nbsp;&nbsp;
                  <div>
                    {getWord('myRoutes')} {curData?.project_routes_number || 0}{' '}
                    {isEN() ? '' : '条'}
                  </div>
                </div>
                {isEN() ? (
                  <>
                    <div
                      className={cs('flex-align-items-center', styles.desItem)}
                      onClick={(event) => event.stopPropagation()}
                    >
                      <div className={styles.labelItem_en}>
                        {getWord('status')}
                      </div>
                      &nbsp;
                      <div
                        className={styles.normalText}
                        style={{ width: '100%' }}
                      >
                        <Select
                          size="small"
                          defaultValue={curData?.status}
                          style={{ width: '100%' }}
                          onChange={(status) =>
                            props?.statusChange(status, curData?.id)
                          }
                          options={handleOption(curData?.status)}
                        />
                      </div>
                    </div>
                    <div
                      className={cs('flex-align-items-center', styles.desItem)}
                      onClick={(event) => event.stopPropagation()}
                    >
                      <div className={styles.labelItem_en}>
                        {getWord('Priority')}
                      </div>
                      &nbsp;
                      <div
                        className={styles.normalText}
                        style={{ width: '100%' }}
                      >
                        <Select
                          size="small"
                          disabled={props?.disabled}
                          defaultValue={curData?.priority}
                          style={{ width: '100%' }}
                          onChange={(value) =>
                            props?.priorityChange(value, curData?.id)
                          }
                          options={priorityOptions}
                        />
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <Row
                      className={cs(styles.desItem)}
                      onClick={(event) => event.stopPropagation()}
                    >
                      <div className={styles.labelItem}>
                        {getWord('status')}
                      </div>
                      &nbsp;
                      <Select
                        size="small"
                        defaultValue={curData?.status}
                        style={{ width: isEN() ? '100%' : '130px' }}
                        onChange={(status) =>
                          props?.statusChange(status, curData?.id)
                        }
                        options={handleOption(curData?.status)}
                      />
                      &nbsp;
                    </Row>
                    <Row
                      className={cs(styles.desItem)}
                      onClick={(event) => event.stopPropagation()}
                    >
                      <div className={styles.labelItem}>
                        {getWord('Priority')}
                      </div>
                      &nbsp;
                      <Select
                        size="small"
                        disabled={props?.disabled}
                        defaultValue={curData?.priority}
                        style={{ width: isEN() ? '100%' : '130px' }}
                        onChange={(value) =>
                          props?.priorityChange(value, curData?.id)
                        }
                        options={priorityOptions}
                      />
                    </Row>
                  </>
                )}
                <div
                  className={cs('flex-align-items-center', styles.desItem)}
                  onClick={(event) => event.stopPropagation()}
                >
                  <div
                    className={isEN() ? styles.labelItem_en : styles.labelItem}
                    style={{ display: 'flex' }}
                  >
                    {getWord('pages.experiment.label.owner')}
                  </div>
                  &nbsp;
                  <div className={styles.normalText} style={{ width: '100%' }}>
                    <Select
                      size="small"
                      defaultValue={curData?.director_id}
                      style={{ width: isEN() ? '100%' : '130px' }}
                      onChange={async (roleId) => {
                        const { error } =
                          await service<MoleculeStatusUpdateParams>(
                            'project-compounds'
                          ).update(curData?.id, {
                            director_id: roleId
                          })
                        if (error) {
                          message.error(error?.message)
                        } else {
                          message.success(getWord('success-update'))
                        }
                      }}
                      options={userList}
                    />
                  </div>
                </div>
              </Card>
            </Col>
          )
        })}
      </Row>
    </div>
  )
}
