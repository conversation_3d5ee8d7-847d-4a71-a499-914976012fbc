import { ProjectStatusAudit } from '@/services/brain'
import { getWord } from '@/utils'
import { ProDescriptions } from '@ant-design/pro-components'
import React from 'react'

export interface StatusAuditProps {
  audit?: ProjectStatusAudit
}

const StatusAudit: React.FC<StatusAuditProps> = ({ audit }) => {
  if (!audit) return null
  return (
    <ProDescriptions<ProjectStatusAudit>
      request={async () => ({ data: audit, success: true })}
      layout="vertical"
      column={1}
      style={{ width: 250 }}
    >
      <ProDescriptions.Item
        dataIndex="createdAt"
        label={getWord('modification-time')}
        valueType="dateTime"
      />
      <ProDescriptions.Item
        dataIndex="user_id"
        label={getWord('modifier')}
        valueType="text"
      />
      <ProDescriptions.Item
        dataIndex="note"
        label={getWord('modification-reason')}
        valueType="text"
      />
    </ProDescriptions>
  )
}

export default StatusAudit
