import { Modal, ModalProps } from 'antd'
import React, { useMemo, useState } from 'react'

export interface ModelWithTriggerProps
  extends React.PropsWithChildren<ModalProps> {
  trigger?: JSX.Element
}

const ModelWithTrigger: React.FC<ModelWithTriggerProps> = ({
  trigger,
  children,
  onCancel,
  ...modelProps
}) => {
  const [open, setOpen] = useState<boolean>(false)

  const triggerDom = useMemo(() => {
    if (!trigger) return
    return React.cloneElement(trigger, {
      key: 'trigger',
      ...trigger.props,
      onClick: async (e: any) => {
        setOpen(!open)
        trigger.props?.onClick?.(e)
      }
    })
  }, [setOpen, trigger, open])

  return (
    <>
      {triggerDom}
      <Modal
        onCancel={(e) => {
          onCancel?.(e)
          setOpen(false)
        }}
        open={open}
        {...modelProps}
      >
        {children}
      </Modal>
    </>
  )
}

export default ModelWithTrigger
