import { getWord, isEN } from '@/utils'
import { ModalForm, ProFormTextArea } from '@ant-design/pro-components'

import { Form, Typography } from 'antd'
import { ReactElement, useEffect, useState } from 'react'
import type { StatusUpdateModelProps } from './index.d'
const StatusUpdateModel = <T extends string>({
  status,
  operateTargetName,
  onFinished,
  onCancel,
  trigger
}: StatusUpdateModelProps<T>): ReactElement<
  StatusUpdateModelProps<T>
> | null => {
  const [form] = Form.useForm<{ status_update_note: string }>()
  const [open, setOpen] = useState<boolean>(false)
  useEffect(() => setOpen(trigger?.open || false), [trigger])
  const isCancelType = ['cancelled', 'canceled'].includes(status)
  const renderTitle = () => {
    switch (status) {
      case 'canceled':
        return getWord(`cancel-molecule`)
      case 'finished':
        return getWord(`complete-molecule`)
      case 'cancelled':
        return getWord(`cancel-projects`)
      case 'started':
        return getWord(`start-project`)
      default:
        return (
          <>
            {getWord(`pages.projectTable.statusChangeLabel.${status}`)}
            {isEN() ? ' ' : ''}
            {operateTargetName}
          </>
        )
    }
  }

  const renderLabel = () => {
    switch (status) {
      case 'canceled':
      case 'cancelled':
        return getWord('cancel-reason')
      default:
        return (
          <>
            {getWord(`pages.projectTable.statusChangeLabel.${status}`)}
            {isEN() ? ' ' : ''}
            {getWord('reason')}
          </>
        )
    }
  }

  const confirmDes = () => {
    switch (status) {
      case 'finished':
        return getWord(`complete-molecule-tip`)
      case 'started':
        return getWord(`start-project-tip`)
      case 'canceled':
        return getWord('cancel-reason')
      default:
        return (
          <>
            {getWord('confirm')}
            {isEN() ? ' ' : ''}
            {getWord(`pages.projectTable.statusChangeLabel.${status}`)}
            {isEN() ? ' ' : ''}
            {operateTargetName}？
          </>
        )
    }
  }

  return (
    <ModalForm<{ status_update_note: string }>
      title={renderTitle()}
      width={400}
      open={open}
      onOpenChange={(o) => {
        if (!o) onCancel?.()
        setOpen(o)
      }}
      form={form}
      autoFocusFirstInput
      modalProps={{ destroyOnClose: true, centered: true }}
      onFinish={async ({ status_update_note }) => {
        await onFinished?.({
          status,
          status_update_note
        })
        setOpen(false)
      }}
    >
      {isCancelType && (
        <Typography.Text type="danger">
          {getWord(
            status === 'cancelled'
              ? 'cancel-projects-note'
              : 'cancel-molecule-tip'
          )}
        </Typography.Text>
      )}
      {isCancelType || status === 'holding' ? (
        <ProFormTextArea
          width="md"
          name="status_update_note"
          rules={[
            {
              required: true,
              message: getWord('enter-reason')
            }
          ]}
          label={renderLabel()}
        />
      ) : (
        confirmDes()
      )}
    </ModalForm>
  )
}

export default StatusUpdateModel
