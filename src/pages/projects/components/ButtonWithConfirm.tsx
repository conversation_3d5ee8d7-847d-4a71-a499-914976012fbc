import { Button, ButtonProps, Popconfirm } from 'antd'
import React, { useState } from 'react'

export interface ButtonWithConfirmProps {
  buttonText: React.ReactNode
  title: string
  description: string
  buttonProps?: ButtonProps
  disabled?: boolean
  onConfirm?: () => void
  type?: 'link' | 'text' | 'ghost' | 'default' | 'primary' | 'dashed'
}

const ButtonWithConfirm: React.FC<ButtonWithConfirmProps> = ({
  title,
  description,
  onConfirm,
  type,
  disabled,
  buttonText,
  buttonProps
}) => {
  const [open, setOpen] = useState<boolean>(false)
  return (
    <Popconfirm
      title={title}
      description={description}
      open={open}
      onOpenChange={setOpen}
      onConfirm={() => {
        setOpen(false)
        onConfirm?.()
      }}
      onCancel={() => setOpen(false)}
    >
      <Button
        {...buttonProps}
        type={type}
        onClick={() => setOpen(true)}
        disabled={disabled}
      >
        {buttonText}
      </Button>
    </Popconfirm>
  )
}

export default ButtonWithConfirm
