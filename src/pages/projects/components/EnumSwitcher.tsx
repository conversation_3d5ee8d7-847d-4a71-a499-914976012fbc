import { DownOutlined } from '@ant-design/icons'
import { Dropdown, Space } from 'antd'

export interface EnumSwitcherProps<T extends string> {
  currentValue: T
  avalibleValues: T[]
  onSelect: (s: T) => void
  valueRender?: (value: T) => JSX.Element
}

const EnumSwitcher = <T extends string>({
  currentValue,
  avalibleValues,
  onSelect,
  valueRender = (s: T) => <>{s}</>
}: EnumSwitcherProps<T>): JSX.Element => {
  if (!avalibleValues.length) {
    return valueRender(currentValue)
  }

  const menuItems = avalibleValues.map((v) => ({
    label: valueRender(v),
    key: v
  }))
  return (
    <Dropdown
      menu={{ items: menuItems, onClick: (info) => onSelect(info.key as T) }}
      trigger={['click']}
    >
      <Space>
        {valueRender(currentValue)}
        <DownOutlined />
      </Space>
    </Dropdown>
  )
}

export default EnumSwitcher
