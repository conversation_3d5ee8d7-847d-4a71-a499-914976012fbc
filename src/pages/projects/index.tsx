import StatusRender from '@/components/StatusRender'
import StatusTip from '@/components/StatusTip'
import { useFormStorage } from '@/hooks/useFormStorage'
import {
  Project,
  ProjectSearchParams,
  ProjectStatus,
  ProjectStatusUpdateParams,
  defaultPageSize,
  query,
  service
} from '@/services/brain'
import { UserBasicInfo } from '@/types/Common'
import { getWord, isEN, isPartenerMode } from '@/utils'
import type {
  ActionType,
  ProColumns,
  ProDescriptionsItemProps,
  ProTableProps
} from '@ant-design/pro-components'
import { PageContainer, ProTable } from '@ant-design/pro-components'
import { history, useModel } from '@umijs/max'
import { App, ConfigProvider, Popover } from 'antd'
import React, { useRef, useState } from 'react'
import ConfigModel from './components/ConfigModel'
import CreateForm from './components/CreateForm'
import EnumSwitcher from './components/EnumSwitcher'
import StatusAudit from './components/StatusAudit'
import StatusUpdateModel from './components/StatusUpdateModel'
import type { StatusConfirmValue } from './components/StatusUpdateModel/index.d'
import styles from './index.less'
import {
  ProjectListItem,
  parseProjectResponse,
  productManagerRoleCode,
  projectTypeEnums,
  statusTransformMap
} from './utils'

const displayAuditStatus: readonly ProjectStatus[] = [
  'cancelled',
  'holding'
] as const

const TableList: React.FC = () => {
  const actionRef = useRef<ActionType>()
  const [openEvent, setOpenEvent] = useState<{ open: boolean }>()
  const [update, setUpdate] = useState<{
    id: number
    status: ProjectStatus
  } | null>(null)
  const { initialState } = useModel('@@initialState')
  const { userInfo } = initialState
  const canCreateProject = ['leaders', 'pm'].includes(userInfo?.role?.name)
  const [, set] = useFormStorage()
  const [listLoading, setListLoading] = useState<boolean>(false)
  const request: ProTableProps<
    ProjectListItem,
    API.PageParams & ProjectSearchParams
  >['request'] = async (params, sort, filter) => {
    setListLoading(true)
    const req = query<Project>('projects')
      .paginate(params.current || 1, params.pageSize || defaultPageSize)
      .populateWith('project_status_audits')
      .populateWith('project_members', ['user_id', 'role'], true)
      .populateWith('project_compounds', ['id', 'type'])
      .sortBy([
        ...Object.entries(sort || {}).map(([key, order]) => ({
          field: key as keyof Project,
          order: order === 'ascend' ? ('asc' as const) : ('desc' as const)
        })),
        { field: 'createdAt', order: 'desc' },
        { field: 'updatedAt', order: 'desc' }
      ])
    for (const key of Object.keys(params)) {
      const fieldName = key as keyof ProjectSearchParams
      const value = params[fieldName]
      if (!value) continue
      switch (fieldName) {
        case 'delivery_date':
        case 'start_datetime':
        case 'end_datetime':
          req.between(fieldName, value as string[])
          break
        case 'customer':
        case 'name':
        case 'no':
          req.contains(fieldName, value as string)
          break
        case 'managers':
          req.filterDeep('project_members.user_id', 'contains', value)
          req.filterDeep(
            'project_members.role.code',
            'eq',
            productManagerRoleCode
          )
          break
        case 'status':
        case 'type':
          if (value.length) {
            req.filterDeep(fieldName, 'in', value as string[])
          }
          break
        default:
          break
      }
    }
    if (filter && Object.entries(filter).length) {
      Object.entries(filter).forEach(([key, value]) => {
        if (value) {
          req.filterDeep(key, 'in', value)
        }
      })
    }
    req.filterDeep('personal_owner.id', 'null' as any, 'true')

    const { error, data, meta } = await req.get()
    setListLoading(false)
    if (error) throw error
    if (!data) throw new Error('No data returned')
    const parsedData = data.map(parseProjectResponse)
    return { data: parsedData, success: true, total: meta?.pagination.total }
  }

  const columns: (ProColumns<ProjectListItem> &
    ProDescriptionsItemProps<ProjectListItem>)[] = [
    {
      title: getWord('pages.projectTable.label.no'),
      dataIndex: 'no',
      sorter: true,
      editable: false,
      order: 9
    },
    {
      title: getWord('project-name'),
      dataIndex: 'name',
      width: 120,
      editable: false,
      sorter: true,
      order: 8
    },
    ...(isPartenerMode()
      ? []
      : [
          {
            title: getWord('pages.projectTable.label.customer'),
            dataIndex: 'customer',
            editable: false,
            sorter: true,
            order: 7
          }
        ]),
    {
      title: getWord('pages.projectTable.label.type'),
      dataIndex: 'type',
      valueType: 'checkbox',
      editable: false,
      sorter: true,
      valueEnum: projectTypeEnums,
      hideInSearch: isPartenerMode(),
      order: 2
    },
    {
      order: 1,
      title: getWord('pages.projectTable.label.status'),
      dataIndex: 'status',
      valueType: 'checkbox',
      hideInForm: true,
      hideInDescriptions: true,
      sorter: true,
      valueEnum: {
        created: {
          text: getWord('pages.projectTable.statusLabel.created')
        },
        started: {
          text: getWord('pages.projectTable.statusLabel.started')
        },
        finished: {
          text: getWord('component.notification.statusValue.success')
        },
        holding: {
          text: getWord('pages.projectTable.statusLabel.holding')
        },
        cancelled: {
          text: getWord('pages.projectTable.statusLabel.cancelled')
        }
      },
      render: (_, { id, status, last_audit }: ProjectListItem) => (
        <Popover
          placement="right"
          content={
            status && displayAuditStatus.includes(status) ? (
              <StatusAudit audit={last_audit} />
            ) : undefined
          }
          trigger="hover"
        >
          <div style={{ width: 'fit-content' }}>
            <EnumSwitcher<ProjectStatus>
              currentValue={status || 'created'}
              valueRender={(s) => (
                <StatusRender
                  labelPrefix="pages.projectTable.statusLabel"
                  status={s as ProjectStatus}
                />
              )}
              avalibleValues={status ? statusTransformMap[status] : []}
              onSelect={async (status) => setUpdate({ id, status })}
            />
          </div>
        </Popover>
      )
    },
    {
      title: getWord('pages.projectTable.label.compoundNumber'),
      width: isEN() ? 140 : 80,
      valueType: 'digit',
      dataIndex: 'project_compounds',
      renderText: (_, project) =>
        project.project_compounds?.filter((c) => c.type !== 'temp_block')
          ?.length || 0,
      hideInSearch: true
    },
    {
      title: getWord('pages.projectTable.label.pm'),
      dataIndex: 'managers',
      width: 120,
      order: 5,
      renderText: (_, project) =>
        project.managers?.map((l) => l.user_info?.username).join(', '),
      hideInDescriptions: true,
      valueType: 'select',
      request: async ({ keyWords }) => {
        const { data } = await service<UserBasicInfo>(
          `users/search?q=${keyWords}`
        )
          .select()
          .get()
        return (
          data?.map((u) => ({
            value: `${u.id}`,
            label: `${u.username}`
          })) || []
        )
      },
      formItemProps: {
        rules: [{ required: true }]
      },
      fieldProps: {
        showSearch: true
      }
    },
    {
      title: getWord('pages.experiment.label.actualStartTime'),
      valueType: 'date',
      width: isEN() ? 120 : 100,
      dataIndex: 'start_datetime',
      hideInSearch: true,
      sorter: true,
      hideInDescriptions: true
    },
    {
      title: getWord('pages.projectTable.label.deliveryDate'),
      valueType: 'date',
      dataIndex: 'delivery_date',
      sorter: true,
      hideInSearch: true
    },

    {
      title: getWord('pages.experiment.label.actualEndTime'),
      valueType: 'date',
      width: isEN() ? 120 : 100,
      dataIndex: 'end_datetime',
      hideInSearch: true,
      sorter: true,
      hideInDescriptions: true
    },
    {
      title: getWord('pages.experiment.label.operation'),
      dataIndex: 'option',
      valueType: 'option',
      hideInDescriptions: true,
      render: (_, project) => {
        return [
          <a
            key="view-detail"
            onClick={() => {
              history.push(`/projects/${project?.id}`)
            }}
          >
            {getWord('pages.projectTable.actionLabel.viewDetail')}
          </a>,
          <ConfigModel
            key="config"
            projectId={project.id}
            trigger={<a>{getWord('pages.projectTable.actionLabel.config')}</a>}
            modelProps={{
              onCancel: () => actionRef.current?.reload()
            }}
          />
        ]
      }
    },
    {
      title: getWord('pages.projectTable.label.deliveryDate'),
      valueType: 'dateRange',
      dataIndex: 'delivery_date',
      hideInTable: true,
      hideInDescriptions: true,
      order: 6
    },
    {
      title: getWord('pages.experiment.label.actualStartTime'),
      valueType: 'dateRange',
      dataIndex: 'start_datetime',
      hideInDescriptions: true,
      hideInTable: true,
      order: 4
    },
    {
      title: getWord('pages.experiment.label.actualEndTime'),
      valueType: 'dateRange',
      dataIndex: 'end_datetime',
      hideInDescriptions: true,
      hideInTable: true,
      order: 3
    }
  ]
  const { message } = App.useApp()

  const createModal = (
    <>
      <CreateForm
        key="create-form"
        openEvent={openEvent}
        onSuccess={() => actionRef.current?.reload()}
      />
    </>
  )

  const customEmpty = (name?: string) => {
    if (name !== 'Table') return
    return (
      <StatusTip
        des={
          <>
            {getWord('create-project-tip-I')}
            {canCreateProject && !listLoading ? (
              <>
                {getWord('create-project-tip-II')}
                <a onClick={() => setOpenEvent({ open: true })}>
                  {getWord('create-project-tip-target')}
                </a>
                {getWord('create-project-tip-III')}
              </>
            ) : (
              ''
            )}
          </>
        }
      />
    )
  }

  return (
    <PageContainer className={styles.projects}>
      <ConfigProvider renderEmpty={customEmpty}>
        <ProTable<Project, API.PageParams>
          headerTitle={getWord('pages.projectTable.title')}
          form={{
            onValuesChange: async (_, values) => set(values),
            initialValues: {
              status: ['created', 'started', 'finished', 'holding']
            }
          }}
          request={request}
          actionRef={actionRef}
          rowKey="id"
          search={{ labelWidth: 120 }}
          columns={columns}
          toolBarRender={() => [createModal]}
          toolbar={{ settings: [] }}
          pagination={{ pageSize: defaultPageSize }}
        />
      </ConfigProvider>
      {update && (
        <StatusUpdateModel
          operateTargetName={getWord('menu.list.project-list')}
          status={update.status}
          trigger={{ open: !!update }}
          onCancel={() => setUpdate(null)}
          onFinished={async (values: StatusConfirmValue<ProjectStatus>) => {
            const { error } = await service<ProjectStatusUpdateParams>(
              'projects'
            ).update(update.id, {
              status: values?.status,
              status_update_note: values?.status_update_note
            })
            if (error) {
              message.error(error.details)
            } else {
              message.success(getWord('project-update-success'))
            }
            setUpdate(null)
            actionRef.current?.reload()
          }}
        />
      )}
    </PageContainer>
  )
}
export default TableList
