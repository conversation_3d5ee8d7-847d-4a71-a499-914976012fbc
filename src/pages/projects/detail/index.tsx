import type { ProjectReaction, ProjectStatus } from '@/services/brain'
import { IPagination } from '@/types/Common'
import { getWord } from '@/utils'
import type { TabsProps } from 'antd'
import { useAccess, useModel, useParams } from 'umi'
import Reaction from './Reaction'

import ExperimentListTab from '@/components/ReactionTabs/ExperimentListTab'
import { useFormStorage } from '@/hooks/useFormStorage'
import { apiExperimentReactionSearch, parseResponseResult } from '@/services'
import {
  MoleculeStatusUpdateParams,
  Priority,
  Project,
  ProjectCompound,
  ProjectCompoundStatus,
  query,
  service
} from '@/services/brain'
import { isValidArray, toInt } from '@/utils'
import { PageContainer } from '@ant-design/pro-components'
import { useUpdateEffect } from 'ahooks'
import { App, Tabs } from 'antd'
import { cloneDeep, isEmpty } from 'lodash'
import { useEffect, useState } from 'react'

import Molecule from './Molecule'
import ProjectInfo from './ProjectInfo'
import type {
  FilterForm,
  ProjectRoutes,
  ReactionFilterForm,
  Update
} from './index.d'
// const { projectInfo }: any = require('@/mock/project-detail')?.data // []

type TabKey = 'molecule' | 'reaction' | 'experiment'
export default function ProjectDetail() {
  // const actionRef = useRef<ActionType>()

  const { projectInfo, getProjectInfo, userList } = useModel('project')

  const { queryMoleculeList, queryTargetMoleculeList, targetMoleculeList } =
    useModel('molecule')
  const { queryRoutesOption } = useModel('routes')
  const { id: projectId } = useParams<{ id: string }>()
  const { message } = App.useApp()
  const [moleculeTotal, setMoleculeTotal] = useState<number>(0)
  const [projectDetail, setProjectDetail] = useState<ProjectCompound[]>([])
  const [update, setUpdate] = useState<Update | null>(null)
  const [isMoleculeListLoading, setIsMoleculeListLoading] =
    useState<boolean>(false)
  const [moleculePagenate, setMoleculePagenate] = useState<IPagination>({
    page: 1,
    pageSize: 12
  })

  const getDetail = async (filters?: FilterForm) => {
    setIsMoleculeListLoading(true)
    let routeRequest = await query<ProjectCompound>('project-compounds')
      .filterDeep('project.id', 'eq', projectId as string)
      .populateWith('project_routes', ['id'])
      .populateDeep([
        {
          path: 'retro_processes',
          fields: ['id'],
          children: [{ key: 'retro_backbones', fields: ['id'] }]
        }
      ])
      .populateWith('compound', ['smiles'])
      .notEqualTo('type', 'temp_block')
      .paginate(moleculePagenate.page, moleculePagenate.pageSize)
    if (filters?.sort) {
      routeRequest = routeRequest.sortBy([
        { field: filters?.sort, order: filters?.order }
      ])
    }
    if (filters?.no) routeRequest = routeRequest.equalTo('no', filters?.no)
    if (filters?.status)
      routeRequest.filterDeep('status', 'in', filters?.status as string[])
    if (filters?.type)
      routeRequest = routeRequest.equalTo('type', filters?.type)
    if (filters?.director_id)
      routeRequest = routeRequest.equalTo('director_id', filters?.director_id)
    const { data: projectDetail, meta } = await routeRequest.get()
    projectDetail?.forEach((detail) => {
      detail.project_routes_number = detail.project_routes?.length
      detail.retro_backbones_number = detail.retro_processes?.flatMap(
        (p) => p.retro_backbones
      ).length
    })
    setProjectDetail(projectDetail as ProjectCompound[])
    setMoleculeTotal(meta?.pagination?.total || 0)
    setIsMoleculeListLoading(false)
  }

  const [tabStatus, setTabStatus] = useState<TabKey>('molecule')
  const [reactionTotal, setReactionTotal] = useState<number>(0)
  const [reactionList, setReactionList] = useState<ProjectReaction[]>([])
  const [isReactionLoading, setIsReactionLoading] = useState<boolean>(false)
  const [get, set] = useFormStorage()
  const [formValue, setFormValue] = useState<FilterForm>({
    status: ['created', 'designing', 'synthesizing', 'finished'],
    ...get(),
    sort: 'createdAt',
    order: 'desc'
  })
  const access = useAccess()
  const [projectRouteIds, setProjectRouteIds] = useState<number[]>([])
  const [pagenate, setPagenate] = useState<IPagination>({
    page: 1,
    pageSize: 10
  })

  const changeReactionList = (newList: ProjectReaction[]) => {
    setReactionList(newList as ProjectReaction[])
    setIsReactionLoading(false)
  }

  const getExperimentCount = async (_reactionList: ProjectReaction[]) => {
    let newReactionList = cloneDeep(_reactionList)
    const res = await apiExperimentReactionSearch({
      data: { ids: newReactionList.map((d) => d.id) }
    })
    if (parseResponseResult(res).ok) {
      if (!isValidArray(res?.data?.data)) return changeReactionList([])
      newReactionList.map((e) => {
        let targetItem = res?.data?.data.find(
          (item) => item?.project_reaction_id === e?.id
        )
        if (targetItem) {
          e.progress = targetItem?.progress
          e.experiment_count = targetItem?.experiment_count
        }
        return null
      })
    }
    changeReactionList(newReactionList as ProjectReaction[])
  }

  const getReactionList = async () => {
    setReactionList([])
    setIsReactionLoading(true)
    const request = service<ProjectReaction>('project-reactions')
      .select(['id', 'reaction', 'effective_procedures'])
      .filterDeep('project.id', 'eq', projectId as string)
      .populateDeep([
        {
          path: 'project_routes',
          fields: ['id', 'name'],
          children: [{ key: 'project_compound', fields: ['id'] }]
        }
      ]) // /projects/:id/compound/:compoundId/view/:routeId
    const {
      data: reactions,
      meta,
      error
    } = !isEmpty(projectRouteIds)
      ? await request
          .filterDeep('project_routes.id', 'in', projectRouteIds)
          .paginate(pagenate?.page, pagenate?.pageSize)
          .get()
      : await request.paginate(pagenate?.page, pagenate?.pageSize).get()
    if (error?.message) {
      message.error(error?.message)
      setIsReactionLoading(false)
    } else {
      setReactionTotal(meta?.pagination?.total || 0)
      getExperimentCount(reactions as ProjectReaction[])
    }
  }

  useEffect(() => {
    queryRoutesOption(projectId as string)
    queryMoleculeList(projectId as string)
    queryTargetMoleculeList(projectId as string)
    getProjectInfo(projectId as string)
  }, [])

  useUpdateEffect(() => {
    getReactionList()
  }, [projectRouteIds, pagenate])

  const handleUpdate = async (
    id?: number,
    params?: MoleculeStatusUpdateParams
  ) => {
    if (!id || !params) {
      setUpdate(null)
      return
    }
    const { error } = await service<MoleculeStatusUpdateParams>(
      'project-compounds'
    ).update(id, {
      priority: params?.priority,
      status: params?.status,
      status_update_note: params?.status_update_note
    })
    if (error) {
      message.error(error?.message)
    } else {
      message.success(getWord('success-update'))
      getDetail(formValue)
    }
    setUpdate(null)
  }

  const priorityChange = (value: Priority, id: number) =>
    handleUpdate(id, { priority: value })

  const statusChange = async (status: ProjectCompoundStatus, id: number) => {
    setUpdate({ id, status })
    /* canceled &&finished status need double check  */
    if (!['finished', 'canceled'].includes(status)) handleUpdate(id, { status })
  }

  const canAddMolecule: boolean = ['created', 'started'].includes(
    projectInfo?.status as ProjectStatus
  )

  const items: Omit<TabsProps['items'], 'authCode'> = [
    {
      key: 'molecule',
      label: getWord('molecules'),
      authCode: 'projects.tab.molecule',
      children: (
        <Molecule
          isLoading={isMoleculeListLoading}
          moleculeTotal={moleculeTotal}
          getDetail={getDetail}
          moleculePagenate={moleculePagenate}
          canAddMolecule={canAddMolecule}
          userList={userList}
          paginationChange={(page, pageSize) =>
            setMoleculePagenate({ page, pageSize })
          }
          projectDetail={projectDetail}
          update={update}
          statusChange={statusChange}
          handleUpdate={handleUpdate}
          projectInfo={projectInfo as Project}
          priorityChange={priorityChange}
          formValue={formValue}
          changeValues={(selectValues: FilterForm) => {
            let newValues = {
              ...selectValues,
              order: selectValues?.order
                ? selectValues?.order
                : formValue?.order
            } as FilterForm
            setFormValue(newValues)
            set(newValues)
            getDetail(newValues)
          }}
        />
      )
    },
    {
      key: 'reaction',
      label: getWord('reaction-tab'),
      authCode: 'projects.tab.reaction',
      children: (
        <Reaction
          isLoading={isReactionLoading}
          reactionList={reactionList}
          reactionTotal={reactionTotal}
          pagenate={pagenate}
          paginationChange={(page, pageSize) => setPagenate({ page, pageSize })}
          restData={() => {
            setProjectRouteIds([])
            getReactionList()
          }}
          changeValues={(selectValues: ReactionFilterForm) => {
            /* NOTE queryRoutesOption(projectId as string, [-1]) 传递[]则是获取全量数据，[-1]则为无数据 */
            if (selectValues?.routes) {
              setProjectRouteIds(selectValues?.routes)
            } else {
              queryRoutesOption(projectId as string)
              let _projectRouteIds: number[] = []
              for (const iterator of targetMoleculeList) {
                if (selectValues?.compounds.includes(iterator?.value)) {
                  if (isEmpty(iterator?.project_routes)) {
                    _projectRouteIds.push(-1)
                  } else {
                    function getID(ids: ProjectRoutes[]) {
                      if (isEmpty(ids)) return setProjectRouteIds([])
                      ids.forEach((e) => _projectRouteIds.push(e?.id))
                    }
                    getID(iterator?.project_routes)
                  }
                }
              }
              setProjectRouteIds(_projectRouteIds)
              queryRoutesOption(projectId as string, _projectRouteIds)
            }
          }}
        />
      )
    },
    {
      key: 'experiment',
      authCode: 'projects.tab.experiment',
      label: getWord('pages.experiment'),
      children: <ExperimentListTab projectId={toInt(projectId)} />
    }
  ]

  const getTab = () => {
    if (isEmpty(items)) return []
    let newTabs = items.filter((e) =>
      access?.authCodeList?.includes(e.authCode)
    ) as TabsProps['items']
    return newTabs
  }

  useEffect(() => {
    if (tabStatus === 'reaction') {
      getReactionList()
    } else if (tabStatus === 'molecule') {
      getDetail(formValue)
    }
  }, [tabStatus, moleculePagenate])

  const onChange = (key: string) => setTabStatus(key)

  return (
    <PageContainer>
      <ProjectInfo
        projectDetail={projectDetail}
        onUpdate={() => getDetail(formValue)}
      />
      <Tabs defaultActiveKey="molecule" items={getTab()} onChange={onChange} />
    </PageContainer>
  )
}
