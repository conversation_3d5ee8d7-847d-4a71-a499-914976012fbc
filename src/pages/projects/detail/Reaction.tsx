import LoadingTip from '@/components/LoadingTip'
import ReactionCard from '@/components/ReactionCard'
import StatusTip from '@/components/StatusTip'
import { useFormStorage } from '@/hooks/useFormStorage'
import { getWord } from '@/utils'
import type { ProFormInstance } from '@ant-design/pro-components'
import {
  ProForm,
  ProFormGroup,
  ProFormSelect
} from '@ant-design/pro-components'
import { Col, Pagination, Row } from 'antd'
import cs from 'classnames'
import { isArray, isEmpty, isNil } from 'lodash'
import { useRef } from 'react'
import { useModel } from 'umi'
import type { ReactionFilterForm, ReactionProps } from './index.d'
import styles from './index.less'
export default function Reaction(props: ReactionProps) {
  const {
    reactionList,
    reactionTotal,
    changeValues,
    pagenate,
    paginationChange,
    restData,
    isLoading
  } = props
  const formRef = useRef<ProFormInstance>()
  const { targetMoleculeList } = useModel('molecule')
  const { routesOption } = useModel('routes')
  const hasReactionData: boolean =
    !isEmpty(reactionList) && isArray(reactionList)
  const [get, set] = useFormStorage({ suffix: 'reaction' })
  formRef.current?.setFieldsValue(get())

  return (
    <div className={styles.reaction}>
      <ProForm<ReactionFilterForm>
        style={{ height: '32px' }}
        layout="horizontal"
        formRef={formRef}
        submitter={false}
        onValuesChange={async () => {
          const filters = await formRef?.current?.getFieldsValue()
          set(filters)
          changeValues(filters)
        }}
      >
        <ProFormGroup>
          <ProFormSelect
            name="compounds"
            label={getWord('target-molecules')}
            mode="multiple"
            showSearch
            onChange={(values) => {
              if (isNil(values)) restData()
              formRef?.current?.setFieldValue('routes', undefined)
            }}
            options={targetMoleculeList}
          />
          <ProFormSelect
            name="routes"
            mode="multiple"
            label={getWord('Routes')}
            showSearch
            options={routesOption}
          />
        </ProFormGroup>
      </ProForm>
      <div
        className={cs(styles.cardContent, 'noPaddingCard', {
          'flex-center': isLoading
        })}
      >
        {isLoading ? (
          <div className="loadingPage">
            <LoadingTip />
          </div>
        ) : (
          <>
            <Row>
              {hasReactionData ? (
                reactionList?.map((item) => (
                  <Col sm={24} md={12} key={item?.id}>
                    <ReactionCard reaction={item} enableToReaction />
                  </Col>
                ))
              ) : (
                <StatusTip
                  des={getWord('noticeIcon.empty')}
                  wrapperClassName="fullLayoutContent"
                />
              )}
            </Row>
            {hasReactionData && (
              <Pagination
                className={cs(styles.pagination, 'flex-justfy-content-end')}
                total={reactionTotal}
                current={pagenate?.page}
                pageSize={pagenate?.pageSize}
                showSizeChanger={false}
                onChange={paginationChange}
              />
            )}
          </>
        )}
      </div>
    </div>
  )
}
