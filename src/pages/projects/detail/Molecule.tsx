import LoadingTip from '@/components/LoadingTip'
import PageHeader from '@/components/PageHeader'
import SortFilter from '@/components/SortFilter'
import ExperimentStatus from '@/components/StatusTip'
import useOptions from '@/hooks/useOptions'
import type { ProjectStatus } from '@/services/brain'
import { ProjectCompoundStatus } from '@/services/brain'
import { getWord, isReadonlyMolecule } from '@/utils'
import { FileAddOutlined } from '@ant-design/icons'
import type { ProFormInstance } from '@ant-design/pro-components'
import {
  ProForm,
  ProFormCheckbox,
  ProFormGroup,
  ProFormSelect
} from '@ant-design/pro-components'
import { Button, Pagination } from 'antd'
import cs from 'classnames'
import { isArray, isEmpty } from 'lodash'
import { useRef } from 'react'
import { history, useAccess, useModel, useParams } from 'umi'
import MoleculeCard from '../components/MoleculeCard'
import StatusUpdateModel from '../components/StatusUpdateModel'
import type { StatusConfirmValue } from '../components/StatusUpdateModel/index.d'
import type { FilterForm, MoleculeProps } from './index.d'
import styles from './index.less'
export default function Molecule(props: MoleculeProps) {
  const { sortStandard, typeMapForSelect } = useOptions()
  const {
    projectDetail,
    isLoading,
    moleculeTotal,
    moleculePagenate,
    statusChange,
    projectInfo,
    priorityChange,
    formValue,
    changeValues,
    paginationChange,
    canAddMolecule,
    handleUpdate,
    update,
    userList
  } = props
  const access = useAccess()
  const { id: projectId } = useParams<{ id: string }>()
  const { moleculeStatusOptions } = useOptions()
  const addMolecule = () => history.push(`/projects/${projectId}/add-molecule`)
  const formRef = useRef<ProFormInstance>()
  const { moleculeList } = useModel('molecule')
  const hasProjectDetail: boolean =
    !isEmpty(projectDetail) && isArray(projectDetail)
  return (
    <div className={styles?.detail}>
      <PageHeader
        minRightWidth={145}
        leftSlot={
          <ProForm<FilterForm>
            layout="horizontal"
            formRef={formRef}
            submitter={false}
            onValuesChange={async () => {
              const filters = formRef?.current?.getFieldsValue()
              changeValues(filters)
            }}
            initialValues={formValue}
          >
            <ProFormGroup>
              <ProFormSelect
                name="no"
                label={getWord('molecules-no')}
                showSearch
                options={moleculeList}
              />
              <ProFormCheckbox.Group
                name="status"
                label={getWord('molecules-status')}
                options={moleculeStatusOptions}
              />
              <ProFormSelect
                name="director_id"
                label={getWord('pages.experiment.label.owner')}
                showSearch
                options={userList}
              />
              <ProFormSelect
                name="type"
                width={150}
                label={getWord('molecules-type')}
                valueEnum={typeMapForSelect}
                placeholder={getWord('select-tip')}
              />
              <SortFilter
                valueEnum={sortStandard}
                handleSort={async (curOrder) => {
                  const filters = await formRef?.current?.getFieldsValue()
                  changeValues({ ...filters, order: curOrder })
                }}
                isAsc={formValue.order === 'asc'}
                isForm={true}
              />
            </ProFormGroup>
          </ProForm>
        }
        rightSlot={
          canAddMolecule &&
          access?.authCodeList?.includes('projects.button.add-molecule') && (
            <Button
              type="primary"
              shape="round"
              icon={<FileAddOutlined />}
              onClick={addMolecule}
            >
              {getWord('menu.list.project-list.detail.addMolecule')}
            </Button>
          )
        }
      />
      {update && isReadonlyMolecule(update?.status as ProjectStatus) && (
        <StatusUpdateModel
          operateTargetName={getWord('molecules')}
          status={update.status}
          trigger={{ open: !!update }}
          onCancel={() => handleUpdate?.()}
          onFinished={async (
            values: StatusConfirmValue<ProjectCompoundStatus>
          ) =>
            handleUpdate(update.id, {
              status: values?.status,
              status_update_note: values?.status_update_note as string
            })
          }
        />
      )}
      {hasProjectDetail || isLoading ? (
        isLoading ? (
          <div className="loadingPage" style={{ minHeight: '400px' }}>
            <LoadingTip />
          </div>
        ) : (
          <MoleculeCard
            detailData={projectDetail}
            statusChange={statusChange}
            disabled={
              isReadonlyMolecule(
                projectInfo?.status as ProjectStatus
              ) as boolean
            }
            priorityChange={priorityChange}
          />
        )
      ) : (
        <ExperimentStatus
          des={getWord('add-molecule-tip')}
          clickEvent={addMolecule}
          wrapperClassName="fullLayoutContent"
        />
      )}
      {hasProjectDetail && (
        <Pagination
          className={cs(styles.pagination, 'flex-justfy-content-end')}
          total={moleculeTotal}
          current={moleculePagenate?.page}
          pageSize={moleculePagenate?.pageSize}
          showSizeChanger={false}
          onChange={paginationChange}
        />
      )}
    </div>
  )
}
