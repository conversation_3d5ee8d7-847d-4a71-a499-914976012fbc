@import '@/style/variables.less';
.detail {
  width: 100%;
  min-width: calc(@minContainerWidth - 20px) !important;
  height: 100%;
  min-height: calc(
    @main-content-height_noHeader - 146px - @crumbs-height - 24px - 46px - 16px
  );
  padding: 10px;
  overflow-x: hidden;
  background-color: #fff;
  :global {
    .page-header-root {
      align-items: unset !important;
      width: 100%;
      min-width: calc(@minContainerWidth - 20px) !important;
      height: auto;
      max-height: 92px;
      overflow-x: hidden;
      overflow-y: hidden;
    }
  }
}

.projectInfo {
  .operate {
    h1 {
      font-size: 20px;
    }
    .offersButton {
      display: flex;
      align-items: center;
    }
    .offersButton:hover {
      :global {
        .ant-btn-icon {
          fill: @color-design-e !important;
        }
      }
    }
  }
  .infoContent {
    width: 100%;
    height: 100px;
    margin: 15px 0;
    background-color: #fff;
    border: 1px solid @color-text-h;
    border-radius: 5px;
    .infoItem {
      padding-left: 23px;
      line-height: 16px;
    }
    .label {
      width: max-content;
      height: 16px;
      color: @color-text-d;
      font-size: 14px;
    }
    .value {
      margin-left: 16px;
      font-size: 16px;
    }
  }
}

.cardContent {
  height: 100%;
  min-height: 450px;
  padding-bottom: 12px;
}

.pagination {
  width: fit-content;
  margin-top: 10px;
  margin-left: auto;
  padding-right: 15px;
  padding-bottom: 2px;
}

.reaction {
  height: auto;
  min-height: 500px;
  padding-top: 10px;
  overflow: hidden;
  background: #fff;
  :global {
    .ant-pro-form-group {
      padding-left: 16px;
    }
  }
}
