import type {
  Project,
  ProjectCompound,
  ProjectReaction
} from '@/services/brain'
import {
  MoleculeStatusUpdateParams,
  ProjectCompoundStatus
} from '@/services/brain'
import { IPagination } from '@/types/Common'
import type { LabeledValue } from 'antd/es/select'
export interface ProjectInfoProps {
  projectDetail: ProjectCompound[]
  onUpdate?: () => void
}

export interface FilterForm {
  no?: string
  status?: string[]
  sort?: 'no' | 'updatedAt' | 'createdAt'
  /**
   * desc 倒叙
   * asc 正序
   */
  order?: 'desc' | 'asc'
  director_id?: string
  type?: 'target' | 'building_block'
}

export interface Update {
  id: number
  status: ProjectCompoundStatus
}
export interface MoleculeProps {
  isLoading: boolean
  getDetail: (filters?: FilterForm) => void
  userList: LabeledValue[]
  canAddMolecule: boolean
  update: Update | null
  projectDetail: ProjectCompound[]
  projectInfo: Project
  statusChange: (status: ProjectCompoundStatus, id: number) => void
  priorityChange: (value: Priority, id: number) => void
  handleUpdate: (id?: number, params?: MoleculeStatusUpdateParams) => void
  changeValues: (selectValues: FilterForm) => void
  paginationChange: (page: number, pageSize: number) => void
  moleculeTotal: number
  moleculePagenate: IPagination
  formValue: FilterForm
}

export interface ReactionProps {
  changeValues: (selectValues: ReactionFilterForm) => void
  reactionList: ProjectReaction[]
  reactionTotal: number
  pagenate: IPagination
  restData: () => void
  paginationChange: (page: number, pageSize: number) => void
  isLoading: boolean
}

export interface ReactionFilterForm {
  /**
   * 目标分子
   */
  compounds: string

  /**
   * 路线
   */
  routes: string
}

export type ProjectRoutes = Omit<Update, 'status'>
