import { ReactComponent as MoneyIcon } from '@/assets/svgs/money.svg'
import WordParser from '@/components/WordParser'
import type { ProjectType, Status } from '@/services/brain'
import { Project, service } from '@/services/brain'
import { formatYMDTime, getWord, isEN, isPartenerMode } from '@/utils'
import { SettingFilled } from '@ant-design/icons'
import { Button, Col, Row, Space, message } from 'antd'
import cs from 'classnames'
import { useState } from 'react'
import { history, useAccess, useModel, useParams } from 'umi'
import ConfigModel from '../components/ConfigModel'
import type { ProjectInfoProps } from './index.d'
import styles from './index.less'
// const { projectInfo }: any = require('@/mock/project-detail')?.data // []
/* TODO API integration and testing */
export default function ProjectInfo(props: ProjectInfoProps) {
  const { projectInfo, getProjectInfo } = useModel('project')

  const { id: projectId } = useParams<{ id: string }>()
  const makeOffers = () =>
    history.push(`/projects/${projectId}/quotation-records`)
  const LabelValue = ({ label, value }: { label: string; value: any }) => (
    <Col span={6} className={cs('flex-align-items-center', styles.infoItem)}>
      <span
        className={styles.label}
        style={{
          minWidth: isEN() ? '80px' : '60px'
        }}
      >
        {label}
      </span>
      <span className={styles.value}>{value}</span>
    </Col>
  )
  const access = useAccess()
  const [projectConfigData, setProjectConfigData] = useState<Project>()
  const [configVisible, setConfigVetvisible] = useState<boolean>(false)
  const getProjectConfigData = async () => {
    const { data, error } = await service<Project>('projects')
      .selectManyByID([projectId as string])
      .populateWith('project_members', ['user_id', 'role'], true)
      .get()
    if (!error) {
      setProjectConfigData(data[0])
      setConfigVetvisible(true)
    } else {
      message.error(error?.message)
    }
  }
  return (
    <div className={styles.projectInfo}>
      <div
        className={cs(
          'commonContainer',
          'flex-justify-space-between',
          styles.operate
        )}
      >
        <h1>{getWord('project-details')}</h1>
        <Space>
          {access?.authCodeList?.includes('project.button.settings') ? (
            <Button icon={<SettingFilled />} onClick={getProjectConfigData}>
              {getWord('pages.project.settings')}
            </Button>
          ) : (
            ''
          )}
          {access?.authCodeList?.includes('project.button.quote') &&
          props?.projectDetail?.length ? (
            <Button
              onClick={makeOffers}
              icon={<MoneyIcon width={18} />}
              className={styles.offersButton}
            >
              {getWord('menu.list.project-list.quotation')}
            </Button>
          ) : (
            ''
          )}
        </Space>
      </div>
      <ConfigModel
        key="config"
        projectId={projectConfigData?.id || 0}
        refreshEvent={() =>
          getProjectInfo(String(projectConfigData?.id) as string)
        }
        modelProps={{
          open: configVisible,
          onCancel: () => {
            setConfigVetvisible(false)
            props?.onUpdate?.()
          }
        }}
      />
      <Row className={cs(styles.infoContent, 'commonContainer')}>
        <LabelValue label={getWord('project-ID')} value={projectInfo?.no} />
        <LabelValue label={getWord('project-name')} value={projectInfo?.name} />
        {isPartenerMode() ? null : (
          <LabelValue
            label={getWord('pages.projectTable.label.customer')}
            value={projectInfo?.customer}
          />
        )}
        <LabelValue
          label={getWord('project-type')}
          value={<WordParser word={projectInfo?.type as ProjectType} />}
        />
        <LabelValue
          label={getWord('project-status')}
          value={<WordParser word={projectInfo?.status as Status} />}
        />
        <LabelValue
          label={getWord('pages.projectTable.label.pm')}
          value={
            projectInfo?.PM ? projectInfo?.PM.split('@')[0] : projectInfo?.PM
          }
        />
        <LabelValue
          label={getWord('project-delivery-date')}
          value={formatYMDTime(projectInfo?.delivery_date)}
        />
      </Row>
    </div>
  )
}
