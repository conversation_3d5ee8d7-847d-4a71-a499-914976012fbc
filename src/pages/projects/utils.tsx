import {
  Project,
  ProjectMember,
  ProjectStatus,
  ProjectStatusAudit
} from '@/services/brain'
import { getWord, isPartenerMode } from '@/utils'
import { omit } from 'lodash'

interface ProjectCustomFields {
  managers?: ProjectMember[]
  last_audit?: ProjectStatusAudit
}

export type ProjectListItem = Project & ProjectCustomFields

export const productManagerRoleCode = 'pm' as const

export const parseProjectResponse = (data: Project): ProjectListItem => ({
  ...data,
  managers: data.project_members?.filter(
    (m) => m.role?.code === productManagerRoleCode
  ),
  last_audit: data.project_status_audits?.sort((a, b) =>
    (a.createdAt || 0) > (b.createdAt || 0) ? -1 : 1
  )?.[0]
})

export const statusTransformMap: Record<ProjectStatus, ProjectStatus[]> = {
  created: ['cancelled', 'started'],
  cancelled: [],
  finished: [],
  holding: ['cancelled', 'started'],
  started: ['cancelled', 'finished', 'holding']
}

const defaultProjectTypeEnums = {
  fte: {
    text: getWord('pages.projectTable.typeValue.fte')
  },
  ffs: {
    text: getWord('pages.projectTable.typeValue.ffs')
  },
  personal: {
    text: getWord('pages.projectTable.typeValue.personal')
  }
}

export const projectTypeEnums = isPartenerMode()
  ? omit(defaultProjectTypeEnums, ['fte', 'ffs'])
  : defaultProjectTypeEnums
