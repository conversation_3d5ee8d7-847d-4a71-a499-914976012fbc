import CustomTable from '@/components/CustomTable'
import ModalBase from '@/components/ModalBase'
import { useModalBase } from '@/components/ModalBase/useModalBase'
import { getWord, isEN } from '@/utils'
import type { DiffCost } from './index.d'
import styles from './index.less'
interface ConfirmModalProps {
  diffCostData: DiffCost[]
  // dialogProps: ModalBaseProps
  // confirm: () => Promise<string>
  confirmDiff: () => void
  openEvent?: { open?: boolean }
}
export default function ConfirmModal(props: ConfirmModalProps) {
  const { openEvent } = props
  const { dialogProps, confirm } = useModalBase()
  const { diffCostData, confirmDiff } = props
  const columns = [
    {
      title: getWord('steps'),
      dataIndex: 'step_no'
    },
    {
      title: isEN()
        ? `${getWord('english-name')}（${getWord('yuan')}）`
        : `${getWord('chinese-name')}（${getWord('yuan')}）`,
      dataIndex: isEN() ? 'name_en' : 'name_zh',
      ellipsis: true
    },
    {
      title: 'CAS',
      dataIndex: 'cas_no'
    },
    {
      title: getWord('min-cost'),
      dataIndex: 'minCost'
    },
    {
      title: getWord('cost'),
      dataIndex: 'cost'
    }
  ]
  const tableConfig = {
    dataSource: diffCostData,
    bordered: true,
    pagination: null
  }

  const repeatedConfirm = async () => {
    confirmDiff()
    confirm()
  }

  return (
    <ModalBase
      {...dialogProps}
      title={getWord('confirm-quote')}
      className={styles.confirmModal}
      width={800}
      openEvent={openEvent}
      onConfirm={repeatedConfirm}
    >
      <div className={styles.title}>
        {getWord('cost-conflict-quote-double-check')}
      </div>
      <CustomTable {...tableConfig} columns={columns} rowKey="cas_no" />
    </ModalBase>
  )
}
