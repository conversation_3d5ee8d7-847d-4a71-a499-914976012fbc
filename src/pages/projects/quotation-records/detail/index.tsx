import ButtonWithLoading from '@/components/ButtonWithLoading'
import ModalBase from '@/components/ModalBase'
import { useModalBase } from '@/components/ModalBase/useModalBase'
import SectionTitle from '@/components/SectionTitle'
import SyntheticRouteCard from '@/components/SyntheticRoutes/SyntheticRouteCard'
import { AmendOtherQuote, OtherQuote, OtherQuoteCB } from '@/models/quotation'
import { apiDelByQuoteNo, parseResponseResult } from '@/services'
import type { IQuote } from '@/services/brain'
import { Cost_detail, query } from '@/services/brain'
import { getWord, isEN, isValidArray } from '@/utils'
import {
  PageContainer,
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormText
} from '@ant-design/pro-components'
import { Button, Form, Select, Space, Typography, message } from 'antd'
import cs from 'classnames'
import { isArray, isEmpty, isNil } from 'lodash'
import { ReactNode, useEffect, useRef, useState } from 'react'
import { history, useAccess, useModel, useParams, useSearchParams } from 'umi'
import CustomAnchor from '../../../../components/Anchor'
import OtherQuoteModal from '../../components/OtherQuoteModal'
import ConfirmModal from './ConfirmQuoteModal'
import MaterialList from './MaterialList'
import MyReaction from './MyReaction'
import Schedule from './Schedule'
import StepButton from './StepButton'
import Summary from './Summary'
import type { ConrifrmRoutes, DiffCost, FilterForm } from './index.d'
import styles from './index.less'
import { findDiffCosts } from './utils'
type QupteType = 'create' | 'view'
export default function QuoteDetail() {
  const { initialState } = useModel('@@initialState')
  const [searchParams] = useSearchParams()
  const {
    quotesDetail,
    updateQuotesDetail,
    createQuote,
    updateQuotes,
    updatetDiffCosts,
    diffCosts
  } = useModel('quotation')
  const { getUserConfigs } = useModel('login')
  const [weighUnit, setWeighUnit] = useState<'mg' | 'g'>('g')
  const [fetchQuoteLoading, setFetchQuoteLoading] = useState<boolean>(false)
  let quoteType: QupteType = searchParams.get('type') as QupteType
  let compound_no: string = searchParams.get('compound_no') as string

  const isCreateType: boolean = quoteType === 'create'
  const { id: projectId, quoteMoleculeId } = useParams<{
    id: string
    quoteMoleculeId: string
  }>()
  const access = useAccess()

  const [curQuoteMoleculeId, setCurQuoteMoleculeId] = useState(quoteMoleculeId)
  const [routesNum, setRoutesNum] = useState<number>()
  const [conrifrmRoutes, setConrifrmRoutes] = useState<IQuote[] | null>([])
  const [isEditorType, setIsEditorType] = useState(false)
  const disabledEditInConfirmedStatus: boolean =
    !isEditorType && quotesDetail?.status === 'confirmed'
  const [form] = Form.useForm<OtherQuote>()

  const getQuotationRecords = async () => {
    const { data, meta } = await query(
      `quote/routes?project_id=${projectId}&compound_id=${curQuoteMoleculeId}`
    ).get()
    setConrifrmRoutes(data)
    setRoutesNum(meta?.total)
  }

  const getDiffCosts = (data: IQuote) => {
    if (!data) return
    let _diffCosts: DiffCost[] = findDiffCosts(data?.cost_detail)
    updatetDiffCosts(_diffCosts)
  }

  useEffect(() => {
    if (!isNil(quotesDetail)) {
      getDiffCosts(quotesDetail)
      setConrifrmRoutes([quotesDetail])
    }
  }, [quotesDetail])

  const toEditQuote = (_curQuoteMoleculeId: string) => {
    history.replace(
      `/projects/${projectId}/quotation-records/${_curQuoteMoleculeId}/quote-info?type=editor&compound_no=${compound_no}`
    )
  }

  const getQuoteDetail = async () => {
    const { data } = await query(`quotes/${curQuoteMoleculeId}`).get()
    setConrifrmRoutes([data])
    setRoutesNum(data?.quote_route?.length)
    getDiffCosts(data)
    updateQuotesDetail(data)
  }

  const formRef = useRef<ProFormInstance>()
  useEffect(() => {
    if (formRef?.current) {
      formRef?.current?.setFieldsValue({
        target_weight: quotesDetail?.target_weight,
        purity: quotesDetail?.purity,
        FTE_unit_price: quotesDetail?.FTE_unit_price,
        ratio: quotesDetail?.ratio
      })
      if (quotesDetail?.target_unit) setWeighUnit(quotesDetail?.target_unit)
    }
  }, [quotesDetail])

  useEffect(() => {
    if (formRef?.current && quotesDetail?.project_compound_id) {
      formRef?.current?.setFieldsValue({
        project_compound_id: quotesDetail?.project_compound_id
      })
    }
  }, [quotesDetail?.project_compound_id])

  const [curRouteId, setCurRouteId] = useState<number>()
  const [createRouteIndex, setCreateRouteIndex] = useState<number>(0)
  useEffect(() => {
    if (formRef?.current && compound_no) {
      formRef?.current?.setFieldsValue({
        compound_no: compound_no
      })
    }
  }, [compound_no])

  const [openEvent, setOpenEvent] = useState<{ open: boolean }>()
  const isConfirmedQuote = quotesDetail?.status === 'confirmed'
  let canSaveDraft =
    (['draft', 'editing'].includes(quotesDetail?.status) ||
      !disabledEditInConfirmedStatus) &&
    access?.authCodeList?.includes('quotation-records.button.saveDraft')
  let canConfirm =
    (['draft', 'editing'].includes(quotesDetail?.status) ||
      !disabledEditInConfirmedStatus) &&
    access?.authCodeList?.includes('quotation-records.button.confirmQuote')
  const handelQuoteStatus = async (quoteStatus: 'confirmed' | 'editing') => {
    let formValues = await formRef?.current?.validateFields()
    const { error } = await updateQuotes(
      curQuoteMoleculeId as string,
      {
        ...formValues,
        status: quoteStatus,
        target_unit: weighUnit,
        project_id: projectId
      },
      quoteStatus === 'confirmed'
        ? getWord('success-confirm-quotate')
        : getWord('success-save-draft')
    )
    if (!error && quoteStatus === 'confirmed') setIsEditorType(false)
  }

  const fetchData = async () => {
    if (fetchQuoteLoading) return
    setFetchQuoteLoading(true)
    if (!isCreateType) {
      await getQuoteDetail()
    } else {
      await getQuotationRecords()
    }
    setFetchQuoteLoading(false)
  }

  const getDefaultConfigs = async () => {
    const data = await getUserConfigs()
    let quoteDefaultSettings = data?.find(
      (e) => e.setting_label === 'quotation'
    )
    if (formRef?.current && quoteDefaultSettings) {
      formRef?.current?.setFieldsValue({
        FTE_unit_price:
          quotesDetail?.FTE_unit_price ||
          quoteDefaultSettings?.setting_value?.FTE_rate,
        ratio: quotesDetail?.ratio || quoteDefaultSettings?.setting_value?.ratio
      })
    }
  }

  /* FIXME render twice */
  useEffect(() => {
    if (!quoteType) return
    fetchData()
    getDefaultConfigs()
  }, [isCreateType])

  useEffect(() => {
    return () => {
      updateQuotesDetail(null)
    }
  }, [])

  const selectAfter = (
    <Select
      className={styles.unitSelect}
      value={weighUnit}
      style={{ width: '60px' }}
      onChange={(e) => setWeighUnit(e)}
      disabled={!isCreateType}
    >
      <Select.Option value="g">g</Select.Option>
      <Select.Option value="mg">mg</Select.Option>
    </Select>
  )

  const handleStepChange = async (step: 'prev' | 'next') => {
    let nextQuoteIndex: number =
      step === 'prev'
        ? quotesDetail?.route_index - 1
        : quotesDetail?.route_index + 1
    let requestQuoteId: string =
      quotesDetail?.quote_route[nextQuoteIndex]?.quote_id
    if (!requestQuoteId) return
    const { data, error } = await query<any>(`quotes/${requestQuoteId}`).get()
    if (error?.message) return message.error(error?.message)
    else if (data) {
      updateQuotesDetail(data)
      setCurQuoteMoleculeId(requestQuoteId)
      window.history.pushState(
        null,
        '',
        `${window.location.origin}/projects/${projectId}/quotation-records/${requestQuoteId}/quote-info?type=editor&compound_no=${compound_no}`
      )
    }
  }

  const handleCreateStepChange = async (step: 'prev' | 'next') => {
    let nextQuoteIndex: number =
      step === 'prev' ? createRouteIndex - 1 : createRouteIndex + 1
    setCreateRouteIndex(nextQuoteIndex)
  }

  const commonAnchor = [
    {
      key: 'quotationParameter',
      href: '#quotationParameter',
      title: getWord('quote-parmas')
    },
    {
      key: 'confirmedRoute',
      href: '#confirmedRoute',
      title: getWord('confirmed-route')
    }
  ]

  const RouteItem = ({
    stepChange,
    route,
    routeIndex
  }: {
    stepChange: (step: 'prev' | 'next') => void
    route: any
    routeIndex: number
  }) => {
    if (!route) return ''
    const routeId = route?.project_route_id || route?.id
    setCurRouteId(routeId)
    return (
      <>
        <div className="flex-justify-space-between">
          <div>
            {getWord('route-id')}：
            {routeId && (
              <Typography.Link
                href={`/projects/${projectId}/compound/${route?.project_compound_id}/view/${routeId}`}
                target="_blank"
              >
                {routeId}
              </Typography.Link>
            )}
          </div>
          <StepButton
            disabled={disabledEditInConfirmedStatus}
            routesNum={routesNum as number}
            handleStepChange={stepChange}
            route_index={routeIndex}
          />
        </div>
        <SyntheticRouteCard key={routeId} route={route} hiddenTitle={true} />
      </>
    )
  }

  const RenderRoutes = (): ReactNode => {
    if (isEmpty(conrifrmRoutes) || !isArray(conrifrmRoutes)) return ''
    return !isCreateType ? (
      conrifrmRoutes?.map((route: ConrifrmRoutes, index: number) => {
        return (
          <>
            <RouteItem
              key={`routeItem-${index}`}
              route={route}
              stepChange={handleStepChange}
              routeIndex={quotesDetail?.route_index as number}
            />
          </>
        )
      })
    ) : (
      <RouteItem
        key="routeItem-item"
        route={conrifrmRoutes[createRouteIndex]}
        stepChange={handleCreateStepChange}
        routeIndex={createRouteIndex as number}
      />
    )
  }

  const [repeatedWeight, setRepeatedWeight] = useState<string>()
  const [quotationNo, setQuotationNo] = useState<string>()
  const { dialogProps, confirm } = useModalBase()

  const createQuoteCB = (values: OtherQuoteCB, isOtherQuote: boolean) => {
    const { _curQuoteMoleculeId, _repeatedWeight, _quotationNo } = values
    if (_repeatedWeight) {
      confirm()
      setRepeatedWeight(_repeatedWeight)
      setQuotationNo(_quotationNo)
    } else {
      setCurQuoteMoleculeId(_curQuoteMoleculeId as string)
      if (!isOtherQuote) {
        toEditQuote(_curQuoteMoleculeId as string)
      } else {
        setCurQuoteMoleculeId(_curQuoteMoleculeId as string)
        toEditQuote(_curQuoteMoleculeId as string)
      }
    }
  }

  const [createLoading, setCreateLoading] = useState<boolean>(false)

  const createNewQuote = () => {
    formRef?.current?.validateFields().then(async (formValues) => {
      setCreateLoading(true)
      await createQuote(
        {
          ...formValues,
          target_unit: weighUnit,
          project_id: projectId,
          route_id: curRouteId
        },
        (values: OtherQuoteCB) => createQuoteCB(values, false)
      )
      setCreateLoading(false)
    })
  }

  /* 删除老的报价 */
  const repeatedWeightConfirm = async () => {
    const res = await apiDelByQuoteNo({
      routeParams: `${quotationNo}/${projectId}`
    })
    if (parseResponseResult(res).ok) createNewQuote()
  }

  const route = conrifrmRoutes?.[createRouteIndex]

  return (
    <div className={styles.quoteInfo}>
      <ConfirmModal
        diffCostData={diffCosts}
        openEvent={openEvent}
        confirmDiff={async () => {
          updatetDiffCosts([])
          await handelQuoteStatus('confirmed')
        }}
      />
      <PageContainer
        className={cs(styles.quoteDetail, {
          [styles['unfoldWidth']]: !initialState?.isMenuCollapsed,
          [styles['foldWidth']]: initialState?.isMenuCollapsed,
          [styles['unfoldWidth_EN']]: !initialState?.isMenuCollapsed && isEN(),
          [styles['foldWidth_EN']]: initialState?.isMenuCollapsed && isEN()
        })}
      >
        <SectionTitle
          anchorId="quotationParameter"
          word={getWord('quote-parmas')}
        />
        <ProForm<FilterForm>
          grid
          layout="horizontal"
          submitter={false}
          formRef={formRef}
          colProps={{ xs: 12, sm: 12, md: 8, lg: 6 }}
          labelCol={{ span: isEN() ? 12 : 10 }}
          wrapperCol={{ span: isEN() ? 12 : 14 }}
          initialValues={{
            project_compound_id: curQuoteMoleculeId
          }}
        >
          <ProFormText
            name="compound_no"
            label={getWord('molecules-no')}
            labelCol={{ span: isEN() ? 10 : 10 }}
            wrapperCol={{ span: isEN() ? 14 : 14 }}
            disabled
          />
          <ProFormText
            name="project_compound_id"
            label={getWord('project-molecule-id')}
            labelCol={{ span: isEN() ? 14 : 10 }}
            wrapperCol={{ span: isEN() ? 10 : 14 }}
            disabled
          />
          <ProFormDigit
            name="target_weight"
            disabled={!isCreateType}
            label={getWord('weight')}
            extra={selectAfter}
            rules={[{ required: true }]}
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 12 }}
            min={0.1}
          />
          <ProFormDigit
            name="purity"
            disabled={!isCreateType}
            rules={[{ required: true }]}
            label={getWord('purity')}
            min={0.1}
            max={100}
            labelCol={{ span: isEN() ? 12 : 10 }}
            wrapperCol={{ span: 8 }}
            addonAfter={
              <div
                className={styles.unitSelect}
                style={{ right: '-24px', top: '5px' }}
              >
                %
              </div>
            }
          />
          <ProFormDigit
            name="FTE_unit_price"
            label={getWord('FTE-per-day')}
            labelCol={{ span: isEN() ? 10 : 10 }}
            wrapperCol={{ span: isEN() ? 10 : 10 }}
            rules={[{ required: true }]}
            disabled={disabledEditInConfirmedStatus}
            extra={
              <div
                className={styles.unitSelect}
                style={{ top: '5px', right: isEN() ? '-92px' : '-40px' }}
              >
                {getWord('FTE-per-day-unit')}
              </div>
            }
          />
          <ProFormDigit
            name="ratio"
            label={getWord('coefficient')}
            rules={[{ required: true }]}
            labelCol={{ span: isEN() ? 14 : 10 }}
            wrapperCol={{ span: isEN() ? 8 : 14 }}
            disabled={disabledEditInConfirmedStatus}
            extra={
              disabledEditInConfirmedStatus || isCreateType ? (
                ''
              ) : (
                <a
                  className={styles.unitSelect}
                  style={{ top: '5px', right: isEN() ? '-130px' : '-60px' }}
                  onClick={async () => {
                    await updateQuotes(curQuoteMoleculeId as string, {
                      FTE_unit_price:
                        formRef?.current?.getFieldValue('FTE_unit_price'),
                      ratio: formRef?.current?.getFieldValue('ratio')
                    })
                    getQuoteDetail()
                  }}
                >
                  {getWord('calculate-quotation')}
                </a>
              )
            }
            min={0.1}
            max={100}
          />
          <div className={styles.operateButton}>
            {!isCreateType || isEditorType ? (
              <Space>
                {canSaveDraft ? (
                  <ButtonWithLoading
                    onClick={() => handelQuoteStatus('editing')}
                  >
                    {getWord('save-draft')}
                  </ButtonWithLoading>
                ) : (
                  ''
                )}
                {canConfirm ? (
                  <ButtonWithLoading
                    onClick={() => {
                      if (!isEmpty(diffCosts))
                        return setOpenEvent({ open: true })
                      handelQuoteStatus('confirmed')
                    }}
                  >
                    {getWord('confirm-quote')}
                  </ButtonWithLoading>
                ) : (
                  ''
                )}
                {isConfirmedQuote ? (
                  <>
                    {!isEditorType && (
                      <>
                        {access?.authCodeList?.includes(
                          'quotation-records.button.editQuote'
                        ) && (
                          <Button onClick={() => setIsEditorType(true)}>
                            {getWord('edit')}
                          </Button>
                        )}
                        <OtherQuoteModal
                          form={form}
                          initalValues={{
                            compound_no: compound_no,
                            purity: quotesDetail?.purity,
                            ratio: quotesDetail?.ratio
                          }}
                          onFinish={(values: AmendOtherQuote) => {
                            createQuote(
                              {
                                ...values,
                                quote_id: curQuoteMoleculeId as string
                              },
                              (values: OtherQuoteCB) =>
                                createQuoteCB(values, true)
                            )
                          }}
                        />
                      </>
                    )}
                  </>
                ) : (
                  ''
                )}
              </Space>
            ) : (
              <ButtonWithLoading
                onClick={createNewQuote}
                loading={createLoading}
              >
                {getWord('pages.route.edit.label.confirm')}
              </ButtonWithLoading>
            )}
          </div>
        </ProForm>
        <SectionTitle
          anchorId="confirmedRoute"
          word={
            !isCreateType && isValidArray(conrifrmRoutes)
              ? getWord('quotate-route')
              : `${getWord('confirmed-route')}${
                  routesNum ? `（${routesNum}）` : ''
                }`
          }
        />
        {/* FIXME 切换 增加loading效果 */}
        <div className={styles.routeInfo}>
          {/* NOTE 能报价的路线只有 我的路线 */}
          <RenderRoutes />
          {!isCreateType ? (
            <Summary
              quotesDetail={quotesDetail}
              disabled={disabledEditInConfirmedStatus}
              curQuoteMoleculeId={curQuoteMoleculeId as string}
            />
          ) : (
            ''
          )}
        </div>
        {!isCreateType ? (
          <>
            <MaterialList
              quotesDetail={quotesDetail}
              disabled={disabledEditInConfirmedStatus}
              curQuoteMoleculeId={curQuoteMoleculeId as string}
            />
            <Schedule
              quotesDetail={quotesDetail}
              disabled={disabledEditInConfirmedStatus}
              curQuoteMoleculeId={curQuoteMoleculeId as string}
            />
            <SectionTitle
              anchorId="reactionDetail"
              word={getWord('reaction-details')}
            />
            {!isEmpty(quotesDetail?.cost_detail) &&
            isArray(quotesDetail?.cost_detail)
              ? quotesDetail?.cost_detail?.map(
                  (item: Cost_detail, index: number) => (
                    <MyReaction
                      disabled={disabledEditInConfirmedStatus}
                      costDetail={item}
                      curQuoteMoleculeId={curQuoteMoleculeId as string}
                      key={`reaction-${index}`}
                      projectCompoundId={route?.project_compound_id}
                      routeId={route?.project_route_id || route?.id}
                    />
                  )
                )
              : ''}
          </>
        ) : (
          ''
        )}
      </PageContainer>
      {/* TODO better 支持折叠展开 */}
      {/* FIXME 动态计算steps，支持 物料明细表、工时明细表 点击步骤进行跳转 */}
      <div style={{ flex: isEN() ? '0 0 170px' : '0 0 120px' }}>
        <CustomAnchor
          wrapClassName={isEN() ? styles.anchorEN : styles.anchor}
          items={
            !isCreateType
              ? commonAnchor.concat([
                  {
                    key: 'materialTable',
                    href: '#materialTable',
                    title: getWord('material-list')
                  },
                  {
                    key: 'laborTable',
                    href: '#laborTable',
                    title: getWord('work-time-detils')
                  },
                  {
                    key: 'reactionDetail',
                    href: '#reactionDetail',
                    title: getWord('reaction-details')
                  }
                ])
              : commonAnchor
          }
        />
        <ModalBase
          {...dialogProps}
          title={getWord('same-quatation')}
          width={800}
          onConfirm={repeatedWeightConfirm}
        >
          <span style={{ fontSize: '20px' }}>
            {isEN() ? (
              `A quotation already exists for the molecule ${repeatedWeight}. Would you like to requote (the previous quotation will be deleted)?`
            ) : (
              <>
                已经存在这个分子&nbsp;
                <span style={{ color: 'red' }}>{repeatedWeight}</span>&nbsp;
                的报价，是否重新报价（原来的报价会被删除）?
              </>
            )}
          </span>
        </ModalBase>
      </div>
    </div>
  )
}
