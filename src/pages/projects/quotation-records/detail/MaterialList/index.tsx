import QuoteMaterialTable from '@/components/QuoteMaterialTable'
import SectionTitle from '@/components/SectionTitle'
import type { Material } from '@/services/brain'
import { getWord } from '@/utils'
import { cloneDeep } from 'lodash'
import { useEffect, useState } from 'react'
import { useModel } from 'umi'
import styles from '../index.less'
import type { MaterialListProps } from './index.d'
export default function MaterialList(props: MaterialListProps) {
  const { curQuoteMoleculeId } = props
  const { updateQuotes } = useModel('quotation')
  const [allMaterials, setAllMaterials] = useState<Material[]>([])
  const handleSave = async (values) =>
    await updateQuotes(curQuoteMoleculeId as string, values)
  // let sum = 0

  const handleMaterialsStepId = (e: any) => {
    let newItem = cloneDeep(e)
    newItem.materials.forEach((item) => {
      item.step_no = newItem?.step_info?.step_no
      item.step_id = newItem?.step_info?.step_id
      // if (item?.material_quotation) sum += item?.material_quotation
    })
    return newItem
  }

  const getAllMaterials = () => {
    let _materials: Material[] = []
    let _costDetail = cloneDeep(props?.quotesDetail?.cost_detail)
    _costDetail.forEach((e) => {
      let newItem = handleMaterialsStepId(e)
      _materials = _materials.concat(newItem?.materials)
    })
    /* （原）报价总计，后续可能改为其他项的总计，目前先去掉报价列
    if (sum > 0) {
      _materials.push({
        no: uuidv4() as string,
        step_no: getWord( 'total')
      })
    } */
    setAllMaterials(_materials)
  }

  useEffect(() => {
    if (props?.quotesDetail?.cost_detail) getAllMaterials()
  }, [props?.quotesDetail?.cost_detail])

  return (
    <>
      <SectionTitle anchorId="materialTable" word={getWord('material-list')} />
      <div className={styles.content}>
        <QuoteMaterialTable
          enableEdit={!props?.disabled}
          enableAddMaterial={false}
          hiddeStep={false}
          materialData={allMaterials}
          handleSave={handleSave}
          quoteMoleculeId={curQuoteMoleculeId as string}
        />
      </div>
    </>
  )
}
