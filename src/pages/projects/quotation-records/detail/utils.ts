import { Cost_detail } from '@/services/brain'
import { isEmpty } from 'lodash'
import type { DiffCost } from './index.d'

export const findDiffCosts = (costDetail: Cost_detail[]) => {
  let diffCosts: DiffCost[] = []
  if (isEmpty(costDetail) || !costDetail) return []
  for (let i = 0; i < costDetail.length; i++) {
    for (let j = 0; j < costDetail[i]?.materials.length; j++) {
      let curItem = costDetail[i]?.materials[j]
      if (curItem?.cost_modified) {
        diffCosts.push({
          step_no: costDetail[i]?.step_info?.step_no,
          name_zh: curItem?.name_zh as string,
          name_en: curItem?.name_en as string,
          cas_no: curItem?.cas_no as string,
          minCost: curItem?.cost as number,
          cost: curItem?.set_cost as string
        })
      }
    }
  }
  return diffCosts
}
