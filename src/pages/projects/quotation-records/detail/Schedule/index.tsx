import SectionTitle from '@/components/SectionTitle'
import useOptions from '@/hooks/useOptions'
import { getWord, isValidArray } from '@/utils'
import { EditableProTable, type ProColumns } from '@ant-design/pro-components'
import { cloneDeep, toNumber } from 'lodash'
import { useEffect, useState } from 'react'
import { useModel } from 'umi'
import styles from '../index.less'
import type { Columns, ScheduleProps } from './index.d'
export default function Schedule(props: ScheduleProps) {
  const { curQuoteMoleculeId } = props
  const { editableConfig } = useOptions()

  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([])
  const [dataSource, setDataSource] = useState<readonly any[]>([])
  const { updateQuotes } = useModel('quotation')
  const handleLaborSum = () => {
    let sum = 0
    let _costDetail = cloneDeep(props?.quotesDetail?.cost_detail)
    if (isValidArray(_costDetail) && _costDetail.length > 1) {
      _costDetail.forEach((e) => {
        sum += Number(e?.labor)
      })
      if (sum > 0) {
        _costDetail.push({
          step_id: getWord('total'),
          labor: !isNaN(sum) && sum ? (toNumber(sum).toFixed(2) as number) : ''
        })
      }
    }
    setDataSource(_costDetail)
  }

  useEffect(() => {
    if (props?.quotesDetail?.cost_detail) handleLaborSum()
  }, [props?.quotesDetail?.cost_detail])

  const operate = (): ProColumns<Columns>[] => {
    return [
      {
        title: getWord('pages.experiment.label.operation'),
        valueType: 'option',
        width: 200,
        render: (
          _text: string,
          record: Columns,
          _: any,
          action: { startEditable: (step_id: string) => void }
        ) => {
          return [
            record?.step_id === getWord('total') ? (
              ''
            ) : (
              <a
                key="editable"
                onClick={() => {
                  action?.startEditable?.(record?.step_id)
                }}
              >
                {getWord('edit')}
              </a>
            )
          ]
        }
      }
    ]
  }
  const columns: ProColumns<Columns>[] = [
    {
      title: getWord('steps'),
      dataIndex: ['step_info', 'step_no'],
      readonly: true,
      render: (_text, record: Columns) =>
        record?.step_id === getWord('total') ? (
          record?.step_id
        ) : (
          <a href={`#${record?.step_info?.step_no}`}>
            {record?.step_info?.step_no}
          </a>
        )
    },
    {
      title: getWord('estimate-work-day'),
      dataIndex: 'labor'
    }
  ]
  return (
    <>
      <SectionTitle anchorId="laborTable" word={getWord('work-time-detils')} />
      <div className={styles.content}>
        <EditableProTable<any>
          rowKey="step_id"
          maxLength={5}
          scroll={{
            x: 960
          }}
          recordCreatorProps={false}
          loading={false}
          columns={props?.disabled ? columns : [...columns, ...operate()]}
          value={dataSource}
          editable={{
            ...editableConfig,
            type: 'multiple',
            editableKeys,
            actionRender: (_row, _config, defaultDom) => [
              defaultDom.save,
              defaultDom.cancel
            ],
            onSave: async (_rowKey, data) => {
              await updateQuotes(curQuoteMoleculeId as string, {
                step_id: data?.step_id,
                labor: Number(data?.labor)
              })
            },
            onChange: setEditableRowKeys
          }}
        />
      </div>
    </>
  )
}
