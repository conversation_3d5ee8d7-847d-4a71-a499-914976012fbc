@import '@/style/variables.less';
.unfoldWidth {
  width: 100%;
  max-width: calc(
    100vw - @menu-width - @anchor-width - @scrollbar-width -
      @layout-horizontal-padding*2
  ) !important;
}
.foldWidth {
  width: calc(
    100vw - @fold-menu-width - @anchor-width - @layout-horizontal-padding*2 -
      @scrollbar-width
  ) !important;
}
.unfoldWidth_EN {
  width: 100%;
  max-width: calc(
    100vw - @menu-width - 170 - @scrollbar-width - @layout-horizontal-padding*2
  ) !important;
}
.foldWidth_EN {
  width: calc(
    100vw - @fold-menu-width - 170 - @layout-horizontal-padding*2 -
      @scrollbar-width
  ) !important;
}

.quoteInfo {
  position: relative;
  display: flex;
  justify-content: space-between;
  .unitSelect {
    position: absolute;
    top: 0px;
    right: -60px;
  }
  .anchor {
    width: 120px;
  }
  .anchorEN {
    width: 170px;
  }
  .quoteDetail {
    flex: 1;
    width: 100%;
    padding-right: 5px;
    .content {
      position: relative;
      min-height: 162px;
      padding: 10px 20px;
      background-color: #fff;
      border-radius: 8px;
      .title {
        font-weight: bold;
        font-size: 16px;
      }
      .stepTitle {
        font-weight: bold;
        font-size: 18px;
      }
      .openLinkWrapper {
        position: absolute;
        top: 132px;
        left: 305px;
      }
      .procedureTitle {
        font-size: 20px;
      }
      .procedureReference {
        color: @color-text-e;
      }
      .quotationSummaryDetail {
        height: 32px;
        margin-top: 6px;
        font-size: 14px;
        line-height: 32px;
      }
    }

    .routeInfo {
      .content;
      width: 100%;
      overflow-x: 'scroll';
    }

    .structure {
      width: 270px;
      height: 109px;
      margin-bottom: 15px;
      border: 1px solid @border-color-base;
    }

    .referenceContent {
      .referenceStructure:extend(.structure) {
        width: auto !important;
        height: 150px !important;
      }
      position: relative;
      .laborEditor {
        position: absolute;
        top: 0px;
        // right: -62px;
      }
    }
    p {
      margin-top: 15px;
    }
    :global {
      .ant-pro-form-group {
        padding: 10px 20px;
      }
      .ant-form {
        position: relative;
        padding: 20px 10px 0px 0px;
        border-radius: 8px;
      }
      .ant-pro-card-body {
        padding: 0px;
      }
    }
    .operateButton {
      position: absolute;
      right: 10px;
      bottom: 28px;
    }
  }
}

.confirmModal {
  .title {
    margin: 12px 0 16px 0;
    color: #626262;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0em;
  }
  :global {
    .ant-modal-body {
      padding-bottom: 20px;
    }
  }
}
