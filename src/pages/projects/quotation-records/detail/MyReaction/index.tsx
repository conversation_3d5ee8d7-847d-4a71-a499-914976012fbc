import MoleculeStructure from '@/components/MoleculeStructure'
import ProcedureText from '@/components/ProcedureText'
import QuoteMaterialTable from '@/components/QuoteMaterialTable'
import { service } from '@/services/brain'
import { getWord, isEN } from '@/utils'
import { ProForm, ProFormDigit } from '@ant-design/pro-components'
import { useAccess } from '@umijs/max'
import { App, Button, Col, Popconfirm, Popover, Row, Space } from 'antd'
import cs from 'classnames'
import { useEffect, useState } from 'react'
import { useModel, useParams } from 'umi'
import { toInt } from '../../../../../utils/common'
import StepButton from '../StepButton'
import styles from '../index.less'
import type { MyReactionProps } from './index.d'
export default function MyReaction(props: MyReactionProps) {
  const { curQuoteMoleculeId, projectCompoundId, routeId } = props

  const { id: projectId } = useParams<{
    id: string
  }>()
  const { step_info, materials, labor } = props?.costDetail
  const [form] = ProForm.useForm()
  const { message } = App.useApp()
  const access = useAccess()

  useEffect(() => {
    form.setFieldsValue({
      labor: labor,
      yields: step_info?.procedure?.yields ? step_info?.procedure?.yields : 50
    })
  }, [labor, step_info?.procedure?.yields])

  const { updateQuotes } = useModel('quotation')
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false)
  const [yieldLoading, setYieldLoading] = useState<boolean>(false)
  const [yieldConfirm, setYieldConfirm] = useState<boolean>(false)
  const [yieldTip, setYieldTip] = useState<string>()
  const [canEditLabor, setCanEditLabor] = useState<boolean>(false)
  const [canEditLaborYield, setCanEditLaborYield] = useState<boolean>(false)
  const [cacheYields, setCacheYields] = useState<string>()
  const reactionNum: number = step_info?.reaction_ids?.length
  /**
   * 更新报价(我的反应 下一步 反应时发送的请求)
   * @param {('prev' | 'next')} step
   */
  const handleStepChange = async (step: 'prev' | 'next') =>
    await updateQuotes(
      curQuoteMoleculeId as string,
      {
        reaction_from_index:
          step === 'prev' ? step_info?.index - 1 : step_info?.index + 1,
        project_reaction_id: step_info?.step_id
      },
      '切换成功'
    )

  const confirm = async () => {
    let newYields = form.getFieldValue('yields')
    await updateQuotes(curQuoteMoleculeId as string, {
      step_id: step_info?.step_id,
      yields: toInt(newYields)
    })
    setYieldConfirm(false)
  }

  const cancel = () => {
    setYieldConfirm(false)
    form.setFieldValue('yields', cacheYields)
  }

  return (
    <>
      <div className={styles.content}>
        <span className={styles.stepTitle} id={step_info?.step_no}>
          {step_info?.step_no}
        </span>
        {step_info?.rxn ? (
          <MoleculeStructure
            structure={step_info?.rxn}
            className={cs(styles.structure, 'enablePointer')}
          />
        ) : (
          ''
        )}
        <Space className={styles.openLinkWrapper}>
          {access?.authCodeList?.includes(
            'view-by-backbone.tab.myReaction.reactionDetail'
          ) && (
            <Popover content={getWord('open-reaction-details-tip')}>
              <a
                className={styles.reactionLink}
                onClick={() => {
                  message.warning(getWord('modify-reaction-tip'))
                  window.open(
                    `/projects/${projectId}/reaction/${step_info?.step_id}`,
                    '_blank'
                  )
                }}
              >
                {getWord('open-reaction-details')}
              </a>
            </Popover>
          )}
          {access?.authCodeList?.includes(
            'quotation-records.button.openRoute'
          ) && (
            <Popover content={getWord('open-reaction-details-tip')}>
              <a
                className={styles.reactionLink}
                onClick={() => {
                  message.warning(getWord('modify-reaction-tip'))
                  const sp = new URLSearchParams()
                  sp.set('step', step_info?.rxn)
                  if (projectId && projectCompoundId && routeId) {
                    window.open(
                      `/projects/${projectId}/compound/${projectCompoundId}/view/${routeId}?${sp.toString()}`,
                      '_blank'
                    )
                  }
                }}
              >
                {getWord('open-route-reaction')}
              </a>
            </Popover>
          )}
        </Space>
        <div className="flex-justify-space-between">
          <div className={styles.stepTitle} style={{ fontWeight: 'normal' }}>
            {getWord('menu.list.workspace.myReaction')}
            {reactionNum ? `(${reactionNum})` : ''}
          </div>
          {reactionNum ? (
            <StepButton
              routesNum={reactionNum}
              handleStepChange={handleStepChange}
              route_index={step_info?.index}
            />
          ) : (
            ''
          )}
        </div>
        <Row>
          <Col span={12} className={styles.referenceContent}>
            {step_info?.procedure?.rxn ? (
              <MoleculeStructure
                structure={step_info?.procedure?.rxn}
                className={cs(styles.referenceStructure, 'enablePointer')}
              />
            ) : (
              ''
            )}
            <ProForm
              grid
              layout="horizontal"
              submitter={false}
              form={form}
              colProps={{ xs: 6, sm: 6, md: 6, lg: 10, offset: 1 }}
              labelCol={{ span: 10 }}
              wrapperCol={{ span: 8 }}
            >
              <ProFormDigit
                name="yields"
                label={`${getWord('yield')}%`}
                disabled={!canEditLaborYield}
                max={100}
                extra={
                  !props.disabled ? (
                    <Popconfirm
                      title={getWord('update-confirm-tip')}
                      description={yieldTip}
                      open={yieldConfirm}
                      onConfirm={confirm}
                      onCancel={cancel}
                      overlayStyle={{
                        width: '500px'
                      }}
                    >
                      <Button
                        type="link"
                        loading={yieldLoading}
                        onClick={async () => {
                          let newYields = form.getFieldValue('yields')
                          if (canEditLaborYield) {
                            setYieldLoading(true)
                            const { data, error } = await service(
                              `quote/yields/${curQuoteMoleculeId}/${projectId}`,
                              {
                                method: 'POST'
                              }
                            ).create({
                              step_id: step_info?.step_id,
                              yields: toInt(newYields)
                            })
                            if (error?.message) {
                              form.setFieldValue('yields', cacheYields)
                              message.error(error?.message)
                              setCanEditLaborYield(!canEditLaborYield)
                            }
                            if (data?.message) {
                              setYieldConfirm(true)
                              setYieldTip(data?.message)
                            }
                            setYieldLoading(false)
                          } else {
                            setCacheYields(newYields)
                          }
                          setCanEditLaborYield(!canEditLaborYield)
                        }}
                        className={styles.laborEditor}
                        style={{ right: isEN() ? '-68px' : '-62px' }}
                      >
                        {!canEditLaborYield
                          ? getWord('edit')
                          : getWord('pages.route.edit.label.confirm')}
                      </Button>
                    </Popconfirm>
                  ) : (
                    ''
                  )
                }
              />
              <ProFormDigit
                name="labor"
                label={getWord('man-days')}
                disabled={!canEditLabor}
                rules={[
                  {
                    pattern: /^(0*[1-9]\d*\.?\d*|0+\.\d*[1-9]\d*)$/,
                    message: getWord('please-input-positive-number')
                  }
                ]}
                extra={
                  !props.disabled ? (
                    <Button
                      type="link"
                      loading={confirmLoading}
                      onClick={async () => {
                        let newLabor = form.getFieldValue('labor')
                        if (!newLabor || newLabor <= 0)
                          return message.error(
                            getWord('please-input-positive-number')
                          )
                        if (canEditLabor) {
                          setConfirmLoading(true)
                          await updateQuotes(curQuoteMoleculeId as string, {
                            step_id: step_info?.step_id,
                            labor: Number.parseFloat(newLabor)
                          })
                          setConfirmLoading(false)
                        }
                        setCanEditLabor(!canEditLabor)
                      }}
                      className={styles.laborEditor}
                      style={{ right: isEN() ? '-68px' : '-62px' }}
                    >
                      {!canEditLabor
                        ? getWord('edit')
                        : getWord('pages.route.edit.label.confirm')}
                    </Button>
                  ) : (
                    ''
                  )
                }
              />
            </ProForm>
          </Col>
          <Col span={12}>
            <ProcedureText
              rows={6}
              procedure={{ text: step_info?.procedure?.procedure }}
            />
            {step_info?.procedure?.reference_type && (
              <div className={styles.procedureReference}>
                {getWord('source')}：{step_info?.procedure?.reference_type}
              </div>
            )}
          </Col>
        </Row>
        <QuoteMaterialTable
          enableEdit={!props.disabled}
          enableAddMaterial={true}
          hiddeStep={true}
          projectReactionId={step_info?.step_id}
          materialData={materials}
          quoteMoleculeId={curQuoteMoleculeId as string}
        />
      </div>
    </>
  )
}
