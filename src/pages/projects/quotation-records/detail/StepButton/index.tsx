import { getWord } from '@/utils'
import { Space } from 'antd'
import cs from 'classnames'

import type { StepButtonProps } from './index.d'
export default function StepButton(props: StepButtonProps) {
  const { route_index, routesNum, handleStepChange, disabled } = props
  return (
    <Space className={cs({ none: routesNum <= 1 || disabled })}>
      {route_index === 0 ? (
        <a className="disabledTip">
          {getWord('previous')}
        </a>
      ) : (
        <a onClick={() => handleStepChange('prev')}>
          {getWord('previous')}
        </a>
      )}
      {route_index < routesNum - 1 ? (
        <a onClick={() => handleStepChange('next')}>
          {getWord('next')}
        </a>
      ) : (
        <a className="disabledTip">
          {getWord('next')}
        </a>
      )}
    </Space>
  )
}
