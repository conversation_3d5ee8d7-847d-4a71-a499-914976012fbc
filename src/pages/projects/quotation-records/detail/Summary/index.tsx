import useOptions from '@/hooks/useOptions'
import { query, type Cost_summary } from '@/services/brain'
import { IOption } from '@/types/Common'
import { getWord, isValidArray } from '@/utils'
import type { ProColumns } from '@ant-design/pro-components'
import { EditableProTable } from '@ant-design/pro-components'
import { message } from 'antd'
import { cloneDeep, isArray, isEmpty } from 'lodash'
import { useEffect, useState } from 'react'
import { useModel } from 'umi'
import type { ConfigDicts } from '../../../../../services/brain/types/quotes'
import styles from '../index.less'
import type { SummaryProps } from './index.d'
type QuoteItem = '其他试剂' | '手性分离'
export default function Summary(props: SummaryProps) {
  const { curQuoteMoleculeId } = props
  const { chargeDes, editableConfig } = useOptions()
  const { updateQuotes } = useModel('quotation')
  const [quoteSummaryData, setQuoteSummaryData] = useState<
    readonly Cost_summary[]
  >([])
  const [chargeOptions, setChargeOptions] = useState<IOption[]>([])
  const [newItemName, setNewItemName] = useState<QuoteItem>(
    getWord('other-reagent')
  )

  const handleDisabledOption = (
    _chargeOptions: IOption[],
    targetLabel: string,
    enable?: boolean
  ) => {
    if (isEmpty(_chargeOptions)) return
    let newChargeOptions = cloneDeep(_chargeOptions)
    if (!isEmpty(newChargeOptions)) {
      if (enable) {
        let targetOption = newChargeOptions.find(
          (e) => e?.value === targetLabel || e?.label === targetLabel
        )
        console.log('---newName02---', targetLabel)
        setNewItemName(targetLabel)
        if (targetOption) targetOption.disabled = false
      } else {
        newChargeOptions.map((e) => {
          e.disabled = e?.value === targetLabel || e?.label === targetLabel
          return null
        })
      }
    }
    setChargeOptions(newChargeOptions)
  }

  const getNewItemName = (
    curQuoteSummaryData: Cost_summary[],
    _chargeOptions: IOption[]
  ) => {
    let newName = newItemName
    let alreadySelectedOther: boolean =
      curQuoteSummaryData.findIndex(
        (e) => e?.name === getWord('other-reagent') || e?.name === 'other'
      ) !== -1
    let alreadySelectedSeparation: boolean =
      curQuoteSummaryData.findIndex(
        (e) =>
          e?.name === getWord('chiral-separation') || e?.name === 'separation'
      ) !== -1
    if (alreadySelectedOther && alreadySelectedSeparation) {
      handleDisabledOption(_chargeOptions, getWord('other-reagent'))
      handleDisabledOption(_chargeOptions, getWord('chiral-separation'))
    } else if (alreadySelectedOther) {
      newName = getWord('chiral-separation') as QuoteItem
      handleDisabledOption(_chargeOptions, getWord('other-reagent'))
    } else if (alreadySelectedSeparation) {
      handleDisabledOption(_chargeOptions, getWord('chiral-separation'))
      newName = getWord('other-reagent') as QuoteItem
    }
    setNewItemName(newName)
  }

  const getChargeOptions = async (defaultDataSource: Cost_summary[]) => {
    const res = await query(`config-dicts`)
      .filterDeep('category', 'eq', 'cost')
      .get()
    let data = res?.data as ConfigDicts[],
      error = res?.error
    let finalData: IOption[] = []
    if (!isEmpty(data) && isArray(data)) {
      const cloneData = [...data]
      cloneData.map((e) => {
        finalData.push({
          value: e?.code,
          label: e?.name,
          disabled:
            defaultDataSource.findIndex((cur) => cur.name === e?.code) !== -1
        })
        return null
      })
    }
    if (error?.message) return message.error(error?.message)
    if (data) setChargeOptions(finalData)
    if (!isEmpty(defaultDataSource) && !isEmpty(finalData))
      getNewItemName(defaultDataSource, finalData)
  }

  useEffect(() => {
    if (!isEmpty(quoteSummaryData) && !isEmpty(chargeOptions))
      getNewItemName(quoteSummaryData as Cost_summary[], chargeOptions)
  }, [quoteSummaryData])

  const handleDataID = (originData: Cost_summary[]) => {
    return originData.map((item) => {
      return {
        ...item,
        id: item.name
      }
    })
  }

  useEffect(() => {
    if (!isValidArray(props?.quotesDetail?.cost_summary)) return
    const newData = handleDataID(props?.quotesDetail?.cost_summary)
    setQuoteSummaryData(newData)
    getChargeOptions(newData)
  }, [props?.quotesDetail?.cost_summary])

  const columns: ProColumns<Cost_summary>[] = [
    {
      title: getWord('charge-items'),
      dataIndex: 'name',
      valueType: 'select',
      fieldProps: (_form, { rowIndex }) => {
        if (rowIndex + 1 > props?.quotesDetail?.cost_summary?.length) {
          return {
            options: chargeOptions
          }
        } else {
          return {
            options: chargeOptions,
            disabled: true
          }
        }
      },
      render: (text) => (chargeDes[text] ? chargeDes[text] : text)
    },
    {
      title: 'RMB',
      dataIndex: 'RMB',
      valueType: 'digit'
    },
    {
      title: 'USD',
      dataIndex: 'USD',
      readonly: true
    }
  ]

  const operate = (): ProColumns<Cost_summary>[] => {
    return [
      {
        title: getWord('pages.experiment.label.operation'),
        valueType: 'option',
        width: 200,
        render: (_text, record, _, action) =>
          !['total_cost', 'material_cost', 'labor_cost'].includes(record?.name)
            ? [
                <a
                  key="editable"
                  onClick={() => {
                    action?.startEditable?.(record.name)
                  }}
                >
                  {getWord('edit')}
                </a>,
                <a
                  key="delete"
                  onClick={async () => {
                    setQuoteSummaryData(
                      quoteSummaryData.filter(
                        (item) => item.name !== record.name
                      )
                    )
                    handleDisabledOption(chargeOptions, record.name, true)
                    await updateQuotes(curQuoteMoleculeId, {
                      other_costs: {
                        name: record?.name,
                        RMB: Number(record?.RMB),
                        delete: true
                      }
                    })
                  }}
                >
                  {getWord('del')}
                </a>
              ]
            : '-'
      }
    ]
  }

  return (
    <>
      <div className={styles.content}>
        <div className={styles.title}>{`${getWord('quotation')} Summary`}</div>
        <div className={styles.quotationSummaryDetail}>
          {getWord('estimate-delivery')}:&nbsp;
          {props?.quotesDetail?.delivery_time}
        </div>
        <div
          className={styles.quotationSummaryDetail}
          style={{ marginBottom: '10px' }}
        >
          {`${getWord('quotation')} summary（RMB）`}
          :&nbsp;
          {props?.quotesDetail?.quotation_summary}
        </div>
        <div className={styles.title} style={{ marginBottom: '18px' }}>
          {getWord('cost-summary')}
        </div>
        <EditableProTable<Cost_summary>
          rowKey="id"
          maxLength={5}
          scroll={{
            x: 960
          }}
          loading={false}
          recordCreatorProps={
            props?.disabled
              ? false
              : {
                  position: 'bottom',
                  creatorButtonText: getWord('add-new-charge'),
                  record: () => ({ id: (Math.random() * 1000000).toFixed(0) })
                }
          }
          columns={props?.disabled ? columns : [...columns, ...operate()]}
          value={
            !isEmpty(chargeOptions) && !isEmpty(quoteSummaryData)
              ? (quoteSummaryData as Cost_summary[])
              : []
          }
          editable={{
            ...editableConfig,
            type: 'multiple',
            onSave: async (_, data) => {
              await updateQuotes(curQuoteMoleculeId as string, {
                other_costs: {
                  name: data?.name,
                  RMB: Number(data?.RMB)
                }
              })
            }
          }}
        />
      </div>
    </>
  )
}
