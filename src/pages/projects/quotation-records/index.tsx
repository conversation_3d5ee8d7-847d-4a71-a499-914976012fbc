import LoadingTip from '@/components/LoadingTip'
import ExperimentStatus from '@/components/StatusTip'
import useDownloadFile from '@/hooks/useFileDownload'
import { useCompoundFilterProps } from '@/pages/workspace/component/Filters/Compound'
import { IQuote, query } from '@/services/brain'
import { IPagination } from '@/types/Common'
import { getEnvConfig, getWord, isEN, isValidArray, toInt } from '@/utils'
import { DownloadOutlined } from '@ant-design/icons'
import {
  LightFilter,
  PageContainer,
  ProFormSelect
} from '@ant-design/pro-components'
import type { CheckboxProps } from 'antd'
import { Button, Checkbox, Pagination, Space, Switch } from 'antd'
import cs from 'classnames'
import { isArray, isEmpty } from 'lodash'
import { useEffect, useState } from 'react'
import { useAccess, useModel, useParams, useSearchParams } from 'umi'
import QuotationCard from '../components/QuotationCard'
import styles from './index.less'
export default function QuotationRecords() {
  const access = useAccess()
  const { downloadFile, fileDownloading } = useDownloadFile()
  const { id: projectId } = useParams<{ id: string }>()
  const [quotesData, setQuotesData] = useState<any>()
  const [loading, setLoading] = useState<boolean>(false)
  const [searchParams] = useSearchParams()
  const [pagenate, setPagenate] = useState<IPagination>({
    page: toInt(searchParams.get('page') || '') || 1,
    pageSize: toInt(searchParams.get('pageSize') || '') || 10
  })
  const [totalCount, setTotalCount] = useState<number>(0)
  const [compoundId, setCompoundId] = useState<number>()
  const [filterConformedRoute, setFilterConformedRoute] =
    useState<boolean>(true)
  const { initialState: { userInfo = undefined } = {} } =
    useModel('@@initialState')

  const userId = userInfo?.id

  const getQuotationRecords = async () => {
    setLoading(true)
    const request = query<IQuote>(
      `quotes?has_conformed_route=${filterConformedRoute}`
    ).equalTo('project_id', projectId || 0)
    if (compoundId) {
      request.equalTo('project_compound_id', compoundId)
    }
    const { data: _quotesData, meta } = await request
      .paginate(pagenate.page, pagenate.pageSize)
      .get()
    setQuotesData(_quotesData)
    setTotalCount(meta?.pagination?.total || 0)
    setLoading(false)
  }

  useEffect(() => {
    getQuotationRecords()
  }, [])

  useEffect(() => {
    getQuotationRecords()
  }, [pagenate, filterConformedRoute, compoundId])

  const { updatetIsCheckedAll, quoteCheckedList, isCheckedAll } =
    useModel('quotation')
  const handleSelectedDownload = async () => {
    downloadFile(
      `${getEnvConfig().apiBase}/api/quote/download`,
      `${getWord('project-num')}${projectId}-${getWord('quotation-sheet')}`,
      'post',
      JSON.stringify({
        data: {
          quote_ids: isCheckedAll ? [] : quoteCheckedList,
          project_id: projectId
        }
      })
    )
  }

  const compoundFilterProps = useCompoundFilterProps(
    userId,
    Number.parseInt(projectId || '0')
  )

  const hasPagination: boolean = isArray(quotesData) && !isEmpty(quotesData)
  const CheckedAll: CheckboxProps['onChange'] = (e) =>
    updatetIsCheckedAll(e.target.checked)

  const filterComp = (
    <div className="flex-align-items-center">
      {getWord('molecules-no')}
      <LightFilter bordered>
        <ProFormSelect
          {...compoundFilterProps}
          onChange={(v) => setCompoundId(v)}
        />
      </LightFilter>
    </div>
  )

  return (
    <PageContainer className={cs(styles.quotationRecords)}>
      <Space className="layoutRighButtons" style={{ top: '8px' }}>
        <Checkbox onChange={CheckedAll}>{getWord('select-all')}</Checkbox>
        {access?.authCodeList?.includes('quotation-records.download') && (
          <Button
            icon={<DownloadOutlined />}
            disabled={
              fileDownloading || (!isCheckedAll && isEmpty(quoteCheckedList))
            }
            onClick={() => handleSelectedDownload()}
          >
            {isEN() ? ' ' : ''}
            {getWord('download-selected-quatation')}
          </Button>
        )}
        {filterComp}
        <Switch
          checkedChildren={getWord('only-show-confirmed-routes')}
          unCheckedChildren={getWord('show-all')}
          onChange={(checked: boolean) => {
            setPagenate({ page: 1, pageSize: 10 })
            setFilterConformedRoute(checked)
          }}
          defaultChecked
        />
      </Space>
      {loading ? (
        <div className={cs(styles.loading, 'loadingPage')}>
          <LoadingTip />
        </div>
      ) : (
        <>
          <div className={styles.content}>
            {isValidArray(quotesData) ? (
              quotesData.map((quote, index) => (
                <QuotationCard key={`quote-${index}`} quote={quote} />
              ))
            ) : (
              <ExperimentStatus
                des={getWord('noticeIcon.empty')}
                wrapperClassName="fullLayoutContent"
              />
            )}
          </div>
          {hasPagination && (
            <Pagination
              className="pagination"
              total={totalCount}
              current={pagenate.page}
              pageSize={pagenate.pageSize}
              showSizeChanger={false}
              onChange={(page, pageSize) => setPagenate({ page, pageSize })}
            />
          )}
        </>
      )}
    </PageContainer>
  )
}
