@import '@/style/variables.less';
.messageNotification .content {
  display: flex;
  justify-content: space-between;
}

.center {
  flex: 0 0 16px;
}

.left,
.right {
  flex: 1;
}

.commonContent {
  display: flex;
  justify-content: center;
  min-height: 76vh;
}

.messageNotification {
  .content {
    .notification-list-root {
      :global {
        .ant-spin-container {
          .commonContent;
        }
      }
      width: 100%;
      padding: 4;
      background-color: #fff;
      .delButton:hover {
        color: @color-design-e !important;
        svg {
          fill: @color-design-e !important;
        }
      }

      :global {
        :where(.css-dev-only-do-not-override-dkbvqv).ant-btn:not(
            .ant-btn-icon-only
          )
          > .ant-btn-icon:not(:last-child) {
          margin-inline-end: 4px;
        }
      }
      .title-wrapper {
        display: flex;
        align-items: center;
        .title {
          margin: 0;
        }
      }
      .title-actions-wrapper {
        margin-left: auto;
      }
      .ant-list-item-extra {
        > div {
          height: 100%;
          .actions-wrapper {
            display: flex;
            flex-direction: column;
            height: 100%;
            margin-left: auto;
            .top-actions-wrapper {
              margin-left: auto;
            }
            .bottom-actions-wrapper {
              margin-top: auto;
            }
          }
        }
      }
      .notification-list {
        max-height: calc(@main-content-height - 10px);
        overflow-y: auto;
      }

      :global {
        .ant-pro-card-body {
          padding: 16px 0;
          .ant-pro-list-row-content {
            margin-inline: 0;
          }
          td.ant-descriptions-item {
            padding-bottom: 4px;
          }
        }
      }

      .commonTag {
        display: flex;
        align-items: center;
        width: max-content;
        height: 24px;
        padding: 0 4px;
        font-size: 12px;
      }
      .statusDes {
        &_completed,
        &_success {
          color: @color-completed;
          background-color: #ecf8ef;
          border: 1px solid @color-completed;
        }
        &_running {
          color: @color-running;
          border: 1px solid @color-running;
        }
        &_limited {
          color: @color-pending;
          border: 1px solid @color-pending;
        }
        &_pending {
          color: @color-pending;
          border: 1px solid @color-pending;
        }
        &_failed {
          color: @color-failed;
          border: 1px solid @color-failed;
        }
      }
    }
  }
}

.wrapSectionTitle {
  padding-left: 12px !important;
}
.listContent {
  max-height: calc(@main-content-height - 10px);
  overflow-y: auto;
}
.systemMessages {
  background-color: #fff;
  .readText {
    color: #929292;
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
  }
  .unReadText {
    .readText;
    color: #c34433;
  }
  :global {
    .ant-spin-container {
      .commonContent;
    }
    .ant-list-empty-text {
      display: flex;
      align-items: center;
    }
  }
}

.divider {
  margin: 4px 0;
}

.extraContent {
  height: 76px;
}
