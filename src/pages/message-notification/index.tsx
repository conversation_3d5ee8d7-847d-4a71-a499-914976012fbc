import { ReactComponent as DelIcon } from '@/assets/svgs/del.svg'
import { useBrainFetch } from '@/hooks/useBrainFetch'
import useNotification from '@/hooks/useNotifications'
import ButtonWithConfirm from '@/pages/projects/components/ButtonWithConfirm'
import { query, service } from '@/services/brain'
import {
  JobNotification,
  JobNotificationStatus
} from '@/services/brain/types/job-notification'
import { getWord, isEN } from '@/utils'
import { CloseOutlined } from '@ant-design/icons'
import {
  PageContainer,
  ProDescriptions,
  ProList
} from '@ant-design/pro-components'
import { history } from '@umijs/max'
import { Button, Divider, Space } from 'antd'
import cs from 'classnames'
import styles from './index.less'

const cancelableStatus: Readonly<JobNotificationStatus[]> = [
  'limited',
  'pending',
  'running'
] as const

import SectionTitle from '@/components/SectionTitle'
import SystemMessages from './SystemMessages'

export default function MessageNotification() {
  const { fetch } = useBrainFetch()
  const handleCheck = (url?: string) => {
    const isSamePath =
      document.location.pathname ===
      new URL(url || '', document.location.origin).pathname
    if (isSamePath) {
      history.go(0)
    } else if (url) {
      history.push(url)
    }
  }

  const { total, paginate, setPaginate, notifications, refetch, loading } =
    useNotification({ unreadOnly: true, handleCheck })

  const handleCancel = async (id: number) => {
    await fetch(service<JobNotification>('job-notifications').deleteOne(id))
    refetch()
  }
  const handleClose = async (id: number) => {
    await fetch(
      service<JobNotification>('job-notifications').update(id, { readed: true })
    )
    refetch()
  }

  const clearAllNotRunning = async () => {
    await fetch(
      query<JobNotification>('job-notifications/clean', {
        method: 'POST'
      }).get()
    )
    refetch()
  }

  const notificationList = (
    <div className={styles['notification-list-root']}>
      <SectionTitle
        word={getWord('pages.Notification.task')}
        wrapClassName={styles.wrapSectionTitle}
        extra={
          <Button
            type="link"
            className={cs(styles.delButton, 'flex-align-items-center')}
            icon={<DelIcon width={18} fill="#1677FF" />}
            onClick={clearAllNotRunning}
          >
            {getWord('pages.Notification.clear-tasks')}
          </Button>
        }
      />
      <Divider className={styles['divider']} orientationMargin={0} />
      <ProList<JobNotification>
        loading={loading}
        dataSource={notifications}
        className={styles.listContent}
        pagination={{
          current: paginate.page,
          pageSize: paginate.pageSize,
          simple: true,
          size: 'small',
          total,
          onChange: (page, pageSize) => setPaginate({ page, pageSize })
        }}
        itemLayout="vertical"
        metas={{
          content: {
            render: (_, e) => {
              return (
                <ProDescriptions<JobNotification> dataSource={e} column={1}>
                  <ProDescriptions.Item
                    label={getWord('pages.Notification.task-no')}
                    dataIndex="job_id"
                    render={(_, e) => {
                      return (
                        <Space>
                          {e.name || e.job_id || '-'}
                          <span
                            className={cs(
                              styles.statusDes,
                              styles.commonTag,
                              styles[`statusDes_${e.status}`]
                            )}
                            style={{
                              minWidth: isEN() ? '46px' : '32px',
                              maxWidth: isEN() ? '200px' : '64px'
                            }}
                          >
                            {getWord(
                              `component.notification.statusValue.${e.status}`
                            )}
                          </span>
                        </Space>
                      )
                    }}
                  />
                  <ProDescriptions.Item
                    label={getWord('project-type')}
                    dataIndex="job_type"
                    valueEnum={{
                      retro: {
                        text: getWord('component.notification.typeValue.retro')
                      }
                    }}
                  />
                  {['limited', 'pending'].includes(e.status) ? (
                    <>
                      {e.status === 'pending' ? (
                        <ProDescriptions.Item
                          label={getWord('tasks-in-queue')}
                          dataIndex="queue_count"
                        />
                      ) : (
                        ''
                      )}
                      <ProDescriptions.Item
                        label={getWord('estimate-start')}
                        dataIndex="predict_start_time"
                      />
                    </>
                  ) : (
                    ''
                  )}
                </ProDescriptions>
              )
            }
          },
          extra: {
            render: (_: unknown, e: JobNotification) => {
              return (
                <div className={styles['actions-wrapper']}>
                  <div className={styles['top-actions-wrapper']}>
                    {e.status !== 'running' && (
                      <Button
                        type="ghost"
                        onClick={() => handleClose(e.id)}
                        size="small"
                      >
                        <CloseOutlined />
                      </Button>
                    )}
                  </div>
                  <div className={styles['bottom-actions-wrapper']}>
                    <Space.Compact className={styles['bottom-actions-wrapepr']}>
                      <Button
                        size="small"
                        type="link"
                        onClick={() => handleCheck(e.access_url)}
                      >
                        {getWord('pages.projectTable.actionLabel.viewDetail')}
                      </Button>
                      {cancelableStatus.includes(e.status) && (
                        <ButtonWithConfirm
                          buttonProps={{ size: 'small' }}
                          key="cancel"
                          buttonText={getWord(
                            'pages.experiment.label.operation.cancel'
                          )}
                          title={getWord('cancel-task-confirm')}
                          type="link"
                          onConfirm={() => handleCancel(e.id)}
                          description={''}
                        />
                      )}
                    </Space.Compact>
                  </div>
                </div>
              )
            }
          }
        }}
      />
    </div>
  )

  return (
    <PageContainer className={cs(styles.messageNotification)}>
      <article className={styles.content}>
        <div className={styles.left}>
          <SystemMessages />
        </div>
        <div className={styles.center} />
        <div className={styles.right}>{notificationList}</div>
      </article>
    </PageContainer>
  )
}
