import SectionTitle from '@/components/SectionTitle'
import { useBrainFetch } from '@/hooks/useBrainFetch'
import useSystemMessages from '@/hooks/useSystemMessages'
import { MessageReaders } from '@/services/brain/types/message-readers'
import { MessageReader } from '@/types/models'
import { formatYMDHMTime, getWord } from '@/utils'
import { ProDescriptions, ProList } from '@ant-design/pro-components'
import { Divider, Switch, Tag } from 'antd'
import cs from 'classnames'
import { cloneDeep } from 'lodash'
import { useState } from 'react'
import styles from './index.less'
export default function SystemMessages() {
  const [readOnly, setreadOnly] = useState(false)
  const {
    loading,
    systemMessagesDatas,
    paginate,
    total,
    setPaginate,
    updateSystemMessagesDatas
  } = useSystemMessages({ readOnly })
  const { updateByStrapi } = useBrainFetch()
  const updateStatus = (curId: number) => {
    updateByStrapi(
      `message-readers/${curId}`,
      {
        readed: true
      },
      () => {
        let _systemMessagesDatas = cloneDeep(systemMessagesDatas)
        let curItem = _systemMessagesDatas.find((cur) => cur?.id === curId)
        curItem.readed = true
        updateSystemMessagesDatas(_systemMessagesDatas)
      }
    )
  }
  return (
    <div className={styles.systemMessages}>
      <SectionTitle
        word={getWord('message')}
        wrapClassName={styles.wrapSectionTitle}
        extra={
          <div className="flex-align-items-center" style={{ height: '40px' }}>
            <span>{getWord('show-read-messages')}</span>
            &nbsp;
            <Switch
              checkedChildren={getWord('yes')}
              unCheckedChildren={getWord('no')}
              defaultChecked={false}
              onChange={(e) => setreadOnly(e)}
            />
          </div>
        }
      />
      <Divider className={styles['divider']} orientationMargin={0} />
      <ProList<MessageReaders>
        loading={loading}
        dataSource={systemMessagesDatas}
        className={styles.listContent}
        pagination={{
          current: paginate.page,
          pageSize: paginate.pageSize,
          simple: true,
          size: 'small',
          total,
          onChange: (page, pageSize) => setPaginate({ page, pageSize })
        }}
        itemLayout="vertical"
        metas={{
          content: {
            render: (_, e) => (
              <ProDescriptions<MessageReaders>
                dataSource={e}
                column={1}
                className={cs({ enablePointer: !e?.readed })}
                onClick={() => {
                  if (e?.readed || !e?.id) return
                  updateStatus(e?.id)
                }}
              >
                <ProDescriptions.Item
                  dataIndex="event_level"
                  render={(_, ee) => {
                    let message_level = ee?.system_message?.message_level
                    return message_level ? (
                      <>
                        <Tag color="magenta">{message_level}</Tag>
                        <div
                          dangerouslySetInnerHTML={{
                            __html: ee?.system_message?.message as string
                          }}
                        />
                      </>
                    ) : (
                      ''
                    )
                  }}
                />
                <ProDescriptions.Item
                  dataIndex="createdAt"
                  render={(_, ee) => {
                    let createdAt = ee?.system_message?.createdAt
                    let sendFrom = ee?.system_message?.send_from
                    return createdAt ? (
                      <>
                        <div>
                          {getWord('send-time')}：{formatYMDHMTime(createdAt)}
                        </div>
                        &nbsp;&nbsp;
                        <div>
                          {getWord('send-from')}：{sendFrom ? sendFrom : '-'}
                        </div>
                      </>
                    ) : (
                      ''
                    )
                  }}
                />
              </ProDescriptions>
            )
          },
          extra: {
            render: (_, e: MessageReader) => {
              return (
                <div
                  className={cs(styles.extraContent, {
                    enablePointer: !e?.readed
                  })}
                  onClick={() => {
                    if (e?.readed || !e?.id) return
                    updateStatus(e?.id)
                  }}
                >
                  <div>
                    {e?.readed ? (
                      <span className={styles.readText}>{getWord('read')}</span>
                    ) : (
                      <span className={styles.unReadText}>
                        {getWord('unread')}
                      </span>
                    )}
                  </div>
                </div>
              )
            }
          }
        }}
      />
    </div>
  )
}
