.experiment-conclusion-page-root {
  .basic-info-wrapper {
    margin: 12px;
  }
  .detail-title-wrapper {
    > div > div {
      position: relative;
      display: flex;
    }
    .ant-affix {
      background-color: white;
    }
    .anchor {
      .ant-anchor {
        padding: 8px 12px;
      }
    }
    .action-buttons-wrapper {
      margin-top: 0;
      margin-left: auto;
    }

    .ant-anchor-wrapper-horizontal::before {
      content: none;
    }
    .divider {
      margin-left: 12px;
    }
  }

  .section-wrapper {
    margin: 36px 12px;
    .section-title {
      display: flex;
      &::before {
        position: absolute;
        left: 0;
        width: 4px;
        height: 24px;
        margin-top: 2px;
        background-color: #027aff;
        border-radius: 4px;
        content: '';
      }
    }
    .section-action {
      margin-left: auto;
    }
  }

  #operations {
    --timeline-left-width: 150px;
    .ant-timeline-item.ant-timeline-item-left {
      .ant-timeline-item-label {
        width: calc(var(--timeline-left-width) - 12px);
      }
      .ant-timeline-item-tail {
        inset-inline-start: var(--timeline-left-width);
      }
      .ant-timeline-item-head {
        inset-inline-start: var(--timeline-left-width);
      }
      .ant-timeline-item-content {
        width: calc(100% - var(--timeline-left-width) - 28px);
        inset-inline-start: calc(var(--timeline-left-width) - 4px);
      }
    }
    .exception-actions-wrapper {
      margin-left: auto;
    }
    .exception-view-wrapper {
      &.full-width .ant-descriptions-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
      }
    }
  }

  #result {
    .note-wrapper .ant-descriptions-item-label {
      align-items: center;
    }
    form {
      margin-right: 12px;
      background-color: transparent;
    }
    .result-actions-wrapper {
      margin-left: auto;
    }
  }

  #announce {
    .announce-text {
      input {
        width: fit-content;
        min-width: 120px;
        padding: 0 4px;
        text-align: center;
        border-bottom: 1px solid;
        border-radius: 0;
      }
    }
  }
}
