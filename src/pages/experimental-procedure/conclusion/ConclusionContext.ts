import { parseResponseResult } from '@/services'
import { apiExperimentConclusionDetail } from '@/services/experiment-conclution'
import { ExperimentConclusionDetailResponse } from '@/services/experiment-conclution/index.d'
import { createContext, useCallback, useEffect, useMemo, useState } from 'react'

export interface Context {
  conclusion?: ExperimentConclusionDetailResponse
  refetch?: () => Promise<void>
  loading?: boolean
}

export const useConclusionContext = (experimentNo?: string): Context => {
  const [conclusion, setConclusion] =
    useState<ExperimentConclusionDetailResponse>()
  const [loading, setLoading] = useState<boolean>(false)
  const [mounted, setMounted] = useState<boolean>(true)

  const fetch = useCallback(async () => {
    if (!experimentNo) return
    setLoading(true)
    const res = await apiExperimentConclusionDetail({
      routeParams: experimentNo
    })
    setLoading(false)
    if (parseResponseResult(res).ok && mounted) {
      setConclusion(res.data)
    }
  }, [experimentNo])

  useEffect(() => {
    fetch()
    return () => setMounted(false)
  }, [experimentNo])

  return useMemo(() => ({ loading, conclusion, refetch: fetch }), [conclusion])
}

export const ConclusionContext = createContext<Context>({})
