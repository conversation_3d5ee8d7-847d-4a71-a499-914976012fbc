import { parseResponseResult } from '@/services'
import {
  apiListExceptionMethod,
  apiListExceptionReasonType
} from '@/services/experiment-exception'
import { ExceptionHandleMethod } from '@/services/experiment-exception/index.d'
import { ProSchemaValueEnumObj } from '@ant-design/pro-components'
import { DefaultOptionType } from 'antd/es/select'
import { useEffect, useMemo, useState } from 'react'

export const useExceptionHandleMethods = () => {
  const [methods, setMethods] = useState<ExceptionHandleMethod[]>([])

  const fetch = async () => {
    if (methods.length) return methods
    const res = await apiListExceptionMethod({ params: { locale: 'zh' } })
    if (parseResponseResult(res).ok) {
      return res.data
    }
    return []
  }

  useEffect(() => {
    fetch().then(setMethods)
  }, [])

  const codeToName = useMemo(() => {
    return methods
      .map((m) => [m.handlerMethodNo, m.handlerMethodName])
      .reduce<Record<string, string>>((acc, cur) => {
        acc[cur[0]] = cur[1]
        return acc
      }, {})
  }, [methods])

  return { fetch, codeToName }
}

export const useExceptionReasonType = (): [
  DefaultOptionType[],
  ProSchemaValueEnumObj
] => {
  const [options, setOptions] = useState<DefaultOptionType[]>()
  const fetch = async () => {
    const res = await apiListExceptionReasonType({})
    if (parseResponseResult(res).ok) {
      return Object.entries(res.data).map(([k, v]) => ({ label: v, value: k }))
    }
    return []
  }

  useEffect(() => {
    let mount = true
    fetch().then((ops) => mount && setOptions(ops))
    return () => {
      mount = false
    }
  }, [])

  const enums = useMemo(
    () =>
      options
        ? options.reduce<ProSchemaValueEnumObj>((acc, cur) => {
            if (cur.value) acc[cur.value] = cur.label
            return acc
          }, {})
        : {},
    [options]
  )

  return [options || [], enums]
}
