import { useModalBase } from '@/components/ModalBase/useModalBase'
import { experimentStatus } from '@/components/ReactionTabs/ExperimentListTab/util'
import ReagentList from '@/components/ReagentList'
import { parseResponseResult } from '@/services'
import type { MaterialTable } from '@/services/brain'
import { query } from '@/services/brain'
import { apiUpdateWorkflowParam } from '@/services/experiment-exception'
import { decodeUrl, encodeString, getWord } from '@/utils'
import { PageContainer, ProDescriptions } from '@ant-design/pro-components'
import { history, useParams } from '@umijs/max'
import { Affix, Anchor, Button, Divider, Space } from 'antd'
import React, { ReactElement, useEffect, useState } from 'react'
import { ConclusionContext, useConclusionContext } from './ConclusionContext'
import './index.less'
import AnalysisRecord from './sections/AnalysisRecord'
import Announce from './sections/Announce'
import Conclusion from './sections/Conclusion'
import Operation from './sections/Operation'
import Result from './sections/Result'
const ExperimentConclusion: React.FC = () => {
  const { experimentalNo: encodeId } = useParams()
  const { dialogProps, confirm } = useModalBase()
  const base64Regex = /^[A-Za-z0-9+/]*(=){0,2}$/
  const id = base64Regex.test(encodeId)
    ? encodeId && JSON.parse(decodeUrl(encodeId))
    : encodeId
  const context = useConclusionContext(id)
  const { conclusion } = context
  const [ownerName, setOwnerName] = useState<string>('')

  const loc = () => {
    const hash = window.location.hash
    if (!hash) return
    const el = document.getElementById(hash.slice(1))
    if (el) {
      el.style.scrollMargin = '50px'
      el.scrollIntoView({ behavior: 'smooth', block: 'start' })
      el.style.scrollMargin = ''
    }
  }

  useEffect(() => {
    setTimeout(() => {
      loc()
    }, 500)
  }, [])

  useEffect(() => {
    if (conclusion?.experiment_owner) {
      query(`users/${conclusion.experiment_owner}`, { normalizeData: false })
        .get()
        .then((d) => {
          setOwnerName((d as unknown as { username: string }).username)
        })
    }
  }, [conclusion?.experiment_owner])

  const toKnowledgeBase = async () => {
    const res = await apiUpdateWorkflowParam({
      routeParams: id
    })
    if (!parseResponseResult(res).ok) return
    history.push(
      `${window.location.href}/knowledgeBase/${encodeString(
        JSON.stringify(conclusion?.experiment_design_id)
      )}`
    )
  }
  /* http://localhost:8000/projects/48/reaction/299/experimental-procedure/conclusion/JTIyRTIwMjMtMDgtMTVfMSUyMg== */

  return (
    <PageContainer className="experiment-conclusion-page-root">
      <ConclusionContext.Provider value={context}>
        <ProDescriptions dataSource={conclusion} className="basic-info-wrapper">
          <ProDescriptions.Item
            label={getWord('pages.experiment.label.no')}
            valueType="text"
            dataIndex="experiment_no"
          />
          <ProDescriptions.Item
            label={getWord('pages.experiment.label.personInCharge')}
            valueType="text"
            dataIndex="experiment_owner"
            render={() => ownerName || '-'}
          />
          <ProDescriptions.Item
            label={getWord('pages.experiment.label.status')}
            valueType="select"
            dataIndex="status"
            valueEnum={experimentStatus.reduce<Record<string, ReactElement>>(
              (acc, cur) => {
                acc[cur] = getWord(`pages.experiment.statusLabel.${cur}`)
                return acc
              },
              {}
            )}
          />
          <ProDescriptions.Item
            label={getWord('yield')}
            valueType="percent"
            dataIndex="estimate_yield"
          />
          <ProDescriptions.Item
            label={getWord('experiment-actual-start-time')}
            valueType="dateTime"
            dataIndex="start_time"
          />
          <ProDescriptions.Item
            label={getWord('experiment-actual-end-time')}
            valueType="dateTime"
            dataIndex="end_time"
          />
        </ProDescriptions>
        <Affix className="detail-title-wrapper">
          <div>
            <Anchor
              direction="horizontal"
              className="anchor"
              offsetTop={56}
              affix={false}
              getCurrentAnchor={(e) => {
                const hash = window.location.hash
                if (!e && hash) {
                  return hash
                }
                return e
              }}
              items={[
                {
                  key: 'operations',
                  href: '#operations',
                  title: getWord('task-list')
                },
                {
                  key: 'analysis',
                  href: '#analysis',
                  title: getWord(
                    'pages.experiment.label.operation.detectRecord'
                  )
                },
                {
                  key: 'result',
                  href: '#result',
                  title: getWord('conclusion')
                },
                {
                  key: 'conclusion',
                  href: '#conclusion',
                  title: getWord(
                    'menu.list.project-list.detail.experiment-conclusion'
                  )
                },
                {
                  key: 'announce',
                  href: '#announce',
                  title: getWord('declaration')
                }
              ]}
            />
            <Space className="action-buttons-wrapper">
              {/*  <Button
                type="default"
                size="small"
                onClick={() =>
                  history.push(
                    `/experiment/experiment-execute/detail/${encodeString(
                      JSON.stringify(params)
                    )}`
                  )
                }
              >
                流程图
              </Button> */}
              <Button type="default" size="small" onClick={() => confirm()}>
                {getWord('pages.reaction.label.material-sheet')}
              </Button>
              <Button type="default" size="small" onClick={toKnowledgeBase}>
                {/* http://localhost:8000/projects/48/reaction/299/experimental-procedure/conclusion/MTE5MA==/knowledgeBase/E-202310173 */}
                {getWord(
                  'menu.list.project-list.detail.experiment-conclusion.knowledgeBase'
                )}
              </Button>
            </Space>
            <Divider
              className="divider"
              style={{ position: 'absolute', top: 22 }}
            />
          </div>
        </Affix>
        <Operation />
        <AnalysisRecord
          analysisData={conclusion?.analysis_records}
          experimentalNo={id}
        />
        <Result />
        <Conclusion />
        <Announce />
      </ConclusionContext.Provider>
      <ReagentList
        dialogProps={dialogProps}
        structure={conclusion?.rxn_smiles || ''}
        material_table={conclusion?.materials as MaterialTable[]}
      />
    </PageContainer>
  )
}

export default ExperimentConclusion
