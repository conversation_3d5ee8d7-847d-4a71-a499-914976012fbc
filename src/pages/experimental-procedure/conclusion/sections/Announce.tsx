import { parseResponseResult } from '@/services'
import { apiUpdateExperimentAuditor } from '@/services/experiment-conclution'
import { getWord } from '@/utils'
import { useModel } from '@umijs/max'
import { App, Button, Input, Space, Typography } from 'antd'
import React, { useContext, useState } from 'react'
import { ConclusionContext } from '../ConclusionContext'
import Section from './Section'

const Announce: React.FC = ({}) => {
  const { initialState } = useModel('@@initialState')
  const userName = initialState?.userInfo?.username
  const { message } = App.useApp()
  const [input, setInput] = useState<string>('')
  const [error, setError] = useState<string>('')
  const { conclusion, refetch, loading } = useContext(ConclusionContext)

  const onConfirm = async () => {
    const experimentNo = conclusion?.experiment_no
    if (!experimentNo) return
    if (!input) {
      setError('未输入用户名')
    } else if (input !== userName) {
      setError('输入用户名非登录用户')
    } else {
      setError('')
      const res = parseResponseResult(
        await apiUpdateExperimentAuditor({
          data: { auditor: input, experiment_no: experimentNo }
        })
      )
      if (res.ok) {
        message.success('确认审核成功')
        await refetch?.()
      } else {
        message.error(res.msg)
      }
    }
  }

  return (
    <Section title={getWord('declaration')} id="announce">
      <Space direction="vertical">
        <Typography.Text className="announce-text">
          <Space>
            {getWord('i')}
            {conclusion?.auditor ? (
              <Typography.Text strong>{conclusion.auditor}</Typography.Text>
            ) : (
              <Input
                disabled={loading}
                placeholder={userName}
                bordered={false}
                onChange={(e) => setInput(e.target.value)}
                value={input}
              />
            )}
            {getWord('declare-tip')}
          </Space>
        </Typography.Text>

        <Typography.Text type="danger" className="error-text">
          {error}
        </Typography.Text>

        {!conclusion?.auditor && (
          <Button
            type="primary"
            size="small"
            onClick={onConfirm}
            disabled={loading}
          >
            {getWord('confirm')}
          </Button>
        )}
      </Space>
    </Section>
  )
}

export default Announce
