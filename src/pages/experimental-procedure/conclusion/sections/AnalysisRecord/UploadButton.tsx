import {
  CheckStatus,
  ExperimentAnalysis
} from '@/services/experiment-conclution/index.d'
import { getEnvConfig, getWord } from '@/utils'
import { App, Button, Upload, UploadProps } from 'antd'
import React, { useContext } from 'react'
import { ConclusionContext } from '../../ConclusionContext'
type ExtendedCheckStatus = CheckStatus | 'running' | 'hold'
const checkStatusesSupportUpload: ExtendedCheckStatus[] = [
  'checking',
  'running',
  'hold',
  'finished'
]

export interface UploadButtonProps {
  editable: boolean
  analysis: ExperimentAnalysis
  requestList?: () => void
}

const UploadButton: React.FC<UploadButtonProps> = ({
  editable,
  analysis,
  requestList
}) => {
  const { message } = App.useApp()
  const { refetch } = useContext(ConclusionContext)

  /** 上传检测报告 */
  const props: UploadProps = {
    action: `${getEnvConfig().apiBase}/api/run/experiment/analysis/upload/${
      analysis.check_no
    }`,
    showUploadList: false,
    beforeUpload: (file) => {
      const isPdf =
        file.type === 'application/pdf' || file.name.endsWith('.pdf')
      if (!isPdf) {
        message.error(`请上传PDF文件`)
      }
      return isPdf || Upload.LIST_IGNORE
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name}上传成功！`)
        refetch?.()
        requestList?.()
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name}上传失败！`)
      }
    }
  }

  return (
    <Upload {...props}>
      <Button
        type="link"
        size="small"
        disabled={
          !checkStatusesSupportUpload.includes(
            analysis.status as CheckStatus
          ) || !editable
        }
      >
        {getWord('upload-report')}
      </Button>
    </Upload>
  )
}

export default UploadButton
