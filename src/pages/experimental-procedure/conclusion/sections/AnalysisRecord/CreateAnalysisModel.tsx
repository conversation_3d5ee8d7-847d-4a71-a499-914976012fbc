import { apiExperimentPlanNo, parseResponseResult } from '@/services'
import {
  apiCreateExperimentAnalysis,
  apiGetSolvents
} from '@/services/experiment-conclution'
import { ExperimentAnalysisCreate } from '@/services/experiment-conclution/index.d'
import { getWord, isReactionDetail } from '@/utils'
import {
  ModalForm,
  ProFormDependency,
  ProFormDigit,
  ProFormGroup,
  ProFormList,
  ProFormRadio,
  ProFormSelect
} from '@ant-design/pro-components'
import { Button, Form, message } from 'antd'
import React, { useContext, useEffect, useState } from 'react'

import { useModel, useParams, useSearchParams } from '@umijs/max'
import { ConclusionContext } from '../../ConclusionContext'

export interface CreateAnalysisModelProps {
  experimentNo: string
}

const CreateAnalysisModel: React.FC<CreateAnalysisModelProps> = ({
  experimentNo
}) => {
  const { searchExperimentCheck, experimentListParams } = useModel('experiment')
  const { refetch } = useContext(ConclusionContext)
  const [form] = Form.useForm<ExperimentAnalysisCreate>()
  const method = Form.useWatch('check_method', form)
  useEffect(() => {
    form.setFieldValue('solvents', [])
  }, [method])

  const { reactionId } = useParams()

  const create = async () => {
    const values = form.getFieldsValue()
    const res = await apiCreateExperimentAnalysis({
      data: {
        ...values,
        status: values.check_method === 'TLC' ? 'checking' : 'todo',
        start_from: 'web'
      }
    })
    if (parseResponseResult(res).ok) {
      message.success(getWord('operate-success'))
      refetch?.()
      if (isReactionDetail()) {
        searchExperimentCheck({
          ...experimentListParams,
          project_reaction_id: reactionId
        })
      }
      return true
    } else {
      message.error('创建检测失败！')
      return false
    }
  }

  const [experimentPlanNoList, setExperimentPlanNoList] = useState<string[]>([])
  const getExperimentPlanNo = async () => {
    const res = await apiExperimentPlanNo({
      routeParams: reactionId
    })
    if (parseResponseResult(res).ok) setExperimentPlanNoList(res.data)
  }

  useEffect(() => {
    getExperimentPlanNo()
  }, [])
  const [searchParams] = useSearchParams()
  return (
    <ModalForm<ExperimentAnalysisCreate>
      trigger={
        <Button type="primary" size="small">
          {getWord('new-test')}
        </Button>
      }
      onOpenChange={() => form.resetFields()}
      form={form}
      title={getWord('new-test')}
      onFinish={create}
      initialValues={{
        experiment_no: experimentNo || searchParams.get('experimentNo')
      }}
    >
      <ProFormSelect
        width="sm"
        name="experiment_no"
        label={getWord('pages.experiment.label.no')}
        rules={[{ required: true }]}
        options={experimentPlanNoList}
      />
      <ProFormRadio.Group
        name="check_type"
        label={getWord('test-type')}
        rules={[{ required: true }]}
        options={[
          { value: 'M', label: getWord('intermediate-detection') },
          { value: 'F', label: getWord('product-detection') }
        ]}
      />
      <ProFormSelect
        width="sm"
        name="check_method"
        label={getWord('test-method')}
        rules={[{ required: true }]}
        options={['TLC', 'LCMS', 'NMR']}
      />
      <ProFormDependency name={['check_method', 'solvents']}>
        {({ check_method, solvents }) => {
          const label =
            check_method === 'TLC'
              ? getWord('developing-agent')
              : getWord('solvent')
          if (!check_method) return null
          return (
            <ProFormList
              name="solvents"
              label={solvents && solvents.length > 0 ? undefined : label}
              required
              min={1}
              max={5}
              copyIconProps={false}
              rules={[
                {
                  required: true,
                  validator: (_, value) => {
                    if (!value || value?.length < 1) {
                      return Promise.reject(
                        `${getWord('at-least-add-one')}${label}`
                      )
                    }
                    const names: string[] = value
                      .map((v: { solvent_name: string }) => v.solvent_name)
                      .filter((n: string) => !!n)
                    if (names.length !== new Set(names).size) {
                      return Promise.reject(`${getWord('exists-tip')}${label}`)
                    }
                    return Promise.resolve()
                  }
                }
              ]}
            >
              <ProFormGroup key="group">
                <ProFormSelect
                  width="sm"
                  name="solvent_name"
                  label={label}
                  rules={[{ required: true }]}
                  fieldProps={{
                    onChange: () => form.validateFields(['solvents'])
                  }}
                  request={async () => {
                    const res = await apiGetSolvents({
                      routeParams: check_method
                    })
                    return res.data.map((n) => ({ value: n.name }))
                  }}
                />
                {check_method === 'TLC' && (
                  <ProFormDigit
                    width="sm"
                    name="ratio"
                    label={getWord('ratio')}
                    min={1}
                    max={120}
                    initialValue={1}
                    rules={[
                      { required: true },
                      {
                        pattern: /^[1-9]\d*$/,
                        message: getWord('positive-integer-tip')
                      }
                    ]}
                  />
                )}
              </ProFormGroup>
            </ProFormList>
          )
        }}
      </ProFormDependency>
    </ModalForm>
  )
}

export default CreateAnalysisModel
