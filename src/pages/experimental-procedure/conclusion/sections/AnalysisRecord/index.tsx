import StatusRender from '@/components/StatusRender'
import useOptions from '@/hooks/useOptions'
import EnumSwitcher from '@/pages/projects/components/EnumSwitcher'
import { apiExperimentChekOperators, parseResponseResult } from '@/services'
import { apiUpdateExperimentAnalysis } from '@/services/experiment-conclution'
import {
  CheckStatus,
  ExperimentAnalysis,
  ExperimentAnalysisUpdate
} from '@/services/experiment-conclution/index.d'
import { getWord, isEN, isValidArray } from '@/utils'
import { ProColumns, ProTable } from '@ant-design/pro-components'
import { history, useModel, useParams, useSearchParams } from '@umijs/max'
import { App, Form, Space, Switch } from 'antd'
import Button from 'antd/es/button'
import React, { useContext, useState } from 'react'
import { ConclusionContext } from '../../ConclusionContext'
import '../../index.less'
import Section from '../Section'
import CreateAnalysisModel from './CreateAnalysisModel'
import UploadButton from './UploadButton'
import type { IAnalysisRecord } from './index.d'
import styles from './index.less'

const statusTransformMap: Record<CheckStatus, CheckStatus[]> = {
  todo: ['checking', 'canceled'], // 待送样
  checking: ['finished'], // 待检测
  canceled: [],
  finished: []
}

const AnalysisRecord: React.FC = (props: IAnalysisRecord) => {
  const { id: projectId, reactionId } = useParams()
  const { searchExperimentCheck, experimentListParams } = useModel('experiment')
  const { analysisData, isAnalysisTab, requestEvent, experimentalNo } = props
  const getExperimentCheckList = () => {
    if (isAnalysisTab) searchExperimentCheck(experimentListParams)
  }
  const { aiAIInferenceStauts } = useOptions()

  const editColumns: (
    editable: boolean,
    update: (
      analysis: ExperimentAnalysisUpdate,
      confirm?: boolean
    ) => Promise<boolean>
  ) => ProColumns<ExperimentAnalysis>[] = (editable, update) => [
    {
      title: getWord('test-status'),
      key: 'status',
      dataIndex: 'status',
      hideInSearch: !isAnalysisTab,
      valueEnum: {
        todo: { text: getWord('pages.experiment.check.statusLabel.todo') },
        checking: {
          text: getWord('pages.experiment.check.statusLabel.checking')
        },
        canceled: {
          text: getWord('pages.experiment.check.statusLabel.canceled')
        },
        finished: {
          text: getWord('pages.experiment.check.statusLabel.finished')
        }
      },
      render: (_, { check_no, status }) => (
        <div style={{ width: 'fit-content' }}>
          <EnumSwitcher<CheckStatus>
            currentValue={status || 'todo'}
            valueRender={(s) => (
              <StatusRender
                status={s}
                labelPrefix="pages.experiment.check.statusLabel"
              />
            )}
            avalibleValues={
              editable && status ? statusTransformMap[status] : []
            }
            onSelect={async (status) => {
              update({ check_no, status, start_from: 'web' }, true)
            }}
          />
        </div>
      )
    },
    {
      title: getWord('AI-inference-status'),
      key: 'report_analysis_result',
      dataIndex: 'report_analysis_result',
      hideInSearch: !isAnalysisTab,
      render: (_, item) => {
        return (
          <>
            {item?.report_analysis_result?.status
              ? aiAIInferenceStauts[item?.report_analysis_result?.status]
              : '-'}
          </>
        )
      }
    },
    {
      title: getWord('reference-report'),
      key: 'is_reference',
      dataIndex: 'is_reference',
      hideInSearch: !isAnalysisTab,
      hideInTable: isAnalysisTab,
      render: (_, item) => {
        return item?.is_reference ? getWord('yes') : getWord('no')
      }
    },
    {
      title: getWord('pages.experiment.label.operation'),
      key: 'other',
      dataIndex: 'other',
      valueType: 'option',
      render: (_, item) => {
        return (
          <>
            {item.report_url && (
              <Button
                type="link"
                size="small"
                disabled={!item.report_url}
                onClick={() =>
                  history.push(
                    `/projects/${projectId}/reaction/${reactionId}/experimental-procedure/conclusion/${
                      experimentalNo || item?.experiment_no
                    }/reportDetail/${item?.check_no}`
                  )
                }
              >
                {getWord('view-report')}
              </Button>
            )}
            <UploadButton
              editable={editable}
              analysis={item}
              requestList={getExperimentCheckList}
            />
          </>
        )
      }
    }
  ]

  const getExperimentChekOperators = async () => {
    const res = await apiExperimentChekOperators({
      routeParams: `?experiment_no=`
    })
    if (parseResponseResult(res).ok) {
      return isValidArray(res?.data)
        ? res?.data
            .filter((e: string | null) => e !== null)
            .map((p: string) => ({ value: p, label: p }))
        : []
    }
  }

  const readOnlyColumns: ProColumns<ExperimentAnalysis>[] = [
    {
      title: getWord('test-sample-id'),
      key: 'check_no',
      dataIndex: 'check_no',
      valueType: 'text',
      hideInSearch: !isAnalysisTab
      /*  fieldProps: {
        labelCol: { span: 14 },
        wrapperCol: { span: 10 }
      } */
    },
    {
      title: getWord('pages.experiment.label.no'),
      key: 'experiment_no',
      dataIndex: 'experiment_no',
      hideInTable: !isAnalysisTab,
      hideInSearch: !isAnalysisTab
    },
    {
      title: getWord('test-method'),
      key: 'check_method',
      dataIndex: 'check_method',
      hideInSearch: !isAnalysisTab,
      valueEnum: {
        TLC: { text: 'TLC' },
        LCMS: { text: 'LCMS' },
        NMR: { text: 'NMR' }
      }
    },
    {
      title: getWord('test-type'),
      key: 'check_type',
      dataIndex: 'check_type',
      hideInSearch: !isAnalysisTab,
      valueEnum: {
        M: { text: getWord('intermediate-detection') },
        F: { text: getWord('product-detection') }
      }
    },
    {
      title: getWord('test-launched-time'),
      key: 'send_time',
      dataIndex: 'send_time',
      valueType: 'dateTime',
      hideInSearch: true
    },
    {
      title: getWord('test-sponsor'),
      key: 'operator',
      dataIndex: 'operator',
      hideInSearch: !isAnalysisTab,
      valueType: 'select',
      request: () => getExperimentChekOperators()
    }
  ]

  const { conclusion, loading, refetch } = useContext(ConclusionContext)
  const [showReferenceOnly, setShowReferenceOnly] = useState<boolean>(false)
  const { message, modal } = App.useApp()

  const update = async (
    analysis: ExperimentAnalysisUpdate,
    confirm: boolean = false
  ) => {
    if (confirm && analysis.status) {
      if (analysis.status === 'canceled') {
        modal.confirm({
          title: '确认取消该条分析记录？',
          onOk: () => update(analysis)
        })
        return true
      } else if (analysis.status === 'finished') {
        modal.confirm({
          title: '确认将该条分析记录的状态修改为已完成？',
          onOk: () => update(analysis)
        })
        return true
      }
    }
    const res = await apiUpdateExperimentAnalysis({ data: analysis })
    if (parseResponseResult(res).ok) {
      message.success('更新检测成功！')
      refetch?.()
      getExperimentCheckList()
      return true
    } else {
      message.error(parseResponseResult(res).msg)
      return false
    }
  }

  const editable = !conclusion?.conclusion_result
  const [searchParams] = useSearchParams()
  return (
    <Section
      title={getWord('pages.experiment.label.operation.detectRecord')}
      id="analysis"
      actionSlot={
        <Space direction="horizontal">
          {!isAnalysisTab ? (
            <Form.Item
              label={getWord('show-reference-report')}
              style={{ marginBottom: 0 }}
            >
              <Switch
                onChange={(checked) => setShowReferenceOnly(checked)}
                checked={showReferenceOnly}
              />
            </Form.Item>
          ) : (
            ''
          )}
          {(!window.location.href.includes(
            'experimental-procedure/conclusion'
          ) ||
            (conclusion?.experiment_no && editable)) && (
            <CreateAnalysisModel experimentNo={conclusion?.experiment_no} />
          )}
        </Space>
      }
      className={styles.analysisTab}
    >
      <ProTable<ExperimentAnalysis>
        form={{
          initialValues: {
            experiment_no: searchParams.get('experimentNo')
          }
        }}
        dataSource={
          isValidArray(analysisData)
            ? analysisData.filter(
                (r) =>
                  r.status !== 'canceled' &&
                  (!showReferenceOnly || !!r.is_reference)
              )
            : []
        }
        loading={loading}
        columns={[
          ...readOnlyColumns,
          ...editColumns(!loading && editable, update)
        ]}
        search={!!isAnalysisTab ? { labelWidth: isEN() ? 148 : 120 } : false}
        request={isAnalysisTab ? requestEvent : false}
        options={false}
        rowKey="check_no"
        pagination={false}
        scroll={{ y: 350 }}
      />
    </Section>
  )
}
export default AnalysisRecord
