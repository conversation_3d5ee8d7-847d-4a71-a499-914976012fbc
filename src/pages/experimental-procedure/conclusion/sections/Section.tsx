import { Typography } from 'antd'
import React, { ReactElement } from 'react'
import '../index.less'

export interface SectionProps {
  title: string
  id?: string
  actionSlot?: ReactElement
  className?: string
}

const Section: React.FC<SectionProps> = ({
  title,
  id,
  children,
  actionSlot,
  className
}) => {
  return (
    <div id={id} className="section-wrapper" className={className}>
      <div className="section-title">
        <Typography.Title level={4}>{title}</Typography.Title>
        <div className="section-action">{actionSlot}</div>
      </div>
      {children}
    </div>
  )
}

export default Section
