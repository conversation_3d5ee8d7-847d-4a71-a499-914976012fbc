import { parseResponseResult } from '@/services'
import { apiUpdateExperimentConclusionResult } from '@/services/experiment-conclution'
import { ExperimentConclusionResult } from '@/services/experiment-conclution/index.d'
import { getWord } from '@/utils'

import { App, Radio, RadioChangeEvent, Space, Typography } from 'antd'
import React, { useContext } from 'react'
import { ConclusionContext } from '../ConclusionContext'
import Section from './Section'

const Conclusion: React.FC = ({}) => {
  const { conclusion, refetch, loading } = useContext(ConclusionContext)
  const { modal, message } = App.useApp()

  const update = async (
    experimentNo: string,
    result: ExperimentConclusionResult
  ) => {
    const res = parseResponseResult(
      await apiUpdateExperimentConclusionResult({
        data: { experiment_no: experimentNo, conclusion_result: result }
      })
    )
    if (res.ok) {
      message.success('实验结果确认成功')
      await refetch?.()
    } else {
      message.error(res.msg)
    }
  }

  const onChange = (event: RadioChangeEvent) => {
    event.preventDefault()
    if (!conclusion?.experiment_no) {
      return
    }
    const result = event.target.value as ExperimentConclusionResult
    modal.confirm({
      onOk: () => update(conclusion.experiment_no, result),
      title: '确认实验结果',
      content: (
        <Space>
          确认设置实验结果为
          <Typography.Text strong>
            {getWord(`app.general.message.${result}`)}
          </Typography.Text>
          吗，确认后实验记录不可编辑！
        </Space>
      )
    })
  }

  return (
    <Section
      id="conclusion"
      title={getWord('menu.list.project-list.detail.experiment-conclusion')}
    >
      {conclusion?.auditor || conclusion?.conclusion_result ? (
        getWord(`app.general.message.${conclusion.conclusion_result}`)
      ) : (
        <Radio.Group
          onChange={onChange}
          disabled={loading}
          value={conclusion?.conclusion_result}
        >
          <Radio value="success">
            {getWord('app.general.message.success')}
          </Radio>
          <Radio value="failed">
            {getWord('component.notification.statusValue.failed')}
          </Radio>
        </Radio.Group>
      )}
    </Section>
  )
}

export default Conclusion
