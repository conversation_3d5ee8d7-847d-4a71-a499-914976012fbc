import useOptions from '@/hooks/useOptions'
import { TaskParam } from '@/services/experiment-exception/index.d'
import type { ProColumns } from '@ant-design/pro-components'
import { EditableProTable } from '@ant-design/pro-components'
import React, { useEffect, useState } from 'react'
import styles from './index.less'

export interface TaskParamsProps {
  params: TaskParam[]
  editMode?: boolean
  getParamsEvent?: { getter?: (params: TaskParam[]) => Promise<void> }
}

const TaskParams: React.FC<TaskParamsProps> = ({
  params,
  editMode,
  getParamsEvent
}) => {
  const { editableConfig } = useOptions()
  const [dataSource, setDataSource] = useState<TaskParam[]>(() => params)
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>()

  useEffect(() => {
    if (editMode) {
      setEditableRowKeys(dataSource.map((item) => item.name))
    } else setEditableRowKeys([])
  }, [editMode])

  useEffect(() => {
    getParamsEvent?.getter?.(dataSource)
  }, [getParamsEvent])

  const columns: ProColumns<TaskParam>[] = [
    {
      title: '任务参数',
      dataIndex: 'name',
      readonly: true
    },
    {
      title: '初始值',
      dataIndex: 'value',
      readonly: true
    },
    {
      title: '修正值',
      dataIndex: 'amend_value',
      valueType: 'text',
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '此项是必填项'
          }
        ]
      }
    }
  ]

  return (
    <EditableProTable<TaskParam>
      rowKey="name"
      loading={false}
      columns={columns}
      value={dataSource}
      className={styles.paramsTable}
      editable={{
        ...editableConfig,
        type: 'multiple',
        editableKeys,
        onValuesChange: (_record, recordList) => {
          setDataSource(recordList)
        },
        onChange: setEditableRowKeys
      }}
      recordCreatorProps={false}
    />
  )
}

export default TaskParams
