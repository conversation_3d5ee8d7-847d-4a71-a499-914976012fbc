import ButtonWithLoading from '@/components/ButtonWithLoading'
import { parseResponseResult } from '@/services'
import { apiUpdateExperimentException } from '@/services/experiment-exception'
import {
  TaskExceptionItem,
  TaskParam
} from '@/services/experiment-exception/index.d'
import { getWord, isEN } from '@/utils'
import {
  ProDescriptions,
  ProForm,
  ProFormSelect,
  ProFormText
} from '@ant-design/pro-components'
import { App, Button, Card, Col, Form, Row, Space } from 'antd'
import classNames from 'classnames'
import { pick } from 'lodash'
import React, { useContext, useEffect, useState } from 'react'

import { ConclusionContext } from '../../ConclusionContext'
import '../../index.less'
import {
  useExceptionHandleMethods,
  useExceptionReasonType
} from '../../useException'
import TaskParams from './TaskParams'

export interface ExceptionProps {
  exception: TaskExceptionItem
  onUpdateSuccess?: () => void
}

const Exception: React.FC<ExceptionProps> = ({
  exception,
  onUpdateSuccess
}) => {
  const [form] = Form.useForm<TaskExceptionItem['task_exception']>()
  const { message, notification } = App.useApp()
  const { conclusion } = useContext(ConclusionContext)
  const { codeToName } = useExceptionHandleMethods()
  const [reasonOptions, reasonValueEnums] = useExceptionReasonType()
  const editable = !(conclusion?.conclusion_result || conclusion?.auditor)

  useEffect(() => {
    form.setFieldsValue(exception.task_exception)
  }, [exception.task_exception])

  const [editMode, setEditMode] = useState<boolean>(false)
  const [getParamsEvent, setGetParamsEvent] = useState<{
    getter?: (params: TaskParam[]) => Promise<void>
  }>({})
  const existTaskParams = !!exception.task_params?.length

  const onSubmit = async (params?: TaskParam[]) => {
    const data = {
      exception_id: exception.exception_id,
      task_params: params,
      task_exception: pick(form.getFieldsValue(), 'reason_type', 'reason')
    }
    const res = parseResponseResult(
      await apiUpdateExperimentException({ data })
    )
    setEditMode(false)
    if (res.ok) {
      message.success('补填成功')
      onUpdateSuccess?.()
      return true
    }
    notification.error({ description: res.msg, message: '补填失败' })
    return false
  }

  const viewContent = (
    <ProDescriptions
      className={classNames('exception-view-wrapper', {
        'full-width': !existTaskParams
      })}
      column={existTaskParams ? 1 : 2}
      dataSource={exception.task_exception}
      columns={[
        {
          title: getWord('reason-type'),
          key: 'reason_type',
          dataIndex: 'reason_type',
          valueType: 'select',
          valueEnum: reasonValueEnums
        },
        {
          title: getWord('reason'),
          key: 'reason',
          dataIndex: 'reason',
          valueType: 'text'
        }
      ]}
    />
  )
  const editContent = (
    <ProForm
      form={form}
      layout="horizontal"
      grid
      labelCol={{ span: 6 }}
      validateMessages={{
        required: '${label}' + `${isEN() ? ' ' : ''}${getWord('is-required')}`
      }}
      submitter={false}
    >
      <ProFormSelect
        colProps={{ md: existTaskParams ? 24 : 12 }}
        name="reason_type"
        label={getWord('reason-type')}
        options={reasonOptions}
      />
      <ProFormText
        colProps={{ md: existTaskParams ? 24 : 12 }}
        name="reason"
        label={getWord('reason')}
      />
    </ProForm>
  )

  return (
    <Card
      size="small"
      title={
        codeToName[exception.task_exception?.exception_code || ''] ||
        exception.task_exception?.exception_code
      }
      extra={
        !editMode &&
        editable && (
          <Button type="default" size="small" onClick={() => setEditMode(true)}>
            补填
          </Button>
        )
      }
    >
      <Row>
        {existTaskParams && (
          <Col span={12}>
            <TaskParams
              params={exception.task_params || []}
              editMode={editMode}
              getParamsEvent={getParamsEvent}
            />
          </Col>
        )}
        <Col span={existTaskParams ? 11 : 24} offset={existTaskParams ? 1 : 0}>
          {editMode ? editContent : viewContent}
        </Col>
      </Row>
      {editMode && (
        <Row>
          <Space className="exception-actions-wrapper">
            <Button onClick={() => setEditMode(false)}>
              {getWord('pages.experiment.label.operation.cancel')}
            </Button>
            <ButtonWithLoading
              type="primary"
              onClick={async () =>
                existTaskParams
                  ? new Promise((resolve, reject) => {
                      setGetParamsEvent({
                        getter: async (params) => {
                          const result = await onSubmit(params)
                          return result ? resolve() : reject()
                        }
                      })
                    })
                  : onSubmit()
              }
            >
              {getWord('pages.route.edit.label.confirm')}
            </ButtonWithLoading>
          </Space>
        </Row>
      )}
    </Card>
  )
}

export default Exception
