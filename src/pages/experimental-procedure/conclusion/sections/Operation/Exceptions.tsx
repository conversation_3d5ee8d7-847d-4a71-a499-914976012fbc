import { apiExperimentException } from '@/services/experiment-exception'
import { TaskExceptionItem } from '@/services/experiment-exception/index.d'
import { Spin, Timeline, TimelineItemProps } from 'antd'
import dayjs from 'dayjs'
import { sortBy } from 'lodash'
import React, { useContext, useEffect, useState } from 'react'
import { ConclusionContext } from '../../ConclusionContext'
import Exception from './Exception'

export interface ExceptionsProps {
  taskNo: string
  experimentNo: string
}

const Exceptions: React.FC<ExceptionsProps> = ({ experimentNo, taskNo }) => {
  const [data, setData] = useState<TaskExceptionItem[]>([])
  const [loading, setLoading] = useState<boolean>(false)
  const { refetch } = useContext(ConclusionContext)

  const fetch = async (taskNo: string) => {
    setLoading(true)
    const res = await apiExperimentException({
      routeParams: `${experimentNo}/${taskNo}`
    })
    setLoading(false)
    setData(res?.data?.exceptions || [])
  }

  useEffect(() => {
    fetch(taskNo)
  }, [taskNo])

  const items: TimelineItemProps[] = sortBy(
    data,
    'task_exception.exception_time'
  ).map((d) => {
    return {
      label: dayjs(d.task_exception?.exception_time).format(
        'YYYY/MM/DD hh:mm:ss'
      ),
      children: (
        <Exception
          exception={d}
          onUpdateSuccess={() => {
            fetch(taskNo)
            refetch?.()
          }}
        />
      )
    }
  })

  return (
    <Spin spinning={loading}>
      <Timeline mode="left" items={items} />
    </Spin>
  )
}

export default Exceptions
