import { ExperimentOperation } from '@/services/experiment-conclution/index.d'
import { getWord } from '@/utils'
import { ProColumns, ProTable } from '@ant-design/pro-components'
import { Badge, Button, Space, Table } from 'antd'
import React, { useContext } from 'react'
import { ConclusionContext } from '../../ConclusionContext'
import Section from '../Section'
import Exceptions from './Exceptions'

const columns: ProColumns<ExperimentOperation>[] = [
  {
    title: getWord('pages.Notification.task'),
    dataIndex: 'task_name',
    key: 'task_name',
    valueType: 'text',
    render: (_, entity) => {
      return (
        <span>
          <Space direction="horizontal">
            {entity.task_name}
            {entity.need_amend ? <Badge dot status="error" /> : null}
          </Space>
        </span>
      )
    }
  },
  {
    title: getWord('pages.searchTable.titleDesc'),
    dataIndex: 'task_desc',
    key: 'task_desc',
    valueType: 'text'
  },
  {
    title: getWord('pages.searchTable.updateForm.schedulingPeriod.timeLabel'),
    dataIndex: 'start_time',
    key: 'start_time',
    valueType: 'dateTime'
  },
  {
    title: getWord('end-time'),
    dataIndex: 'end_time',
    key: 'end_time',
    valueType: 'dateTime'
  },
  Table.EXPAND_COLUMN
]

const Operation: React.FC = () => {
  const { conclusion, loading } = useContext(ConclusionContext)

  return (
    <Section title={getWord('task-list')} id="operations">
      <ProTable<ExperimentOperation>
        dataSource={conclusion?.operation_records}
        loading={loading}
        columns={columns}
        search={false}
        options={false}
        rowKey="task_id"
        expandable={{
          expandedRowRender: ({ task_no, experiment_no }) => (
            <Exceptions taskNo={task_no} experimentNo={experiment_no} />
          ),
          rowExpandable: (record) => !!record.error_count,
          expandIcon: ({ expanded, onExpand, record }) =>
            !!record.error_count && (
              <Button type="link" onClick={(e) => onExpand(record, e)}>
                {expanded
                  ? getWord('component.tagSelect.collapse')
                  : getWord('pages.projectTable.actionLabel.viewDetail')}
                异常记录
              </Button>
            )
        }}
        pagination={false}
      />
    </Section>
  )
}

export default Operation
