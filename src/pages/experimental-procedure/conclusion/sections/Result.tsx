import ButtonWithLoading from '@/components/ButtonWithLoading'
import { parseResponseResult } from '@/services'
import { apiUpdateExperimentConclusion } from '@/services/experiment-conclution'
import { ExperimentConclusion } from '@/services/experiment-conclution/index.d'
import { getWord, isEN } from '@/utils'
import {
  ProDescriptions,
  ProForm,
  ProFormDigit,
  ProFormGroup,
  ProFormSelect,
  ProFormText,
  ProFormTextArea
} from '@ant-design/pro-components'

import { App, Button, Form, Row, Space } from 'antd'
import React, { useContext, useEffect, useState } from 'react'
import { ConclusionContext } from '../ConclusionContext'
import '../index.less'
import Section from './Section'

const columns = [
  {
    title: getWord('yield'),
    key: 'experiment_yield',
    dataIndex: 'experiment_yield',
    valueType: 'percent'
  },
  {
    title: getWord('purity'),
    key: 'purity',
    dataIndex: 'purity',
    valueType: 'percent'
  },

  {
    title: `${getWord(
      'pages.experiment.conclusion.label.phase.solid'
    )}/${getWord('pages.experiment.conclusion.label.phase.liquid')}`,
    key: 'phase',
    dataIndex: 'phase',
    valueEnum: {
      solid: {
        text: getWord('pages.experiment.conclusion.label.phase.solid')
      },
      liquid: {
        text: getWord('pages.experiment.conclusion.label.phase.liquid')
      }
    }
  },
  {
    title: getWord('product-color'),
    key: 'color',
    dataIndex: 'color',
    valueType: 'text'
  },
  {
    title: getWord('other'),
    key: 'other',
    dataIndex: 'other',
    valueType: 'text'
  },
  {
    title: getWord('comment'),
    key: 'comment',
    dataIndex: 'comment',
    valueType: 'textarea',
    className: 'note-wrapper'
  }
]

const Result: React.FC = () => {
  const { conclusion, refetch, loading } = useContext(ConclusionContext)
  const [editMode, setEditMode] = useState<boolean>(false)
  const [form] = Form.useForm<ExperimentConclusion>()
  const { message } = App.useApp()

  useEffect(() => {
    form.setFieldsValue(conclusion?.conclusion || {})
  }, [conclusion?.conclusion])

  const onFinish = async (data: ExperimentConclusion) => {
    if (!conclusion?.experiment_no) return

    const res = parseResponseResult(
      await apiUpdateExperimentConclusion({
        data: { experiment_no: conclusion?.experiment_no, conclusion: data }
      })
    )
    if (res.ok) {
      message.success('实验结果保存成功')
      await refetch?.()
      setEditMode(false)
    } else {
      message.error(res.msg)
    }
  }

  if (!editMode) {
    return (
      <Section
        id="result"
        title={getWord('conclusion')}
        actionSlot={
          !conclusion?.conclusion_result ? (
            <Button
              type="default"
              size="small"
              onClick={() => setEditMode(true)}
            >
              {getWord('edit')}
            </Button>
          ) : undefined
        }
      >
        <ProDescriptions
          column={3}
          title={getWord('summary')}
          dataSource={conclusion?.conclusion}
          columns={columns.slice(0, 2)}
        />
        <ProDescriptions
          column={3}
          title={getWord('property')}
          dataSource={conclusion?.conclusion}
          columns={columns.slice(2)}
        />
      </Section>
    )
  }

  return (
    <Section id="result" title={getWord('conclusion')}>
      <ProForm<ExperimentConclusion>
        form={form}
        disabled={loading}
        layout="horizontal"
        grid
        labelCol={{ span: 6 }}
        validateMessages={{
          required: '${label}' + `${isEN() ? ' ' : ''}${getWord('is-required')}`
        }}
        onFinish={onFinish}
        submitter={false}
      >
        <ProFormGroup title={getWord('summary')}>
          <ProFormDigit
            colProps={{ md: 8 }}
            max={100}
            min={0}
            name="experiment_yield"
            label={getWord('yield')}
          />
          <ProFormDigit
            colProps={{ md: 8 }}
            max={100}
            min={0}
            name="purity"
            label={getWord('purity')}
          />
        </ProFormGroup>
        <ProFormGroup title={getWord('property')}>
          <ProFormSelect
            colProps={{ md: 8 }}
            name="phase"
            label={`${getWord(
              'pages.experiment.conclusion.label.phase.solid'
            )}/${getWord('pages.experiment.conclusion.label.phase.liquid')}`}
            valueEnum={{
              solid: {
                text: getWord('pages.experiment.conclusion.label.phase.solid')
              },
              liquid: {
                text: getWord('pages.experiment.conclusion.label.phase.liquid')
              }
            }}
          />
          <ProFormText
            colProps={{ md: 8 }}
            name="color"
            label={getWord('product-color')}
          />
          <ProFormText
            colProps={{ md: 8 }}
            name="other"
            label={getWord('other')}
          />
        </ProFormGroup>
        <ProFormTextArea
          labelCol={{ md: 2 }}
          colProps={{ md: 24 }}
          name="comment"
          label={getWord('comment')}
        />
      </ProForm>
      {editMode && (
        <Row>
          <Space className="result-actions-wrapper">
            <Button onClick={() => setEditMode(false)}>
              {getWord('pages.experiment.label.operation.cancel')}
            </Button>
            <ButtonWithLoading
              type="primary"
              onClick={() => onFinish(form.getFieldsValue())}
            >
              {getWord('pages.route.edit.label.confirm')}
            </ButtonWithLoading>
          </Space>
        </Row>
      )}
    </Section>
  )
}

export default Result
