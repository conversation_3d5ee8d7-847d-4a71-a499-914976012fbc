@import '@/style/variables.less';

.unfoldWidth {
  width: 100%;
  max-width: calc(
    100vw - @menu-width - @anchor-width - @scrollbar-width -
      @layout-horizontal-padding*2
  ) !important;
  iframe {
    width: 100%;
  }
}
.foldWidth {
  width: calc(
    100vw - @fold-menu-width - @anchor-width - @layout-horizontal-padding*2 -
      @scrollbar-width
  ) !important;
  iframe {
    width: 100%;
  }
}
.unfoldWidth_EN {
  width: 100%;
  max-width: calc(
    100vw - @menu-width - 170 - @scrollbar-width - @layout-horizontal-padding*2
  ) !important;
}
.foldWidth_EN {
  width: calc(
    100vw - @fold-menu-width - 170 - @layout-horizontal-padding*2 -
      @scrollbar-width
  ) !important;
}

.reportDetails {
  position: relative;
  display: flex;
  justify-content: space-between;
  padding-left: 10px;
  .content {
    flex: 1;
    width: 100%;
    padding-right: 5px;
    .pdfReview {
      iframe {
        height: 710px;
        overflow: auto;
      }
    }

    .actionProposalTip {
      margin: 6px 0;
      font-weight: bold;
      font-size: 16px;
    }

    .confirmButton {
      margin-top: 16px;
    }
  }

  .anchor {
    width: 120px;
  }

  .anchorEN {
    width: 170px;
  }
}
.stepInfo {
  display: flex;
  justify-content: space-between;
  width: 800px;
  background-color: #fff;
  .reactionDes {
    flex: 0 0 348px;
  }
  .space {
    flex: 0 0 20px;
  }
  .procedureDes {
    flex: 1;
  }

  .structure {
    width: auto;
    height: 120px;
  }

  .desTitle {
    color: rgba(0, 0, 0, 0.88);
    font-weight: 600;
    font-size: 16px;
    line-height: 1.5;
  }
}
