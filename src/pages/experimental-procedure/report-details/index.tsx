import CustomAnchor from '@/components/Anchor'
import LazySmileDrawer from '@/components/LazySmileDrawer'
import ProcedureText from '@/components/ProcedureText'
import { transformReferenceType } from '@/components/ReactionTabs/ReactionLibTab/util'
import {
  apiExperimentAnalysisReport,
  apiUploadAnalysisReport,
  parseResponseResult
} from '@/services'
import { ReportReference, ReportResponse } from '@/types/models'
import {
  convertToPercentage,
  getEnvConfig,
  getWord,
  isEN,
  isValidArray
} from '@/utils'
import { PageContainer, ProColumns, ProTable } from '@ant-design/pro-components'
import { useModel, useParams } from '@umijs/max'
import type { RadioChangeEvent } from 'antd'
import { Button, Popover, Radio, Space, Tag, message } from 'antd'
import cs from 'classnames'
import { isEmpty, isNil } from 'lodash'
import { useEffect, useState } from 'react'
import SectionTitle from '../../../components/SectionTitle/index'
import styles from './index.less'
export default function ReportDetails() {
  const { initialState } = useModel('@@initialState')
  const { check_no } = useParams()
  const [reportDetailData, setreportDetailData] = useState<ReportResponse>()
  const [selectedActionIndex, setSelectedActionIndex] = useState<number>(0)

  const getExperimentAnalysisReport = async () => {
    const res = await apiExperimentAnalysisReport({
      routeParams: check_no
    })
    if (parseResponseResult(res).ok) {
      setreportDetailData(res?.data)
    }
  }

  useEffect(() => {
    setSelectedActionIndex(
      reportDetailData?.report_analysis_result?.selected_action_index
    )
  }, [reportDetailData?.report_analysis_result?.selected_action_index])

  useEffect(() => {
    getExperimentAnalysisReport()
  }, [])

  const reportDetailsColumns: ProColumns<any>[] = [
    {
      title: getWord('structural'),
      width: 160,
      dataIndex: 'smiles',
      fixed: 'left',
      render: (smiles: string) =>
        smiles ? (
          <LazySmileDrawer structure={smiles} className="smilesItem" />
        ) : (
          ''
        )
    },
    {
      title: getWord('exact-mass'),
      width: 96,
      dataIndex: 'exact_mass'
    },
    {
      title: getWord('ultra-spectra'),
      width: 96,
      dataIndex: 'detected_map',
      render: (_, record) =>
        record?.detected_map?.chromatograph ? getWord('yes') : getWord('no')
    },
    {
      title: getWord('mass-spectra'),
      width: 96,
      dataIndex: 'detected_map',
      render: (_, record) =>
        record?.detected_map?.mass_spectrum ? getWord('yes') : getWord('no')
    },
    {
      title: getWord('role'),
      width: 96,
      dataIndex: 'role',
      valueEnum: {
        material: { text: getWord('menu.list.material-manage') },
        product: { text: getWord('product') },
        other: { text: getWord('other-and-impurity') },
        'by-product': { text: getWord('by-product') }
      }
    },
    {
      title: getWord('proportion-254'),
      width: 96,
      dataIndex: 'peak_area_ratio',
      render: (_, record) =>
        record?.peak_area_ratio['254']
          ? convertToPercentage(record?.peak_area_ratio['254'])
          : '-'
    },
    {
      title: getWord('proportion-214'),
      width: 96,
      dataIndex: 'peak_area_ratio',
      render: (_, record) =>
        record?.peak_area_ratio['214']
          ? convertToPercentage(record?.peak_area_ratio['214'])
          : '-'
    }
  ]

  const onChange = (e: RadioChangeEvent) =>
    setSelectedActionIndex(e.target.value)

  const stepsEnum = {
    continue: getWord('continue'),
    change_condition: getWord('try-another-procedure'),
    terminate: getWord('cancel-experiment')
  }
  const stepInfo = (reference: ReportReference) => {
    return (
      <div className={styles.stepInfo}>
        <div className={styles.reactionDes}>
          <span className={styles.desTitle}>{getWord('reaction')}</span>
          <LazySmileDrawer
            structure={reference?.rxn}
            className={styles.structure}
          />
        </div>
        <div className={styles.space} />
        <div className={styles.procedureDes}>
          {reference?.procedure && (
            <ProcedureText
              rows={6}
              procedure={{ text: reference?.procedure }}
            />
          )}
          <div>
            <Tag color="blue">
              {transformReferenceType(reference?.reference_type)}
            </Tag>
            {reference?.reference_text}
          </div>
        </div>
      </div>
    )
  }

  const confrimReport = async () => {
    let curOperateItem =
      reportDetailData?.report_analysis_result?.recommended_actions[
        selectedActionIndex
      ]
    let successMsg =
      !isNil(curOperateItem?.reference) && !isEmpty(curOperateItem?.reference)
        ? `${stepsEnum[curOperateItem.action_type]}${
            curOperateItem?.condition_index
          }`
        : stepsEnum[curOperateItem.action_type]
    const res = await apiUploadAnalysisReport({
      data: {
        check_no,
        report_analysis_result: {
          ...reportDetailData?.report_analysis_result,
          selected_action_index: selectedActionIndex
        }
      }
    })
    if (!parseResponseResult(res).ok) return
    message.success(`机器人会${successMsg}`)
    getExperimentAnalysisReport()
  }

  return (
    <PageContainer>
      <div className={styles.reportDetails}>
        <div
          className={cs(styles.content, {
            [styles['unfoldWidth']]: !initialState?.isMenuCollapsed,
            [styles['foldWidth']]: initialState?.isMenuCollapsed,
            [styles['unfoldWidth_EN']]:
              !initialState?.isMenuCollapsed && isEN(),
            [styles['foldWidth_EN']]: initialState?.isMenuCollapsed && isEN()
          })}
        >
          <SectionTitle word={getWord('report')} anchorId="report" />
          <div
            className={cs(styles.pdfReview, {
              [styles['unfoldWidth']]: !initialState?.isMenuCollapsed,
              [styles['foldWidth']]: initialState?.isMenuCollapsed
            })}
          >
            <iframe
              src={`${
                getEnvConfig().apiBase
              }/api/run/experiment/analysis/report/${check_no}.pdf`}
            />
          </div>
          <SectionTitle
            word={getWord('substance-inference')}
            anchorId="substanceInference"
            wrapClassName="mt20"
          />
          <ProTable
            rowKey="smiles"
            scroll={{ x: 650 }}
            search={false}
            columns={reportDetailsColumns}
            pagination={false}
            options={false}
            dataSource={reportDetailData?.report_analysis_result?.substances}
          />
          <SectionTitle
            word={getWord('ai-inference')}
            wrapClassName="mt20"
            anchorId="AIInference"
          />
          {getWord(reportDetailData?.report_analysis_result?.conclusion)}
          <SectionTitle
            anchorId="actionProposal"
            word={getWord('action-proposal')}
            wrapClassName="mt20"
          />
          <div className={styles.actionProposalTip}>
            {getWord('action-proposal-tip')}
          </div>
          <div>
            <Radio.Group onChange={onChange} value={selectedActionIndex}>
              <Space direction="vertical">
                {isValidArray(
                  reportDetailData?.report_analysis_result?.recommended_actions
                )
                  ? reportDetailData?.report_analysis_result?.recommended_actions?.map(
                      (item, index: number) => (
                        <Radio key={index} value={index}>
                          {!isNil(item?.reference) &&
                          !isEmpty(item?.reference) ? (
                            <Popover
                              placement="right"
                              content={stepInfo(item?.reference)}
                            >
                              {stepsEnum[item.action_type]}{' '}
                              {item?.condition_index}
                            </Popover>
                          ) : (
                            stepsEnum[item.action_type]
                          )}
                        </Radio>
                      )
                    )
                  : ''}
              </Space>
            </Radio.Group>
          </div>
          <Button
            type="primary"
            className={styles.confirmButton}
            onClick={confrimReport}
            disabled={
              reportDetailData?.report_analysis_result
                ?.selected_action_index !== 0
            }
          >
            {getWord('pages.route.edit.label.confirm')}
          </Button>
        </div>
        <div style={{ flex: isEN() ? '0 0 170px' : '0 0 120px' }}>
          <CustomAnchor
            wrapClassName={isEN() ? styles.anchorEN : styles.anchor}
            items={[
              {
                key: 'report',
                href: '#report',
                title: getWord('report')
              },
              {
                key: 'substanceInference',
                href: '#substanceInference',
                title: getWord('substance-inference')
              },
              {
                key: 'AIInference',
                href: '#AIInference',
                title: getWord('ai-inference')
              },
              {
                key: 'action-proposal',
                href: '#actionProposal',
                title: getWord('action-proposal')
              }
            ]}
          />
        </div>
      </div>
    </PageContainer>
  )
}
