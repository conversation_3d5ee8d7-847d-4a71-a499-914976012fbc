import { ReactComponent as CreateSvg } from '@/assets/svgs/create.svg'
import { ReactComponent as HelpIcon } from '@/assets/svgs/help.svg'
import { ReactComponent as MaterialSvg } from '@/assets/svgs/material.svg'
import { ReactComponent as PublishSvg } from '@/assets/svgs/publish.svg'
import { ReactComponent as SaveSvg } from '@/assets/svgs/save.svg'
import BpmnEditor from '@/components/BpmnEditor'
import LazySmileDrawer from '@/components/LazySmileDrawer'
import MaterialsTable from '@/components/MaterialsTable'
import type { Materials } from '@/components/MaterialsTable/index.d'
import EditExperimentModal from '@/components/ReactionTabs/EditExperimentModal'
import {
  apiExperimentDesignDetail,
  apiGenerateProcedure,
  apiGenerateWorkflow,
  apiPublishExperimentDesigns,
  apiUpdateExperimentDesigns,
  apiUpdateExperimentDesignsMaterial,
  parseResponseResult
} from '@/services'
import type { MaterialTable } from '@/services/brain'
import { apiAddReactionLib } from '@/services/experiment-exception'
import { decodeUrl, encodeString, getWord, toInt } from '@/utils'
import { BookOutlined } from '@ant-design/icons'
import { PageContainer } from '@ant-design/pro-components'
import type {
  DesignStatus,
  ExperimentDesignResponseData,
  ExperimentDesignToFormattedProcedureResponse
} from '@types'

import {
  Button,
  Col,
  Input,
  Modal,
  Popover,
  Radio,
  Row,
  Space,
  message
} from 'antd'
import cs from 'classnames'
import { cloneDeep, isEmpty, isEqual } from 'lodash'
import { ReactNode, useEffect, useRef, useState } from 'react'
import { history, useModel, useParams, useSearchParams } from 'umi'
import xss from 'xss'
import type { IRouteParams, OperateType } from './index.d'
import styles from './index.less'
type ModeType = 'traditional' | 'auto'
let timer: NodeJS.Timeout

const experimentActions = ['newExperiment'] as const
type ExperimentAction = (typeof experimentActions)[number]
type ExperimentDesignStatus = 'published'
const statusActionMap: Record<ExperimentDesignStatus, ExperimentAction[]> = {
  published: ['newExperiment']
}

export default function ExperimentPlanDetail() {
  const [hidePanel, setHidePanel] = useState<boolean>(false)
  const [experimentName, setExperimentName] = useState<string>()
  const [experimentMode, setExperimentMode] = useState<ModeType>('traditional')
  const [curType, setCurType] = useState<OperateType>(null)
  const [experimentPlanDetail, setExpErimentPlanDetail] =
    useState<ExperimentDesignResponseData | null>(null)

  const { experimentDesignId, projectId, reactionId } =
    useParams() as IRouteParams
  const { queryTaskList, taskList } = useModel('task')
  const isEditor: boolean = curType === 'editor'
  const [searchParams] = useSearchParams()
  const [generating, setGenerating] = useState(false)
  const getIcon: Record<ExperimentAction, ReactNode> = {
    newExperiment: <CreateSvg width={15} />
  }

  const saveExperimentDesigns = async (workflow: string) => {
    if (generating) return
    const res = await apiUpdateExperimentDesigns({
      data: {
        name: experimentName,
        id: JSON.parse(decodeUrl(experimentDesignId as string)),
        reference_text: experimentPlanDetail?.reference_text,
        workflow,
        mode: experimentMode
      }
    })
    if (
      parseResponseResult(res)?.ok &&
      !isEqual(
        JSON.parse(decodeUrl(experimentDesignId as string)),
        res?.data?.id
      )
    ) {
      history.replace(
        `/projects/${projectId}/reaction/${reactionId}/experimental-procedure/detail/${encodeString(
          JSON.stringify(res?.data?.id)
        )}?type=editor`
      )
    }
  }

  const queryRef = useRef(null)
  const handleSaveEvent = () =>
    queryRef?.current?.handleSave &&
    queryRef?.current?.handleSave(saveExperimentDesigns)
  const [material, setMaterial] = useState<MaterialTable[]>([])
  const autoSaveExperimentDesigns = () => {
    timer = setInterval(handleSaveEvent, 30000)
  }

  useEffect(() => {
    const type: OperateType = searchParams.get('type')
    setCurType(type)
    queryTaskList()
    if (experimentPlanDetail?.status !== 'published')
      autoSaveExperimentDesigns()
    return () => {
      clearTimeout(timer)
      setGenerating(false)
    }
  }, [])

  const getDetailInfo = async () => {
    let curId = JSON.parse(decodeUrl(experimentDesignId as string))
    const res = await apiExperimentDesignDetail({
      routeParams: curId
    })
    if (parseResponseResult(res).ok) {
      setExpErimentPlanDetail(res?.data)
      setMaterial(res?.data?.materials)
      setExperimentMode(res?.data?.mode)
    }
  }

  useEffect(() => {
    if (!experimentDesignId) return
    getDetailInfo()
  }, [experimentDesignId])

  const publishExperimentDesigns = async () => {
    const res: any = await apiPublishExperimentDesigns({
      routeParams: experimentPlanDetail?.experiment_design_no
    })
    if (parseResponseResult(res).ok) message.success('workflow 发布成功！')
  }

  const addReactionLib = async () => {
    let curExId = JSON.parse(decodeUrl(experimentDesignId as string))
    const res: any = await apiAddReactionLib({
      data: {
        id: toInt(curExId) as number,
        reference_text: experimentPlanDetail?.reference_text,
        formatted_text: experimentPlanDetail?.formatted_text,
        workflow: experimentPlanDetail?.workflow,
        mode: experimentMode
      }
    })
    if (parseResponseResult(res).ok) message.success(getWord('operate-success'))
  }

  //  TODO sprint10 https://c12ai.atlassian.net/browse/LAB-694
  const generateWorkflow = async () => {
    setGenerating(true)
    const res: any = await apiGenerateWorkflow({
      data: {
        procedure: experimentPlanDetail?.reference_text,
        experiment_design_no: experimentPlanDetail?.experiment_design_no,
        mode: experimentMode
      }
    })
    if (parseResponseResult(res).ok) {
      let newExperimentPlanDetail = cloneDeep(experimentPlanDetail)
      if (res?.data?.workflow) {
        newExperimentPlanDetail.workflow = res?.data?.workflow
      }
      setExpErimentPlanDetail(newExperimentPlanDetail)
      message.success(getWord('generate-process-success-message'))
      setGenerating(false)
    } else {
      setGenerating(false)
      message.error(getWord('generate-process-failed-message'))
    }
  }

  const generateProcedure = async (workflow: string) => {
    const res: ExperimentDesignToFormattedProcedureResponse =
      await apiGenerateProcedure({
        /* TODO 请求增加物料materials字段 */
        data: {
          workflow: workflow,
          experiment_design_no: experimentPlanDetail?.experiment_design_no
          // materials
        }
      })
    if (parseResponseResult(res).ok) {
      let _experimentPlanDetail = cloneDeep(experimentPlanDetail)
      if (res?.data?.formatted_text) {
        _experimentPlanDetail.formatted_text = res?.data?.formatted_text
      }
      setExpErimentPlanDetail(_experimentPlanDetail)
      message.success('转化成功！')
    }
  }

  const procedureTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    let _experimentPlanDetail: any = {
      ...experimentPlanDetail,
      reference_text: e.target.value
    }
    setExpErimentPlanDetail(_experimentPlanDetail)
  }

  const [isModalOpen, setIsModalOpen] = useState(false)
  const plainOptions = [
    { label: getWord('traditional-mode'), value: 'traditional' },
    { label: getWord('auto-mode'), value: 'auto' }
  ]
  const handleMaterial = async () => {
    if (!isEmpty(material)) setIsModalOpen(true)
    else message.error('暂无物料数据，请稍后再试～')
  }
  const isAddKnowledgeBase: boolean =
    location.pathname.includes('knowledgeBase')

  const generateTypeChange = (type: ModeType) => setExperimentMode(type)
  useEffect(() => {
    setExperimentName(experimentPlanDetail?.name)
  }, [experimentPlanDetail?.name])

  const updateMaterial = async (newMaterials: Materials[]) => {
    const res = await apiUpdateExperimentDesignsMaterial({
      data: {
        id: JSON.parse(decodeUrl(experimentDesignId as string)),
        updated_materials: newMaterials
      }
    })
    if (!parseResponseResult(res).ok) return
    getDetailInfo()
    message.success('保存成功')
    setIsModalOpen(false)
  }

  return (
    <PageContainer className={cs(styles.experimentPlanDetailWrapper)}>
      <Modal
        title={getWord('material-sheet')}
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={false}
        centered
        width={1160}
      >
        <MaterialsTable
          material_table={material}
          updateMaterial={updateMaterial}
          enableAdd={['validated', 'created'].includes(
            experimentPlanDetail?.status as DesignStatus
          )}
        />
      </Modal>
      <Space className="layoutRighButtons" style={{ top: '8px' }}>
        <Button icon={<MaterialSvg width={15} />} onClick={handleMaterial}>
          {getWord('material-sheet')}
        </Button>
        {!isAddKnowledgeBase ? (
          <>
            <Button
              icon={<SaveSvg width={15} />}
              onClick={handleSaveEvent}
              disabled={generating}
              className={cs({ [styles['disabledType']]: generating })}
            >
              {getWord('pages.route.edit.label.save')}
            </Button>
            <Button
              icon={<PublishSvg width={15} />}
              onClick={() => publishExperimentDesigns()}
            >
              {getWord('publish')}
            </Button>
            {/*  <Button icon={<SimulateSvg width={15} />} onClick={simulateDesigns}>
              模拟实验
            </Button> */}
          </>
        ) : (
          ''
        )}
        {isAddKnowledgeBase ? (
          <Button icon={<BookOutlined width={15} />} onClick={addReactionLib}>
            加入知识库
          </Button>
        ) : (
          ''
        )}
        {experimentPlanDetail?.status === 'published'
          ? statusActionMap[
              experimentPlanDetail?.status as ExperimentDesignStatus
            ]?.map((key) => {
              return (
                <EditExperimentModal
                  key={key}
                  materialTable={experimentPlanDetail?.materials}
                  projectId={toInt(experimentPlanDetail?.project_no as string)}
                  projectReactionId={experimentPlanDetail?.project_reaction_id}
                  experiementDesignNo={
                    experimentPlanDetail?.experiment_design_no
                  }
                  onSuccess={() => {
                    history.push(
                      `/projects/${experimentPlanDetail?.project_no}/reaction/${experimentPlanDetail?.project_reaction_id}?tab=my-experiment`
                    )
                  }}
                  triggerCom={
                    <Button icon={getIcon[key]}>
                      {getWord(`pages.experimentDesign.label.${key}`)}
                    </Button>
                  }
                />
              )
            })
          : ''}
      </Space>
      <div
        className={cs(styles.experimentPlanDetail, {
          [styles['hidePanel']]: hidePanel,
          [styles['showPanel']]: !hidePanel
        })}
      >
        <div
          className={cs(styles.foldIcon, {
            [styles['hide']]: hidePanel
          })}
          onClick={() => setHidePanel(!hidePanel)}
        />
        <Row className={cs('flex-align-items-center', styles.experimentTitle)}>
          <span>
            {getWord('pages.experiment.label.experimentDesignName')}：
          </span>
          <div className="flex-align-items-center">
            <Input
              value={experimentName}
              style={{ width: '228px' }}
              maxLength={30}
              onChange={(e) => setExperimentName(e.target.value)}
            />
          </div>
        </Row>
        <Row className={styles.content}>
          <Col span={8}>
            {getWord('reaction')}
            {experimentPlanDetail?.rxn ? (
              <LazySmileDrawer
                structure={experimentPlanDetail?.rxn}
                className={styles.experimentStructure}
              />
            ) : (
              <>
                <br />
                {getWord('noticeIcon.empty')}～
              </>
            )}
          </Col>
          <Col span={8}>
            <div
              className={cs(styles.commonTitle, 'flex-justify-space-between')}
              style={{ paddingRight: '15px' }}
            >
              <span>Procedure</span>
              {isEditor && (
                <div className={styles.generateContent}>
                  <Popover
                    placement="bottomRight"
                    align={{
                      offset: [10, 12]
                    }}
                    content={
                      <>
                        <div
                          dangerouslySetInnerHTML={{
                            __html:
                              '自动模式涉及的任务包括投料、反应,<br/>若procedure的条件与自动模式有冲<br/>突,则冲突部分任务切换回传统模式'
                          }}
                        ></div>
                      </>
                    }
                  >
                    <HelpIcon width={20} className="enablePointer" />
                  </Popover>
                  &nbsp;&nbsp;
                  <Radio.Group
                    options={plainOptions}
                    onChange={({ target: { value } }) =>
                      generateTypeChange(value)
                    }
                    value={experimentMode}
                    optionType="button"
                    size="small"
                  />
                  &nbsp;&nbsp;
                  <Button
                    type="primary"
                    size="small"
                    onClick={generateWorkflow}
                    disabled={generating}
                  >
                    {generating
                      ? getWord('switching')
                      : getWord('switch-to-workflow')}
                  </Button>
                </div>
              )}
            </div>
            <div
              className={styles.procedureText}
              style={{ overflow: 'hidden' }}
            >
              <Input.TextArea
                placeholder="请编写procedure"
                autoSize={{ minRows: 6, maxRows: 6 }}
                bordered={false}
                style={{ maxHeight: '146px', marginBottom: 24, padding: 0 }}
                onChange={procedureTextChange}
                value={experimentPlanDetail?.reference_text}
              />
            </div>
          </Col>
          <Col span={8}>
            <div
              className={cs(styles.commonTitle, 'flex-justify-space-between')}
              style={{ paddingRight: '15px' }}
            >
              <span>{getWord('formulated-procedure')}</span>
              {isEditor && (
                <Button
                  type="primary"
                  size="small"
                  onClick={() =>
                    queryRef?.current?.handleSave &&
                    queryRef?.current?.handleSave(generateProcedure)
                  }
                >
                  {getWord('generate')}
                </Button>
              )}
            </div>
            <div
              className={styles.procedureText}
              dangerouslySetInnerHTML={{
                __html: experimentPlanDetail?.formatted_text
                  ? xss(experimentPlanDetail?.formatted_text)
                  : experimentPlanDetail?.formatted_text
              }}
            />
          </Col>
        </Row>
      </div>
      <div>
        {isEmpty(taskList) ? (
          'loading...'
        ) : (
          <BpmnEditor
            ref={queryRef}
            panelDatas={{
              experimentDesignNo:
                experimentPlanDetail?.experiment_design_no as string,
              workflow: experimentPlanDetail?.workflow as string
            }}
            taskList={taskList}
          />
        )}
      </div>
    </PageContainer>
  )
}
