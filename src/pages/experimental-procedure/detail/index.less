@import '@/style/variables.less';
.experimentPlanDetailWrapper {
  :global {
    .ant-page-header {
      background-color: #fff;
    }
  }
  position: relative;
  min-height: @main-content-height;
  .experimentPlanDetail {
    height: 228px;
    margin-top: 8px;
    margin-bottom: 20px;
    overflow: hidden;
    background-color: @fill-bg-light;
    .experimentTitle {
      height: 36px;
      padding-left: 24px;
    }
    .content {
      padding: 8px 24px 8px 24px;
    }
    .experimentStructure {
      width: auto; // 100%
      height: auto !important;
      max-height: 136px !important;
      margin-top: 18px;
      margin-right: 10px;
      overflow: hidden;
      border: 1px solid @border-color-base;
    }
    .commonTitle {
      height: 22px;
      color: @color-text-a;
      font-weight: 400;
      font-size: 16px;
      line-height: 22px;
    }
    .generateContent {
      display: flex;
      align-items: center;
      height: 24px;
      :global {
        .ant-radio-group {
          min-width: 144px !important;
        }
      }
    }
    .procedureText {
      height: 146px;
      margin: 8px 10px 0 0;
      margin-top: 8px;
      padding: 9px 14px;
      overflow-y: scroll;
      color: @color-text-secondary;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      background: @fill-bg-textarea;
      border-radius: 8px;
    }
  }
  .foldIcon {
    position: absolute;
    top: 264px;
    right: calc(50% - 13px);
    z-index: 11;
    width: 26px;
    height: 26px;
    cursor: pointer;
    transition: all 0.5s ease-in-out;
  }
  .foldIcon::before {
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../../../assets/svgs/fold.svg') 0 0 no-repeat;
    transform: rotate(-180deg);
    transition: all 0.5s ease-in-out;
    content: '';
  }
  .hide {
    top: 28px;
    transition: all 0.5s ease-in-out;
  }
  .hide::before {
    background: url('../../../assets/svgs/fold.svg') 0 0 no-repeat;
    transform: rotate(-360deg);
    transition: all 0.5s ease-in-out;
  }
  .hidePanel {
    height: 0px;
    transition: all 0.5s ease-in-out;
  }
  .showPanel {
    transition: all 0.5s ease-in-out;
  }
}

.disabledType {
  svg {
    fill: @fill-disabled !important;
  }
}
