import { statusColor, statusDes } from '@/constants'
import { encodeString, getWord } from '@/utils'
import { Divider, Tag } from 'antd'
import type { ColumnsType } from 'antd/lib/table'
import { isEmpty } from 'lodash'
import { history } from 'umi'
import type { ExperimentalProcedureProps, IStatusCount } from './index.d'
/**
 * 计算 实验记录成功、失败数目
 */
/* TODO any -> ts interface */
const calcCount = (list: any) => {
  const acc: IStatusCount = {
    failureCount: 0,
    successCount: 0
  }
  if (!isEmpty(list)) {
    list.forEach((item: any) => {
      const { status } = item
      if (status === 'failed') acc.failureCount += 1
      else if (status === 'success') acc.successCount += 1
    })
  }
  return acc
}
export const columns: ColumnsType<ExperimentalProcedureProps> = [
  {
    title: getWord('reaction-ID'),
    dataIndex: 'experiment_design_no',
    align: 'left',
    width: 160
  },
  {
    title: getWord('creator'),
    dataIndex: 'creator',
    align: 'left',
    width: 120
  },
  {
    title: '实验流程名称',
    dataIndex: 'name',
    align: 'left',
    width: 160,
    render: (text, record: any) => {
      return (
        <a
          onClick={() =>
            history.push(
              `/experimental-procedure/detail/${encodeString(
                JSON.stringify(record?.id)
              )}`
            )
          }
        >
          {text}
        </a>
      )
    }
  },
  {
    title: '实验流程状态',
    dataIndex: 'status',
    align: 'left',
    width: 120,
    render: (text) => (
      <Tag color={statusColor[text] as string}>{statusDes[text]}</Tag>
    )
  },
  {
    title: getWord('experiment-log'),
    dataIndex: 'experiments',
    align: 'left',
    width: 150,
    render: (_, { experiments, status }) => {
      const { failureCount, successCount } = calcCount(experiments)
      return status !== 'created' ? (
        <>
          {getWord('app.general.message.success：')}
          <a>{successCount}个</a>
          <Divider type="vertical" />
          {getWord('component.notification.statusValue.failed')}
          失败：<a>{failureCount}个</a>
        </>
      ) : (
        '-'
      )
    }
  }
]
