import { getWord } from '@/utils'
/* 实验流程列表 */
/* NOTE 跳转入口：https://brain-prod.labwise.cn/target-molecule/11/route-edit/22 */
// import CustomButton from '@/components/CustomButton'
import CustomTable from '@/components/CustomTable'
import LazySmileDrawer from '@/components/LazySmileDrawer'
import SearchForm from '@/components/SearchForm'
import { EXPERIMENT_DESIGNS, initFilter } from '@/constants'
import useFetchData from '@/hooks/useFetchData'
import {
  apiDeleteExperimentDesigns,
  apiUpdateExperimentDesigns,
  parseResponseResult
} from '@/services'
import { decodeUrl, encodeString, isEN } from '@/utils'
import { PageContainer } from '@ant-design/pro-components'
import { Col, Popconfirm, Row, Space, message } from 'antd'
import cs from 'classnames'
import { isEmpty } from 'lodash'
import { useCallback, useEffect, useState } from 'react'
import { history, useDispatch, useSearchParams, useSelector } from 'umi'
import OperateModal from './OperateModal'
import { columns } from './column'
import { titleDes } from './enum'
import type { IOperateInfo } from './index.d'
import styles from './index.less'
import { queryData } from './query-config'
export default function ExperimentPlan() {
  const [queryParams, setQueryParams] = useState<any>(initFilter)
  const [refresh, setRefresh] = useState(false)
  const { loading, listData, total } = useFetchData(
    queryParams,
    EXPERIMENT_DESIGNS,
    refresh
  )
  const [openEvent, setOpenEvent] = useState<{ open?: boolean }>({})
  const [operateInfo, setOperateInfo] = useState<IOperateInfo>({
    operateType: 'create'
  })

  const tableConfig = {
    loading,
    bordered: true,
    dataSource: listData,
    pagination: {
      total,
      current: queryParams.page_no,
      pageSize: queryParams.page_size,
      showTotal: () => `共${total}条记录`,
      showQuickJumper: true,
      showSizeChanger: true
    }
  }
  const smiles = listData[0]?.rxn

  const [searchParams] = useSearchParams()
  useEffect(() => {
    let id = searchParams.get('rxn')
    if (!id) return
    setQueryParams({
      ...queryParams,
      rxn: id ? JSON.parse(decodeUrl(id)) : undefined
    })
  }, [])

  useEffect(() => {
    if (loading === false) setRefresh(false)
  }, [loading])

  const refreshRequest = () => setRefresh(true)

  const handleCancel = async (id: number) => {
    const res: any = await apiUpdateExperimentDesigns({
      data: {
        id,
        status: 'canceled'
      }
    })
    if (!parseResponseResult(res).ok) return
    message.success(getWord('operate-success'))
    refreshRequest()
  }

  const handleDelete = async (id: number) => {
    const res: any = await apiDeleteExperimentDesigns({
      routeParams: String(id)
    })
    if (parseResponseResult(res).ok) {
      message.success(getWord('operate-success'))
      refreshRequest()
    }
  }

  const operate = () => [
    {
      title: '操作详情',
      dataIndex: 'opreate',
      align: 'center',
      render: (
        _,
        {
          id,
          status,
          experiment_design_no,
          name
        }: {
          id: number
          status: string
          experiment_design_no?: string
          name: string
        }
      ) => {
        const isDraft = status === 'created'
        const isPublished = status === 'published'
        const isCanceled = status === 'canceled'
        return (
          /*
          可操作逻辑如下
          TODO request 编辑中（草稿）：编辑实验流程、删除实验流程
          TODO request 已发布：查看实验设计、复制实验流程、查看实验计划、作废实验流程
          TODO request 已作废：查看实验设计、复制实验流程、查看实验计划
          */
          <>
            <div className={styles.operateLine}>
              <Space size="small">
                {(!isPublished || isCanceled) && (
                  <a
                    onClick={() =>
                      history.push(
                        `/experiment/experiment-plan?experiment_design_no=${encodeString(
                          JSON.stringify(experiment_design_no)
                        )}`
                      )
                    }
                  >
                    查看实验计划
                  </a>
                )}
                {(isPublished || isCanceled) && (
                  <>
                    <a
                      onClick={() => {
                        setOperateInfo({
                          operateType: 'copy',
                          id
                        })
                        setOpenEvent({ open: true })
                      }}
                    >
                      {titleDes['copy']}
                    </a>
                  </>
                )}
                {isDraft && (
                  <>
                    <a
                      onClick={() =>
                        history.push(
                          `/experimental-procedure/detail/${encodeString(
                            JSON.stringify(id)
                          )}?type=editor`
                        )
                      }
                    >
                      编辑实验流程
                    </a>
                  </>
                )}
                {isDraft && (
                  <>
                    <Popconfirm
                      title={`请确认是否删除实验流程${name}?`}
                      onConfirm={() => handleDelete(id)}
                      okText={isEN() ? 'YES' : '是'}
                      cancelText={isEN() ? 'NO' : '否'}
                    >
                      <a>删除实验流程</a>
                    </Popconfirm>
                  </>
                )}
                {isPublished && (
                  <a onClick={() => handleCancel(id)}>作废实验流程</a>
                )}
              </Space>
            </div>
          </>
        )
      }
    }
  ]

  const dispatch = useDispatch()
  const getExperimentDesignList = async () => {
    await dispatch({ type: 'enum/queryExperimentDesignList' })
  }
  useEffect(() => {
    getExperimentDesignList()
  }, [])

  const enumState = useSelector((state) => state?.enum)
  const { experimentDesignList } = enumState
  const handleQueryData = useCallback(() => {
    if (!isEmpty(experimentDesignList)) {
      queryData.forEach((item) => {
        if (item?.key === 'experiment_design_no')
          item.enums = experimentDesignList
      })
    }
    return queryData
  }, [experimentDesignList])

  return (
    <PageContainer className={cs(styles.experimentalProcedure)}>
      <Row className={styles.queryContent}>
        <Col span={5}>
          {/* TODO style all button -> CustomButton */}
          {/* <Breadcrumb /> */}
          <div className={cs('display-flex', styles.smiles)}>
            {smiles && (
              <LazySmileDrawer
                structure={smiles}
                className={styles.structure}
              />
            )}
          </div>
        </Col>
        <Col span={19}>
          <SearchForm
            formData={handleQueryData()}
            onSubmit={(values: any) =>
              setQueryParams({ ...queryParams, ...values, pageNo: 1 })
            }
            onReset={() => setQueryParams(initFilter)}
            btnGroupsConfig={[
              {
                clickFn: () => {
                  setOperateInfo({
                    operateType: 'create'
                  })
                  setOpenEvent({ open: true })
                },
                text: `${titleDes['create']}`
              }
            ]}
          />
        </Col>
      </Row>
      <OperateModal
        operateInfo={operateInfo}
        openEvent={openEvent}
        smiles={smiles}
        refreshRequest={refreshRequest}
      />
      <CustomTable
        {...tableConfig}
        columns={[...columns, ...operate()]}
        rowKey="id"
        onChange={(current, pageSize) => {
          setQueryParams({
            ...queryParams,
            page_no: current,
            page_size: pageSize
          })
        }}
      />
    </PageContainer>
  )
}
