import type { IFormData } from '@/components/SearchForm/index.d'
import { getWord } from '@/utils'
export const queryData: IFormData[] = [
  {
    label: '实验流程名称',
    ctype: 'select',
    key: 'experiment_design_no',
    placeholder: getWord('select-tip'),
    XL: { col: 7, labelWidth: 10, wrapperWidth: 14 }
  },
  {
    label: '实验流程状态',
    ctype: 'select',
    key: 'status',
    enums: [
      {
        label: '草稿',
        value: 'created'
      },
      {
        label: '已发布',
        value: 'published'
      },
      {
        label: '已作废',
        value: 'canceled'
      }
    ],
    placeholder: getWord('select-tip'),
    XL: { col: 6, labelWidth: 12, wrapperWidth: 12 }
  }
]
