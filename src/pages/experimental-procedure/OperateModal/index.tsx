import LazySmileDrawer from '@/components/LazySmileDrawer'
import ModalBase from '@/components/ModalBase'
import {
  apiCopyExperimentDesign,
  apiCreateExperimentDesigns,
  parseResponseResult
} from '@/services'
import { getWord, isEN } from '@/utils'
import { Form, Input, message } from 'antd'
import { ReactElement } from 'react'

import { titleDes } from '../enum'
import styles from '../index.less'
import type { CreateModalProps, ICreateModalForm } from './index.d'
/* TODO style */
const OperateModal = ({
  openEvent,
  smiles,
  operateInfo,
  refreshRequest
}: CreateModalProps): ReactElement => {
  const [form] = Form.useForm<ICreateModalForm>()
  const { operateType } = operateInfo
  const onConfirm = async () => {
    const values = await form.validateFields()
    const isCreate = operateType === 'create'
    let res,
      params = {
        name: values?.name
      }
    if (isCreate) {
      res = await apiCreateExperimentDesigns({
        data: {
          ...params,
          rxn: smiles
        }
      })
    } else {
      res = await apiCopyExperimentDesign({
        data: {
          ...params,
          source_id: operateInfo?.id
        }
      })
    }
    if (parseResponseResult(res).ok) {
      message.success(
        `${
          isCreate
            ? getWord('pages.projectTable.statusChangeLabel.created')
            : getWord('pages.experiment.label.operation')
        }${isEN() ? ' ' : ''}${getWord(
          'pages.experiment.statusLabel.success'
        )}～`
      )
      refreshRequest()
    } else {
      throw 'request error'
    }
  }

  return (
    <ModalBase
      title={titleDes[operateType]}
      openEvent={openEvent}
      onConfirm={onConfirm}
      afterClose={() => form.resetFields()}
    >
      <Form
        name="job_form_modal"
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 16 }}
      >
        <Form.Item label="reaction">
          {smiles && (
            <LazySmileDrawer structure={smiles} className={styles.structure} />
          )}
        </Form.Item>
        <Form.Item
          label="实验流程名称"
          name="name"
          rules={[{ required: true }]}
        >
          <Input
            placeholder={getWord('experimental-procedure-name')}
            maxLength={15}
            showCount
            allowClear
          />
        </Form.Item>
      </Form>
    </ModalBase>
  )
}

export default OperateModal
