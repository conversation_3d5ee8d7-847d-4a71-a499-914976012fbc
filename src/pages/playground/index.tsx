import Launcher from '@/components/Launcher'
import SyntheticRoutes from '@/components/SyntheticRoutes'
import type { RouteType } from '@/types/Common'
import { getWord } from '@/utils'
import { PageContainer } from '@ant-design/pro-components'
import { Card } from 'antd'
import cs from 'classnames'
import { useModel } from 'umi'
import styles from './index.less'

export default function Playground() {
  const { updateRouteType, routeType, curAiRouteNum } = useModel('compound')
  const { showLauncher, isOpen, sendMessage } = useModel('commend')
  const routeOptions = {
    aiGenerated: { name: getWord('aiGenerated') },
    myRoutes: { name: getWord('myRoutes') },
    reaction: { name: getWord('reaction') }
  }

  const getTabName = (tab: RouteType): string => {
    return routeOptions[tab].name
    switch (tab) {
      case 'aiGenerated': {
        const backboneNumber = curAiRouteNum || ''
        if (Number.isInteger(backboneNumber)) {
          return `${routeOptions[tab].name} (${backboneNumber})`
        }
        return routeOptions[tab].name
      }
      case 'myRoutes':
        if (targetMolecule?.project_routes_number) {
          return `${routeOptions[tab].name} (${targetMolecule.project_routes_number})`
        }
        return routeOptions[tab].name
      default:
        return ''
    }
  }
  return (
    <PageContainer className={cs(styles.playground)}>
      <Launcher
        onMessageWasSent={sendMessage}
        hiddenLauncher={showLauncher}
        isOpen={isOpen}
        commendType={routeType === 'reaction' ? 'reaction' : 'route'}
      />
      <Card
        className="routes-list-wrapper"
        tabList={Object.entries(routeOptions).map(([key]) => ({
          key,
          tab: getTabName(key as RouteType)
        }))}
        activeTabKey={routeType}
        onTabChange={(key) => updateRouteType(key as RouteType)}
      >
        <SyntheticRoutes isPlayground={true} />
      </Card>
    </PageContainer>
  )
}
