import ButtonWithLoading from '@/components/ButtonWithLoading'
import {
  GetRouteEvent,
  RouteEditor,
  UpdateChildrenEvent
} from '@/components/RouteEditor'
import { useRouteTreeStore } from '@/components/RouteEditor/Flex/Tree/store'
import { useBrainFetch } from '@/hooks/useBrainFetch'
import { useUserSetting } from '@/hooks/useUserSetting'
import { getReactionFromRxn } from '@/pages/reaction/util'
import type { ProjectCompoundStatus, ProjectType } from '@/services/brain'
import { ProjectCompound, ProjectRoute, service } from '@/services/brain'
import { getWord, isEN, isReadonlyMolecule } from '@/utils'
import { PageContainer } from '@ant-design/pro-components'
import {
  history,
  useAccess,
  useModel,
  useParams,
  useSearchParams
} from '@umijs/max'
import { App, Space } from 'antd'
import { cloneDeep } from 'lodash'
import React, { ReactNode, useEffect, useState } from 'react'
import ExportRouteButton from '../components/ExportRouteButton'
import ReactionDrawer from '../components/ReactionDrawer'
import EditRoute from '../edit'
import { useReactionDrawer } from '../hooks/useReactionDrawer'
import { useRxnYieldMap } from '../useRxnYieldMap'
import {
  MainTreeForRoute,
  getAllRxnsFromTree,
  getDeepthMap,
  getIdOfReaction,
  getNameOfReaction,
  getNavigateConfig,
  getRxnFromReaction,
  selectProcedure
} from '../util'
import './index.less'

const ViewRoute: React.FC = ({}) => {
  const { cacheCurReactionStepNo } = useModel('commend')
  const { routeId: idStr = '' } = useParams<{ routeId: string }>()
  const [searchParams] = useSearchParams()
  const step = searchParams.get('step') || ''
  const id = Number.parseInt(idStr)
  const { notification } = App.useApp()
  const [projectStatus, setProjectStatus] = useState<ProjectType>()
  const [compoundStatus, setCompoundStatus] = useState<ProjectCompoundStatus>()
  const [mainTree, setMainTree] = useState<MainTreeForRoute>()
  const [curTree, setCurTree] = useState<MainTreeForRoute>()
  const [compoundId, setCompoundId] = useState<number>()
  const [projectId, setProjectId] = useState<number>()
  const [editMode, setEditMode] = useState<boolean>(false)
  const [route, setRoute] = useState<ProjectRoute>()
  const { fetch } = useBrainFetch()

  const setExportFileName = useRouteTreeStore((s) => s.setExportFileName)
  const [saveEvent, setSaveEvent] = useState<GetRouteEvent>({})
  const access = useAccess()
  const { setting } = useUserSetting()
  const [selectRxnEvent, setSelectRxnEvent] = useState<{ select?: string }>({})
  const [updateChildrenEvent, setUpdateChildrenEvent] =
    useState<UpdateChildrenEvent>({})
  const {
    onSelectReaction,
    selectedReaction,
    selectedNode,
    selectedMainReaction
  } = useReactionDrawer(curTree)
  const deepthMap = getDeepthMap(mainTree)
  const getStepName = (node?: MainTreeForRoute): string | ReactNode => {
    const deepth = node?.id && deepthMap.get(node.id)
    if (deepth) {
      let reaction_step_no = getNameOfReaction(deepth[0], deepth[1])
      cacheCurReactionStepNo(reaction_step_no)
      return `${getWord('reaction')} ${reaction_step_no}`
    }
    return getWord('reaction')
  }
  const { map, add } = useRxnYieldMap(getAllRxnsFromTree(mainTree), projectId)

  const onSelectProcedure = selectProcedure(
    setUpdateChildrenEvent,
    curTree,
    selectedReaction
  )

  const fetchProjectRoute = async (id: number) => {
    const { data, error } = await service<ProjectRoute>(`project-routes/${id}`)
      .select()
      .populateDeep([
        {
          path: 'project_compound',
          children: [{ key: 'project', fields: ['id'] }]
        }
      ])
      .get()
    if (error) {
      notification.error({ message: error.message })
    } else if (!data) {
      notification.error({ message: `Project route with id ${id} not found` })
    } else {
      return data as unknown as ProjectRoute
    }
    return null
  }

  const fetchCompound = async (
    id: number
  ): Promise<ProjectCompound | undefined> => {
    const { data } = await fetch(
      service<ProjectCompound>('project-compounds')
        .selectManyByID([id])
        .populateWith('project', ['id'])
        .get()
    )
    return data?.[0]
  }

  useEffect(() => {
    fetchProjectRoute(id).then((data) => {
      if (data) {
        setRoute(data)
        setMainTree(data.main_tree)
        setCompoundId(data.project_compound?.id)
        setExportFileName(
          `${getWord('Routes')}${isEN() ? ' ' : ''}${data.name || ''}`
        )
      }
    })
  }, [id])

  useEffect(() => {
    if (mainTree && step) {
      const id = getIdOfReaction(getReactionFromRxn(step), mainTree) || ''
      setSelectRxnEvent({ select: id })
    }
  }, [mainTree, step])

  useEffect(() => {
    if (compoundId) {
      fetchCompound(compoundId).then((data) => {
        setCompoundStatus(data?.status)
        const projectId = data?.project?.id
        const projectStatus = data?.project?.status as ProjectType
        setProjectStatus(projectStatus)
        if (projectId) {
          setProjectId(projectId)
        }
      })
    }
  }, [compoundId])

  if (Number.isNaN(id)) {
    history.push('/404')
    return <></>
  }

  if (editMode) {
    return (
      <EditRoute
        projectId={projectId}
        mainTree={cloneDeep(mainTree)}
        onCancel={() => {
          setEditMode(false)
          setSaveEvent({})
          onSelectReaction(false)
          setUpdateChildrenEvent({})
        }}
        retroBackboneId={id}
        compoundId={compoundId}
      />
    )
  }

  return (
    <PageContainer className="route-view-root">
      <div className="container">
        {mainTree && (
          <RouteEditor
            root={mainTree}
            getRouteEvent={saveEvent}
            editMode={false}
            onTreeChange={(t) => {
              setCurTree(t)
              if (selectedNode?.id) onSelectReaction(selectedNode.id, t)
            }}
            onSelectRxn={onSelectReaction}
            updateChildrenEvent={updateChildrenEvent}
            selectRxnEvent={selectRxnEvent}
            rxnYieldMap={setting.retro?.route_show_yields ? map : undefined}
            rightTopSlot={
              <Space size="middle">
                {access?.authCodeList?.includes(
                  'view-by-backbone.button.export'
                ) && <ExportRouteButton />}
                {access?.authCodeList?.includes(
                  'view-by-backbone.button.edit'
                ) &&
                !isReadonlyMolecule(projectStatus, compoundStatus) &&
                ['editing', 'confirmed'].includes(route?.status || '') ? (
                  <ButtonWithLoading
                    onClick={() => {
                      setSaveEvent({
                        getRoute: (route) => {
                          setMainTree(route)
                          setEditMode(true)
                          setSaveEvent({})
                        }
                      })
                    }}
                    size="small"
                    type="primary"
                  >
                    {getWord('edit')}
                  </ButtonWithLoading>
                ) : (
                  ''
                )}
              </Space>
            }
          />
        )}
      </div>

      <ReactionDrawer
        projectId={projectId}
        reaction={selectedReaction}
        title={getStepName(selectedNode)}
        onClose={() => setSelectRxnEvent({})}
        mainReaction={selectedMainReaction}
        onUpdate={() =>
          selectedReaction && add(getRxnFromReaction(selectedReaction))
        }
        onSelectProcedure={onSelectProcedure}
        navigateConfig={
          mainTree &&
          selectedNode &&
          getNavigateConfig(mainTree, selectedNode, (node) => {
            onSelectReaction(node.id)
            setSelectRxnEvent({ select: node.id })
          })
        }
      />
    </PageContainer>
  )
}

export default ViewRoute
