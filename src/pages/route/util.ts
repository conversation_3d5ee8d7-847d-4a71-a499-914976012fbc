import {
  fetchEffectiveProcedureIds,
  fetchProcedures
} from '@/components/ReactionTabs/util'
import { UpdateChildrenEvent } from '@/components/RouteEditor'
import {
  MainTree,
  MaterialTable,
  ProcedureRoleResponse,
  ProjectReaction,
  RetroReaction,
  query
} from '@/services/brain'
import { isEqual, pickBy, sortBy } from 'lodash'
import { v4 } from 'uuid'
import { getReactionFromRxn } from '../reaction/util'

export interface MainTreeForRoute {
  id: string
  value: string
  parent?: string
  children?: MainTreeForRoute[]
  mainMaterial?: string
}

export interface Reaction {
  product: string
  reactants: string[]
}

export const normalizeMainTree = (
  tree: MainTree & { id?: string },
  parent?: string
): MainTreeForRoute => {
  const id = tree?.id || v4()
  return {
    value: tree.value,
    children: tree.children?.map((c) => normalizeMainTree(c, id)) || [],
    parent,
    id
  }
}

export const formatSmiles = (smiles: string): string => {
  return smiles.replace(/\./g, '~')
}

export const deFormatSmiles = (smiles: string): string => {
  return smiles.replace(/~/g, '.')
}

export const getRxnFromReaction = (
  { product, reactants }: Reaction,
  sort: boolean = false,
  format: boolean = false
) => {
  let reactantsStrs = sort ? reactants.sort() : reactants
  if (format) reactantsStrs = reactantsStrs.map(formatSmiles)
  return `${reactantsStrs.join('.')}>>${product}`
}

export const getReactionFromTree = (tree: MainTreeForRoute): Reaction => {
  return {
    product: tree.value,
    reactants: (tree.children || [])?.map((c) => c.value)
  }
}

export const getRxnFromTree = (
  tree: MainTreeForRoute,
  sort: boolean = false
): string => {
  return getRxnFromReaction(getReactionFromTree(tree), sort)
}

export const getRxnFromMaterialTable = (table: MaterialTable[]): string => {
  return getRxnFromReaction(
    table.reduce<Reaction>(
      (acc, cur) => {
        if (cur.role === 'product') acc.product = cur.smiles
        else acc.reactants.push(cur.smiles)
        return acc
      },
      { product: '', reactants: [] }
    )
  )
}

const getLeafNodeWithMultipleMaterial = (
  tree: MainTreeForRoute,
  leaves: MainTreeForRoute[] = []
): MainTreeForRoute[] => {
  if (tree.children?.length) {
    if (tree.children.every((n) => !n.children?.length)) {
      leaves.push(tree)
    } else {
      tree.children.forEach((n) => getLeafNodeWithMultipleMaterial(n, leaves))
    }
  }
  return leaves
}

export const findNodeById = (
  tree?: MainTreeForRoute,
  id?: string
): MainTreeForRoute | null => {
  if (!tree || !id) return null
  if (tree.id === id) return tree
  for (const child of tree.children || []) {
    const found = findNodeById(child, id)
    if (found) return found
  }
  return null
}

export const addMainMaterial = async (
  tree: MainTreeForRoute
): Promise<MainTreeForRoute> => {
  const leaves = getLeafNodeWithMultipleMaterial(tree)
  const rxnIdMap = leaves.reduce<Record<string, string>>((acc, cur) => {
    acc[getRxnFromTree(cur)] = cur.id
    return acc
  }, {})

  // const { role } = ((await query<ProcedureRolesResponse>('procedure/roles', {
  //   normalizeData: false,
  //   method: 'post',
  //   data: { reaction_smiles: Object.keys(rxnIdMap) }
  // }).get()) || {}) as unknown as ProcedureRolesResponse
  const res = ((await Promise.all(
    Object.keys(rxnIdMap).map((rxn) =>
      query<ProcedureRoleResponse>('procedure/role', {
        normalizeData: false,
        method: 'post',
        data: { reaction_smiles: rxn }
      }).get()
    )
  )) || {}) as unknown as ProcedureRoleResponse[]
  const role = res.map((r) => r.role)

  const rxnMainMaterialMap = Object.keys(rxnIdMap).reduce<
    Record<string, string>
  >((acc, cur, index) => {
    const mainReactants = Object.keys(
      pickBy(role[index], (v) => v === 'main_reactant')
    )
    acc[cur] = mainReactants[0]
    return acc
  }, {})

  Object.entries(rxnMainMaterialMap).forEach(([rxn, main]) => {
    const node = findNodeById(tree, rxnIdMap[rxn])
    if (node && main) {
      node.mainMaterial = main
    }
  })
  return tree
}

const getIdNodeMap = (
  tree: MainTreeForRoute,
  map: Map<string, MainTreeForRoute> = new Map()
) => {
  map.set(tree.id, tree)
  tree.children?.forEach((c) => getIdNodeMap(c, map))
  return map
}

const getAllLeafsWithDeepth = (
  tree: MainTreeForRoute,
  deepth: number = 0,
  brotherIndex: number = 0
): [MainTreeForRoute, number][] => {
  if (!tree.children?.length) {
    if (brotherIndex === 0) return [[tree, deepth]]
    return []
  }
  return tree.children.reduce<[MainTreeForRoute, number][]>(
    (acc, cur, index) =>
      acc.concat(getAllLeafsWithDeepth(cur, deepth + 1, index)),
    []
  )
}

export const getDeepthMap = <T = [number, number]>(
  tree?: MainTreeForRoute,
  valueParser: (pos: [number, number]) => T = (pos) => pos as T
): Map<string, T> => {
  const deepthMap = new Map<string, T>()
  if (!tree) {
    return deepthMap
  }

  const idNodeMap = getIdNodeMap(tree)
  const leafs = getAllLeafsWithDeepth(tree)
  leafs
    .sort((a, b) => (a[1] > b[1] ? -1 : 1))
    .forEach(([leaf], index) => {
      let cur: MainTreeForRoute | undefined = leaf
      let bottomUpOrder = 0
      while (cur) {
        if (deepthMap.get(cur.id)) break
        deepthMap.set(cur.id, valueParser([index, bottomUpOrder]))
        cur = cur.parent ? idNodeMap.get(cur.parent) : undefined
        bottomUpOrder++
      }
    })
  return deepthMap
}

export const getNameOfReaction = (
  routeOrder: number,
  bottomUpOrder: number
): string => {
  return `${(routeOrder + 10).toString(36).toUpperCase()}-${bottomUpOrder}`
}

export const getNameOfReactionMap = (
  tree?: MainTreeForRoute
): Map<string, string> => {
  return getDeepthMap(tree, (pos) => getNameOfReaction(pos[0], pos[1]))
}

export const getIdOfReaction = (
  { product, reactants }: Reaction,
  tree: MainTreeForRoute
): string | undefined => {
  if (
    tree.value === product &&
    isEqual(sortBy(tree.children?.map((t) => t.value)), sortBy(reactants))
  ) {
    return tree.id
  }
  for (const c of tree.children || []) {
    const id = getIdOfReaction({ product, reactants }, c)
    if (id) return id
  }
  return
}

export const getUpdateEvent = (
  tree: MainTreeForRoute,
  reaction: Reaction,
  reactants: string[]
): UpdateChildrenEvent => {
  const parentId = getIdOfReaction(reaction, tree)
  if (!parentId) return {}

  const idMap = getIdNodeMap(tree)
  const parent = idMap.get(parentId)
  if (!parent) return {}

  const children = reactants.map((r) => {
    const old = parent.children?.find((c) => c.value === r)
    return old || normalizeMainTree({ value: r }, parentId)
  })
  return { parent: parentId, children }
}
export default normalizeMainTree

export const getUpdateSelectedReactionWithRxnEvent = async (
  route: MainTreeForRoute,
  rxn: string,
  selectedReaction: Reaction
): Promise<UpdateChildrenEvent> => {
  const data = (await query<ProcedureRoleResponse>('procedure/role', {
    normalizeData: false,
    method: 'post',
    data: { reaction_smiles: rxn }
  }).get()) as any

  const reactants = Object.keys(
    pickBy(data.role, (v) =>
      ['main_reactant', 'reactant', 'other_reagent'].includes(v)
    )
  )
  return getUpdateEvent(route, selectedReaction, reactants)
}

export interface NavigateConfig {
  next?: () => void
  prev?: () => void
}

export const getNavigateConfig = (
  mainTree: MainTreeForRoute,
  node: MainTreeForRoute,
  updateNode: (node: MainTreeForRoute) => void
): NavigateConfig => {
  const parentNode = findNodeById(mainTree, node.parent)
  return {
    prev: parentNode ? () => updateNode(parentNode) : undefined,
    next: node?.children?.[0].children?.length
      ? () => updateNode(node.children?.[0] as MainTreeForRoute)
      : undefined
  }
}

const getExpandedReaction = async (
  root: MainTreeForRoute,
  projectId: number
): Promise<UpdateChildrenEvent> => {
  const reaction = getReactionFromTree(root)
  let expandedSmiles = getRxnFromReaction(reaction)

  const procedures = await fetchProcedures(
    await fetchEffectiveProcedureIds(reaction, {
      project: { id: projectId }
    } as ProjectReaction)
  )
  const smiles = procedures.map((p) => p.smiles)
  if (smiles.length) {
    expandedSmiles = smiles[smiles.length - 1]
  } else {
    const { data } = await query<RetroReaction>('retro-reactions/list', {
      method: 'post',
      data: { filters: reaction }
    })
      .sortBy([{ field: 'updatedAt', order: 'desc' }])
      .paginate(1, 1)
      .get()
    if (data?.length) {
      expandedSmiles = getRxnFromReaction(data[0])
    }
  }

  return getUpdateSelectedReactionWithRxnEvent(
    root,
    expandedSmiles,
    getReactionFromTree(root)
  )
}

export const expandRoute = async (
  root: MainTreeForRoute,
  projectId: number,
  update?: (event: UpdateChildrenEvent) => void
): Promise<void> => {
  if (!root.children?.length) return
  const event = await getExpandedReaction(root, projectId)
  update?.(event)
  await Promise.all(
    (root.children || [])?.map((child) => expandRoute(child, projectId, update))
  )
  update?.({ resetStack: true })
}

export const isReadonlyMaterialRole = (value: string): boolean =>
  ['main_reactant', 'reactant'].includes(value)

export const getAllRxnsFromTree = (tree?: MainTreeForRoute): string[] => {
  if (!tree) return []
  const nodes = getIdNodeMap(tree).values()
  return [...nodes].map((n) => getRxnFromTree(n, true))
}

export const selectProcedure = (
  setUpdateChildrenEvent: React.Dispatch<
    React.SetStateAction<UpdateChildrenEvent>
  >,
  route?: MainTreeForRoute,
  selectedReaction?: Reaction,
  onUpdated?: () => void
) => {
  return (rxn?: string): void => {
    if (!(rxn && route && selectedReaction)) {
      return
    }
    const event = getUpdateEvent(
      route,
      selectedReaction,
      getReactionFromRxn(rxn).reactants
    )
    setUpdateChildrenEvent(event)
    onUpdated?.()
  }
}
