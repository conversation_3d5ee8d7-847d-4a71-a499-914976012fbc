import { ProcedureRoleResponse, query, ReactionRole } from '@/services/brain'
import { useCallback, useState } from 'react'
import {
  findNodeById,
  getReactionFromTree,
  getRxnFromReaction,
  MainTreeForRoute,
  Reaction
} from '../util'

const getMainReactent = async (reaction: Reaction): Promise<string> => {
  const { role } = (await query<ProcedureRoleResponse>('procedure/roles', {
    normalizeData: false,
    method: 'post',
    data: { reaction_smiles: [getRxnFromReaction(reaction)] }
  }).get()) as unknown as { role: Record<string, ReactionRole>[] }
  const mainSmiles =
    Object.entries(role[0]).find(
      ([, value]) => value === 'main_reactant'
    )?.[0] || ''

  const { reactants } = reaction

  const mainReactant = reactants.find((r) => r === mainSmiles)
  if (mainReactant) return mainReactant

  const mainReactantContainMainSmiles = reactants.filter(
    (r) => r.includes(`.${mainSmiles}`) || r.includes(`${mainSmiles}.`)
  )
  if (mainReactantContainMainSmiles.length) {
    return mainReactantContainMainSmiles.sort((a, b) => b.length - a.length)[0]
  }
  return mainSmiles
}

const sameReaction = (r1?: Reaction, r2?: Reaction): boolean => {
  if (!r1 || !r2) return r1 === r2
  return getRxnFromReaction(r1, true) === getRxnFromReaction(r2, true)
}

export const useReactionDrawer = (initTree?: MainTreeForRoute) => {
  const [selectedReaction, setSelectedReaction] = useState<Reaction>()
  const [selectedMainReaction, setMainSelectedReaction] = useState<Reaction>()
  const [selectedNode, setSelectedNode] = useState<MainTreeForRoute>()
  const onSelectReaction = useCallback(
    async (id: string | false, latestTree?: MainTreeForRoute) => {
      const update = (r: Reaction) => {
        setMainSelectedReaction((prev) => {
          if (!sameReaction(prev, r)) {
            return r
          }
          return prev
        })
      }
      const tree = latestTree || initTree
      const productId = id && id.split(':')[0]
      const node = tree && productId && findNodeById(tree, productId)
      if (!node || !node.children?.length) {
        setSelectedReaction(undefined)
        setSelectedNode(undefined)
        setMainSelectedReaction(undefined)
        console.log()
        return
      }
      console.log()

      setSelectedNode(node)
      setSelectedReaction((prev) => {
        const newReaction = {
          product: node.value,
          reactants: node.children?.map((c) => c.value) || []
        }
        if (!sameReaction(prev, newReaction)) {
          return newReaction
        }
        return prev
      })

      const mids = node.children
        .filter((c) => !!c.children?.length)
        .map((c) => c.value)
      const materials = node.children
        .filter((c) => !c.children?.length)
        .map((c) => c.value)
      const mainReactants = mids.length
        ? mids
        : materials.length === 1
        ? materials
        : []
      if (mainReactants.length) {
        update({ product: node.value, reactants: mainReactants })
        return
      }

      const r = await getMainReactent(getReactionFromTree(node))
      if (r) update({ product: node.value, reactants: [r] })
    },
    [initTree]
  )

  return {
    onSelectReaction,
    selectedReaction,
    selectedNode,
    selectedMainReaction
  }
}
