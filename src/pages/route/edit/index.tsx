import ButtonWithLoading from '@/components/ButtonWithLoading'
import {
  GetRouteEvent,
  RouteEditor,
  UpdateChildrenEvent
} from '@/components/RouteEditor'
import { useRouteTreeStore } from '@/components/RouteEditor/Flex/Tree/store'
import { useUserSetting } from '@/hooks/useUserSetting'
import { ProjectCompound, ProjectRoute, service } from '@/services/brain'
import { getWord } from '@/utils'
import { LoadingOutlined } from '@ant-design/icons'
import { PageContainer } from '@ant-design/pro-components'
import { history, useModel, useParams } from '@umijs/max'
import { App, Button, Card, Col, Row, Space } from 'antd'
import React, { useCallback, useEffect, useState } from 'react'
import { useAccess } from 'umi'
import ExportRouteButton from '../components/ExportRouteButton'
import ReactionDrawer from '../components/ReactionDrawer'
import { useReactionDrawer } from '../hooks/useReactionDrawer'
import { useSaveRoute } from '../saveRoute'
import { useRxnYieldMap } from '../useRxnYieldMap'
import normalizeMainTree, {
  MainTreeForRoute,
  getAllRxnsFromTree,
  getDeepthMap,
  getNameOfReaction,
  getNavigateConfig,
  getRxnFromReaction,
  selectProcedure
} from '../util'
import './index.less'
interface EditRouteProps {
  mainTree?: MainTreeForRoute
  retroProcessId?: number
  compoundId?: number
  projectId?: number
  retroBackboneId?: number
  onCancel?: () => void
  routeId?: number
}

const EditRoute: React.FC<EditRouteProps> = ({
  mainTree: propMainTree,
  routeId,
  compoundId,
  projectId: propProjectId,
  retroBackboneId,
  retroProcessId,
  onCancel
}) => {
  const access = useAccess()
  const { cacheCurReactionStepNo } = useModel('commend')
  const { routeId: idStr = '' } = useParams<{ routeId: string }>()
  const [updateChildrenEvent, setUpdateChildrenEvent] =
    useState<UpdateChildrenEvent>({})
  const id = routeId || Number.parseInt(idStr)
  const [route, setExportFileName] = useRouteTreeStore((s) => [
    s.route,
    s.setExportFileName
  ])

  const { modal, notification } = App.useApp()
  const [mainTree, setMainTree] = useState<MainTreeForRoute | undefined>(
    propMainTree
  )
  const { setting } = useUserSetting()
  const [curTree, setCurTree] = useState<MainTreeForRoute>()

  const [projectId, setProjectId] = useState<number | undefined>(propProjectId)
  const [expandingId, setExpandingId] = useState<number | undefined>()
  const [expandingNodeId, setExpandingNodeId] = useState<string>()
  const [saveEvent, setSaveEvent] = useState<GetRouteEvent>({})
  const [selectRxnEvent, setSelectRxnEvent] = useState<{ select?: string }>({})
  const {
    onSelectReaction,
    selectedReaction,
    selectedNode,
    selectedMainReaction
  } = useReactionDrawer(curTree)
  const saveRouteMethod = useSaveRoute(projectId, compoundId, retroBackboneId)
  const deepthMap = route ? getDeepthMap(route) : new Map()
  const getStepName = (node?: MainTreeForRoute): string => {
    const deepth = node?.id && deepthMap.get(node.id)
    if (deepth) {
      let reaction_step_no = getNameOfReaction(deepth[0], deepth[1])
      cacheCurReactionStepNo(reaction_step_no)
      return `${getWord('reaction')} ${reaction_step_no}`
    }
    return getWord('reaction')
  }
  const { map, add } = useRxnYieldMap(getAllRxnsFromTree(mainTree), projectId)

  const onSelectProcedure = selectProcedure(
    setUpdateChildrenEvent,
    curTree,
    selectedReaction
  )

  const fetchProjectRoute = async (id: number) => {
    const { data, error } = await service<ProjectRoute>(`project-routes/${id}`)
      .select()
      .populateDeep([
        {
          path: 'project_compound',
          children: [{ key: 'project', fields: ['id'] }]
        }
      ])
      .populateWith('expanding_material', ['id', 'no'])
      .get()
    if (error) {
      notification.error({ message: error.message })
    } else if (!data) {
      notification.error({ message: `Project route with id ${id} not found` })
    } else {
      return data as unknown as ProjectRoute
    }
    return null
  }

  const saveRoute = useCallback(
    async (
      mainTree: MainTreeForRoute,
      action: 'save' | 'confirm' = 'save',
      notToRedirect: boolean = false
    ) => {
      return saveRouteMethod(
        mainTree,
        action,
        id,
        notToRedirect || !propMainTree
      )
    },
    [propMainTree, id]
  )

  const updateRoute = async (id: number) => {
    const route = await fetchProjectRoute(id)
    if (route) {
      setMainTree(route.main_tree)
      setProjectId(route.project_compound?.project?.id)
      setExpandingId(route.expanding_material?.id)
      setExpandingNodeId(route.expanding_material?.no)
      setExportFileName(`路线${route.name || ''}`)
    }
    return route
  }

  const onExpandMolecule = async (nodeId: string | false): Promise<void> => {
    if (!nodeId) return

    setSaveEvent({
      getRoute: async (route) => {
        if (!route) return
        const saved = await saveRoute(normalizeMainTree(route), 'save', true)
        if (!saved?.id) return
        await service<ProjectRoute>(`project-routes/retro-expand/${saved.id}`, {
          method: 'post',
          data: { expandId: nodeId }
        })
          .select()
          .get()
        const newRoute = await updateRoute(saved.id)
        if (newRoute?.expanding_material?.id) {
          window.open(
            `/projects/${projectId}/compound/${newRoute.expanding_material.id}`,
            '_blank'
          )
          if (propMainTree) {
            history.push(
              `/projects/${projectId}/compound/${compoundId}/edit/${saved.id}`
            )
          }
        }
      }
    })
  }

  const onCancelExpandMolecule = async () => {
    await service<ProjectRoute>('project-routes').update(id, {
      expanding_material: [] as unknown as ProjectCompound
    })
    updateRoute(id)
  }

  const cancelRoute = useCallback(() => {
    if (!onCancel) return
    setSaveEvent({
      getIsDirty: (dirty) => {
        if (dirty) {
          modal.confirm({
            title: `${getWord('pages.route.edit.label.confirm')} ${
              propMainTree
                ? getWord('pages.experiment.label.operation.cancel')
                : getWord('pages.searchTable.nameStatus.default')
            }`,
            content: getWord('unsaved-changes-tip'),
            onOk: () => onCancel(),
            onCancel: () => {}
          })
        } else {
          onCancel()
        }
      }
    })
  }, [onCancel])

  useEffect(() => {
    if (!propMainTree) {
      updateRoute(id)
    }
  }, [id, propMainTree])

  if (Number.isNaN(id) && !propMainTree) {
    history.push('/404')
    return <></>
  }

  const editableButtons = (
    <Space>
      {access?.authCodeList?.includes('view-by-backbone.button.export') && (
        <ExportRouteButton />
      )}
      {onCancel && (
        <Button size="small" onClick={cancelRoute}>
          {getWord('pages.experiment.label.operation.cancel')}
        </Button>
      )}
      {access?.authCodeList?.includes('view-by-backbone.saveRoute') && (
        <ButtonWithLoading
          onClick={async () => {
            return new Promise((resolve) => {
              setSaveEvent({
                getRoute: async (route) => {
                  if (!route) return
                  await saveRoute(normalizeMainTree(route))
                  resolve()
                }
              })
            })
          }}
          size="small"
        >
          {getWord('pages.route.edit.label.save')}
        </ButtonWithLoading>
      )}
      {access?.authCodeList?.includes('view-by-backbone.confirmRoute') && (
        <ButtonWithLoading
          onClick={async () => {
            return new Promise((resolve, reject) => {
              modal.confirm({
                title: getWord('confirm-the-route'),
                content: getWord('route-confirm'),
                onOk: () =>
                  setSaveEvent({
                    getRoute: async (route) => {
                      if (!route) return
                      await saveRoute(normalizeMainTree(route), 'confirm')
                      resolve()
                    }
                  }),
                onCancel: () => reject()
              })
            })
          }}
          size="small"
          type="primary"
        >
          {getWord('pages.route.edit.label.confirm')}
        </ButtonWithLoading>
      )}
    </Space>
  )

  const expandingButton = (
    <Card size="small">
      <Space>
        <LoadingOutlined style={{ color: '#1890FF' }} />
        {getWord('molecules-resynthesized')}
      </Space>
      <Row justify="end">
        <Col>
          <Button
            type="link"
            size="small"
            onClick={() =>
              window.open(
                `/projects/${projectId}/compound/${expandingId}`,
                '_blank'
              )
            }
          >
            {getWord('pages.projectTable.actionLabel.viewDetail')}
          </Button>
          <Button type="link" size="small" onClick={onCancelExpandMolecule}>
            {getWord('pages.experiment.label.operation.cancel')}
          </Button>
        </Col>
      </Row>
    </Card>
  )

  return (
    <PageContainer
      className="route-edit-root"
      title={getWord(`menu.list.route.edit`)}
    >
      <div className="container">
        {mainTree && (
          <RouteEditor
            root={mainTree}
            onTreeChange={(t) => {
              setCurTree(t)
              if (selectedNode?.id) onSelectReaction(selectedNode.id, t)
            }}
            getRouteEvent={saveEvent}
            expandingNodeId={expandingNodeId}
            updateChildrenEvent={updateChildrenEvent}
            onSelectRxn={onSelectReaction}
            onExpandMolecule={onExpandMolecule}
            editMode={!expandingId}
            selectRxnEvent={selectRxnEvent}
            rightTopSlot={expandingId ? expandingButton : editableButtons}
            rxnYieldMap={setting.retro?.route_show_yields ? map : undefined}
          />
        )}
      </div>

      <ReactionDrawer
        onSelectProcedure={expandingId ? undefined : onSelectProcedure}
        projectId={projectId}
        reaction={selectedReaction}
        title={getStepName(selectedNode)}
        onClose={() => setSelectRxnEvent({})}
        retroProcessId={retroProcessId}
        mainReaction={selectedMainReaction}
        onUpdate={() =>
          selectedReaction && add(getRxnFromReaction(selectedReaction))
        }
        navigateConfig={
          mainTree &&
          selectedNode &&
          getNavigateConfig(mainTree, selectedNode, (node) => {
            onSelectReaction(node.id)
            setSelectRxnEvent({ select: node.id })
          })
        }
      />
    </PageContainer>
  )
}

export default EditRoute
