import {
  fetchProcedures,
  fetchProceduresWithReaction
} from '@/components/ReactionTabs/util'
import {
  Procedure,
  ProcedureRxnBoneMatchResult,
  ProjectReaction,
  service
} from '@/services/brain'
import { groupBy, mapValues } from 'lodash'
import { useEffect, useState } from 'react'
import { getReactionFromRxn, matchResultToProcedure } from '../reaction/util'

const getRxnToMaxYield = (
  map: Record<string, Procedure[]>
): Record<string, number> => {
  return Object.keys(map).reduce<Record<string, number>>((acc, cur) => {
    acc[cur] = map[cur].length
      ? Math.max(...map[cur].map((p) => p.yields || 0))
      : 0
    return acc
  }, {})
}

const fetchEffectiveProcedureBatch = async (
  projectId: number,
  rxns: string[]
): Promise<Record<string, Procedure[]>> => {
  const rxnQueries = rxns.map((r) => r.replace(/\+/g, '%2B'))
  const { data } = await service<ProjectReaction>('project-reactions')
    .select(['effective_procedures', 'reaction'])
    .filterDeep('project.id', 'eq', projectId)
    .filterDeep('reaction', 'in', rxnQueries)
    .get()
  if (!data?.length) return {}
  const rxnToProcedureIds = data.reduce<Record<string, number[]>>(
    (acc, cur) => {
      acc[cur.reaction] = cur.effective_procedures || []
      return acc
    },
    {}
  )
  const ids = Object.values(rxnToProcedureIds).flat()
  const idProcedures = groupBy(await fetchProcedures(ids), (p) => p.id)
  return Object.keys(rxnToProcedureIds).reduce<Record<string, Procedure[]>>(
    (acc, cur) => {
      acc[cur] = rxnToProcedureIds[cur]
        .map((id) => idProcedures[id]?.[0] || null)
        .filter((p) => !!p)
      return acc
    },
    {}
  )
}

const fetchBoneMatchProcedureBatch = async (
  rxns: string[]
): Promise<Record<string, Procedure[]>> => {
  const res = await service<Procedure>('procedure/rxn-bone-match', {
    method: 'post',
    data: { rxns, has_yield: true, order_by_yield: false, top_k: 5 },
    normalizeData: false
  })
    .select()
    .get()
  return mapValues(
    (res as unknown as ProcedureRxnBoneMatchResult).matched_reference,
    (ps) => ps.map(matchResultToProcedure)
  )
}

export const useRxnYieldMap = (
  rxns: string[],
  projectId?: number
): { map: Record<string, number>; add: (rxn: string) => Promise<void> } => {
  const [map, setMap] = useState<Record<string, number>>({})

  useEffect(() => {
    let unmount = false
    if (!projectId) return
    fetchEffectiveProcedureBatch(projectId, rxns)
      .then((map) => {
        if (unmount) return {}
        const rxnsHaseffectiveProcedure = new Set(Object.keys(map))
        const newMap = getRxnToMaxYield(map)
        setMap((prev) => ({ ...prev, ...newMap }))
        return {
          lostRxns: rxns.filter((rxn) => !rxnsHaseffectiveProcedure.has(rxn)),
          map: getRxnToMaxYield(map)
        }
      })
      .then(async ({ lostRxns, map }) => {
        if (unmount || !lostRxns?.length) return { rxnToProcedures: {}, map }
        return {
          rxnToProcedures: await fetchBoneMatchProcedureBatch(rxns),
          map
        }
      })
      .then(({ rxnToProcedures, map }) => {
        const newMap = getRxnToMaxYield(rxnToProcedures)
        setMap((prev) => ({ ...prev, ...newMap, ...map }))
      })

    return () => {
      unmount = true
    }
  }, [projectId])

  const add = async (rxn: string) => {
    if (!projectId) return
    const procedures = await fetchProceduresWithReaction(
      getReactionFromRxn(rxn),
      { project: { id: projectId } } as ProjectReaction
    )
    const maxYields = Math.max(...procedures.map((p) => p.yields || 0))
    if (maxYields > 0) {
      setMap((m) => ({ ...m, [rxn]: maxYields }))
    }
  }

  return { map, add }
}
