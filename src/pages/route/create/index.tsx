import { ProjectCompound, service } from '@/services/brain'
import { history, useParams } from '@umijs/max'
import { App } from 'antd'
import React, { useEffect, useState } from 'react'
import { v4 } from 'uuid'
import EditRoute from '../edit'

const CreateRoute: React.FC = () => {
  const { compoundId: idStr = '' } = useParams<{ compoundId: string }>()
  const id = Number.parseInt(idStr)
  const [compound, setCompound] = useState<ProjectCompound>()
  const { notification } = App.useApp()

  const fetchCompound = async (id: number) => {
    const { data, error } = await service<ProjectCompound>(
      `project-compounds/${id}`
    )
      .select(['id', 'status'])
      .populateWith('compound', ['smiles'])
      .populateWith('project', ['id'])
      .get()
    if (!data || error) {
      notification.error({ message: error?.message || '分子未找到' })
      history.push('/404')
    } else {
      setCompound(data as unknown as ProjectCompound)
    }
  }

  useEffect(() => {
    fetchCompound(id)
  }, [id])

  if (!compound?.compound?.smiles) {
    return <></>
  }
  return (
    <EditRoute
      mainTree={{
        id: `${v4()}`,
        value: compound.compound.smiles
      }}
      compoundId={id}
      projectId={compound.project?.id}
      onCancel={() => history.back()}
    />
  )
}

export default CreateRoute
