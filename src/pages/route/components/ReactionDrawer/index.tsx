import { useGetMyReactionTabConfig } from '@/components/ReactionTabs/MyReactionTab'
import { getReactionProcedureTabConfig } from '@/components/ReactionTabs/ReactionProcedureTab'
import { Procedure, ProjectReaction, query } from '@/services/brain'
import { getWord } from '@/utils'
import { useAccess } from '@umijs/max'
import { Button, Drawer, Space, Tabs, TabsProps } from 'antd'
import React, { ReactNode, useEffect, useState } from 'react'
import { NavigateConfig, Reaction, getRxnFromReaction } from '../../util'

export interface ReactionDrawerProps {
  projectId?: number
  retroProcessId?: number
  reaction?: Reaction
  mainReaction?: Reaction
  title?: string | ReactNode
  onSelectProcedure?: (p?: string, procedures?: Procedure[]) => void
  onClose?: () => void
  onUpdate?: () => void
  navigateConfig?: NavigateConfig
}

const ReactionDrawer: React.FC<ReactionDrawerProps> = ({
  retroProcessId,
  onSelectProcedure,
  projectId,
  reaction,
  mainReaction,
  title,
  navigateConfig: { next, prev } = {},
  onClose,
  onUpdate
}) => {
  const [open, setOpen] = useState<boolean>(false)
  const [key, setKey] = useState<string>('reaction-procedure-lib')

  const onCheckDetail = async () => {
    if (!reaction || !projectId || !mainReaction) return
    const data = await query<ProjectReaction>(
      'project-reaction/get-or-create',
      {
        method: 'post',
        data: { reaction: getRxnFromReaction(mainReaction, true), projectId },
        normalizeData: false
      }
    ).get()

    const reactionId = (data as unknown as ProjectReaction[])?.[0]?.id
    if (reactionId) {
      window.open(`/projects/${projectId}/reaction/${reactionId}`, '_blank')
    }
  }
  const access = useAccess()
  useEffect(() => {
    setOpen(!!reaction)
  }, [reaction])

  const myReactionTab = useGetMyReactionTabConfig(
    mainReaction,
    { project: { id: projectId } } as ProjectReaction,
    onSelectProcedure,
    key,
    onUpdate
  )

  const getItems = () => {
    const curItems = []
    if (access?.authCodeList?.includes('view-by-backbone.tab.reactionSearch')) {
      curItems.push(
        getReactionProcedureTabConfig(
          projectId,
          mainReaction,
          onSelectProcedure,
          retroProcessId,
          () => setKey('my-reaction'),
          {
            fullReaction: reaction,
            onSelectFullReaction: (r) => console.log(r)
          }
        )
      )
    }
    if (access?.authCodeList?.includes('view-by-backbone.tab.myReaction')) {
      curItems.push(myReactionTab)
    }
    return curItems as TabsProps['items']
  }

  return (
    <Drawer
      title={title}
      open={open}
      width={1000}
      onClose={() => {
        setOpen(false)
        onClose?.()
      }}
      size="large"
      extra={
        <Space>
          <Button type="link" onClick={prev} disabled={!prev}>
            {getWord('prev-step')}
          </Button>
          <Button type="link" onClick={next} disabled={!next}>
            {getWord('next-step')}
          </Button>
          {access?.authCodeList?.includes(
            'view-by-backbone.tab.myReaction.reactionDetail'
          ) ? (
            <Button type="primary" onClick={onCheckDetail}>
              {getWord('menu.list.project-list.detail.reaction')}
            </Button>
          ) : (
            ''
          )}
        </Space>
      }
    >
      <Tabs items={getItems()} activeKey={key} onChange={setKey} />
    </Drawer>
  )
}

export default ReactionDrawer
