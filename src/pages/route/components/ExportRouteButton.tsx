import ButtonWithLoading from '@/components/ButtonWithLoading'
import { useRouteTreeStore } from '@/components/RouteEditor/Flex/Tree/store'
import { useBrainFetch } from '@/hooks/useBrainFetch'
import { query } from '@/services/brain'
import { getWord } from '@/utils'
import { saveAs } from 'file-saver'
import React from 'react'

const ExportRouteButton: React.FC = () => {
  const [route, exportFileName] = useRouteTreeStore((s) => [
    s.route,
    s.exportFileName
  ])
  const { fetch } = useBrainFetch(undefined, false)
  const handleDownload = async () => {
    const { data } = await fetch(
      query<any>('project-routes/download', {
        method: 'post',
        data: { data: { main_tree: route } },
        normalizeData: false
      }).get()
    )
    saveAs(
      new Blob([data as any], { type: 'text/xml' }),
      `${exportFileName}.cdxml`
    )
  }

  return (
    <ButtonWithLoading onClick={handleDownload} size="small">
      {getWord('export')}
    </ButtonWithLoading>
  )
}

export default ExportRouteButton
