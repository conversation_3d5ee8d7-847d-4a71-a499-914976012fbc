import {
  ProjectCompound,
  ProjectRoute,
  RetroBackbone,
  service
} from '@/services/brain'
import { getWord } from '@/utils'
import { history } from '@umijs/max'
import { App } from 'antd'
import { MessageInstance } from 'antd/es/message/interface'
import { NotificationInstance } from 'antd/es/notification/interface'
import { MainTreeForRoute, addMainMaterial } from './util'

const saveRoute = async (
  mainTree: MainTreeForRoute,
  action: 'save' | 'confirm' = 'save',
  message: MessageInstance,
  notification: NotificationInstance,
  ids?: {
    projectId?: number
    compoundId?: number
    retroBackboneId?: number
    treeId?: number
  },
  notToRedirect: boolean = false
) => {
  const { projectId, treeId, compoundId, retroBackboneId } = ids || {}
  const request = service<ProjectRoute>('project-routes')
  const status = action === 'confirm' ? 'confirmed' : 'editing'
  const mainTreeWithMainMaterial = await addMainMaterial(mainTree)
  let response
  if (!treeId) {
    const project_compound = compoundId
      ? ({ id: compoundId } as ProjectCompound)
      : undefined
    const retro_backbone = retroBackboneId
      ? ({ id: retroBackboneId } as RetroBackbone)
      : undefined
    response = await request.create({
      main_tree: mainTreeWithMainMaterial,
      project_compound,
      retro_backbone,
      status
    } as ProjectRoute)
  } else {
    response = await request.update(treeId, {
      main_tree: mainTreeWithMainMaterial,
      status
    })
  }
  const { data, error } = response

  const actionLabel = getWord(`pages.route.edit.label.${action}`)

  if (error || !data) {
    notification.error({
      message: (
        <>
          {actionLabel}
          {getWord('it.label.route')}
          {getWord('component.notification.statusValue.failed')}
        </>
      )
    })
  } else {
    message.success(
      <>
        {actionLabel}
        {getWord('pages.route.edit.label.route')}
        {getWord('app.general.message.success')}
      </>
    )
    if (!notToRedirect && projectId && compoundId) {
      history.push(
        `/projects/${projectId}/compound/${compoundId}/edit/${data.id}`
      )
    }
  }
  return data
}

export const useSaveRoute = (
  projectId?: number,
  compoundId?: number,
  retroBackboneId?: number
) => {
  const { message, notification } = App.useApp()
  return (
    mainTree: MainTreeForRoute,
    action: 'save' | 'confirm' = 'save',
    treeId?: number,
    notToRedirect: boolean = false
  ) =>
    saveRoute(
      mainTree,
      action,
      message,
      notification,
      {
        projectId,
        compoundId,
        retroBackboneId,
        treeId
      },
      notToRedirect
    )
}
