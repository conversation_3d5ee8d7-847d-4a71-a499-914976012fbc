import ButtonWithLoading from '@/components/ButtonWithLoading'
import Launcher from '@/components/Launcher'
import {
  GetRouteEvent,
  RouteEditor,
  UpdateChildrenEvent
} from '@/components/RouteEditor'
import { useRouteTreeStore } from '@/components/RouteEditor/Flex/Tree/store'
import { useBrainFetch } from '@/hooks/useBrainFetch'
import { fetchUserSetting, useUserSetting } from '@/hooks/useUserSetting'
import type {
  ProjectCompoundStatus,
  ProjectRoute,
  ProjectType
} from '@/services/brain'
import { ProjectCompound, RetroBackbone, service } from '@/services/brain'
import { getWord, isReadonlyMolecule } from '@/utils'
import { PageContainer } from '@ant-design/pro-components'
import {
  history,
  useAccess,
  useLocation,
  useModel,
  useParams
} from '@umijs/max'
import { App, Space, Typography } from 'antd'
import { cloneDeep } from 'lodash'
import React, { useEffect, useState } from 'react'
import ExportRouteButton from '../components/ExportRouteButton'
import ReactionDrawer from '../components/ReactionDrawer'
import EditRoute from '../edit'
import { useReactionDrawer } from '../hooks/useReactionDrawer'
import { useSaveRoute } from '../saveRoute'
import { useRxnYieldMap } from '../useRxnYieldMap'
import normalizeMainTree, {
  MainTreeForRoute,
  getAllRxnsFromTree,
  getDeepthMap,
  getNameOfReaction,
  getNavigateConfig,
  getRxnFromReaction,
  selectProcedure
} from '../util'
import './index.less'

const ViewRoutes: React.FC = ({}) => {
  const { backboneId: idStr = '' } = useParams<{ backboneId: string }>()
  const { message, notification, modal } = App.useApp()
  const [retroBackbonesData, setRetroBackbonesData] =
    useState<RetroBackbone | null>(null)
  const [mainTrees, setMainTrees] = useState<MainTreeForRoute[]>([])
  const [curTree, setCurTree] = useState<MainTreeForRoute>()
  const [editMode, setEditMode] = useState<boolean>(false)
  const [tab, setTab] = useState<string>('0')
  const [compoundId, setCompoundId] = useState<number>()
  const [retroProcessId, setRetroProcessId] = useState<number>()
  const [compound, setCompound] = useState<ProjectCompound>()
  const [updateChildrenEvent, setUpdateChildrenEvent] =
    useState<UpdateChildrenEvent>({})
  const [projectId, setProjectId] = useState<number>()
  const [projectStatus, setProjectStatus] = useState<ProjectType>()
  const [compoundStatus, setCompoundStatus] = useState<ProjectCompoundStatus>()
  const setExportFileName = useRouteTreeStore((s) => s.setExportFileName)
  const { fetch } = useBrainFetch()
  const {
    getProfileInfo,
    getCommonExpression,
    showLauncher,
    sendMessage,
    reload,
    finishedReload,
    cacheCurReactionStepNo,
    isOpen
  } = useModel('commend')
  const { pathname } = useLocation()
  useEffect(() => {
    getCommonExpression('retro-backbone')
    return () => {
      if (isOpen) showLauncher()
    }
  }, [])
  const { setting } = useUserSetting()
  const saveRoute = useSaveRoute(projectId, compoundId, retroBackbonesData?.id)

  const [selectedMainTree, setSelectedMainTree] = useState<MainTreeForRoute>(
    mainTrees[0]
  )
  const access = useAccess()
  const [saveEvent, setSaveEvent] = useState<GetRouteEvent>({})
  const [selectRxnEvent, setSelectRxnEvent] = useState<{ select?: string }>({})
  const {
    onSelectReaction,
    selectedReaction,
    selectedNode,
    selectedMainReaction
  } = useReactionDrawer(curTree)
  const deepthMap = getDeepthMap(selectedMainTree)
  const getStepName = (node?: MainTreeForRoute): string => {
    const deepth = node?.id && deepthMap.get(node.id)
    if (deepth) {
      let reaction_step_no = getNameOfReaction(deepth[0], deepth[1])
      cacheCurReactionStepNo(reaction_step_no)
      return `${getWord('reaction')} ${reaction_step_no}`
    }
    return getWord('reaction')
  }
  const { map, add } = useRxnYieldMap(
    getAllRxnsFromTree(selectedMainTree),
    projectId
  )
  const id = Number.parseInt(idStr)

  const fetchRetroBackbones = async (id: number) => {
    const { data, error } = await service<RetroBackbone>(
      `retro-backbones/${id}`,
      {
        customQuery: `comment=true&collectionId=${id}-${Number(tab) + 1}`
      }
    )
      .select(['main_trees', 'full_trees'])
      .populateDeep([
        {
          path: 'retro_process',
          fields: ['id'],
          children: [{ key: 'project_compound', fields: ['id'] }]
        }
      ])
      .get()
    if (error) {
      message.error(error.message)
    } else if (!data) {
      message.error(`Retro backbone with id ${id} not found`)
    } else {
      const backbone = data as unknown as RetroBackbone
      if (!backbone.main_trees.length) {
        message.error(`Retro backbone with id ${id} has no main_trees`)
      } else {
        return backbone
      }
    }
    return null
  }

  const fetchCompound = async (
    id: number
  ): Promise<ProjectCompound | undefined> => {
    const { data } = await fetch(
      service<ProjectCompound>('project-compounds')
        .selectManyByID([id])
        .populateWith('project', ['id'])
        .get()
    )
    return data?.[0]
  }

  const updateData = async () => {
    if (!compoundId) return
    const data = await fetchCompound(compoundId)
    setCompound(data)
    setCompoundStatus(data?.status)
    const projectId = data?.project?.id
    const projectStatus = data?.project?.status as ProjectType
    setProjectStatus(projectStatus)
    if (projectId) {
      setProjectId(projectId)
    }
  }

  const handleSelect = async () => {
    const { data } = await service<ProjectRoute>(
      `project-compounds/${compoundId}/select`,
      {
        method: 'post',
        data: { backboneId: id, selectedMainTreeIndex: Number.parseInt(tab) }
      }
    )
      .select()
      .get()
    await updateData()

    const route = data?.[0]

    if (!route) return
    notification.success({
      type: 'success',
      message: getWord('replaced-route-success'),
      duration: 3,
      description: (
        <Typography.Link
          onClick={() => {
            notification.destroy()
            history.push(
              `/projects/${route.project_compound?.project?.id}/compound/${route.project_compound?.id}/edit/${route.id}`
            )
          }}
        >
          {getWord('continue-edit')}
        </Typography.Link>
      )
    })
  }

  const handleConfirmSelect = async () => {
    modal.confirm({
      title: getWord('choose-route'),
      content: getWord('choose-route-tip'),
      onOk: handleSelect
    })
  }

  useEffect(() => {
    Promise.all([fetchRetroBackbones(id), fetchUserSetting()]).then(
      ([data, setting]) => {
        if (data) {
          let trees = data.main_trees.map((t) => normalizeMainTree(t))
          if (
            setting?.retro?.route_detail_show_policy === 'all_route' &&
            data.full_trees?.length === data.main_trees?.length
          ) {
            trees = data.full_trees.map((t) => normalizeMainTree(t))
          }
          setRetroBackbonesData(data)
          setCompoundId(data.retro_process.project_compound?.id)
          setRetroProcessId(data.retro_process.id)
          setMainTrees(trees)
          setSelectedMainTree(trees[0])
        }
      }
    )
  }, [id])

  const onSelectProcedure = selectProcedure(
    setUpdateChildrenEvent,
    curTree,
    selectedReaction
  )

  const getCommendAmount = () => {
    setRetroBackbonesData(null)
    fetchRetroBackbones(id).then((data) => {
      if (data) {
        setRetroBackbonesData(data)
      }
    })
  }

  useEffect(() => {
    if (reload) {
      getCommendAmount()
      finishedReload()
    }
  }, [reload])

  useEffect(() => {
    getCommendAmount()
    setExportFileName(`${getWord('Routes')} ${id}-${Number.parseInt(tab) + 1}`)
  }, [tab])

  useEffect(() => {
    updateData()
  }, [compoundId])

  if (Number.isNaN(id)) {
    history.push('/404')
    return <></>
  }

  if (editMode) {
    return (
      <EditRoute
        projectId={projectId}
        retroProcessId={retroProcessId}
        mainTree={cloneDeep(selectedMainTree)}
        onCancel={() => {
          onSelectReaction(false)
          setSaveEvent({})
          setUpdateChildrenEvent({})
          setEditMode(false)
          setCurTree(cloneDeep(selectedMainTree))
        }}
        retroBackboneId={id}
        compoundId={compoundId}
      />
    )
  }
  const isPlayground: boolean = pathname.includes('/playground')
  return (
    <PageContainer
      className="route-view-by-backbone-root"
      title={getWord(`menu.list.route.${editMode ? 'edit' : 'view'}`)}
      header={{
        title: getWord(`menu.list.route.${editMode ? 'edit' : 'view'}`)
      }}
      tabList={mainTrees.map((_, index) => ({
        tab: `${id}-${index + 1}`,
        key: `${index}`
      }))}
      tabProps={{ type: 'card' }}
      tabActiveKey={tab}
      onTabChange={(key) => {
        setTab(key)
        setSelectedMainTree(mainTrees[Number.parseInt(key)])
      }}
    >
      <div className="container">
        {selectedMainTree && (
          <>
            <RouteEditor
              root={selectedMainTree}
              getRouteEvent={saveEvent}
              onTreeChange={(t) => {
                setCurTree(t)
                if (selectedNode?.id) onSelectReaction(selectedNode.id, t)
              }}
              editMode={false}
              onSelectRxn={onSelectReaction}
              selectRxnEvent={selectRxnEvent}
              updateChildrenEvent={updateChildrenEvent}
              rxnYieldMap={setting.retro?.route_show_yields ? map : undefined}
              rightTopSlot={
                <Space size="middle">
                  {access?.authCodeList?.includes(
                    'view-by-backbone.button.export'
                  ) && <ExportRouteButton />}
                  {!isReadonlyMolecule(projectStatus, compoundStatus) &&
                    !isPlayground &&
                    (compound?.type === 'temp_block' ? (
                      <ButtonWithLoading
                        onClick={handleConfirmSelect}
                        size="small"
                        type="primary"
                      >
                        {getWord('choose')}
                      </ButtonWithLoading>
                    ) : (
                      <>
                        {access?.authCodeList?.includes(
                          'view-by-backbone.button.comment'
                        ) && (
                          <ButtonWithLoading
                            onClick={() => {
                              getProfileInfo({
                                _commendSuject: {
                                  id: `${id}-${Number(tab) + 1}`
                                },
                                collection_class: 'retro-backbone'
                              })
                            }}
                            size="small"
                            type="primary"
                          >
                            {retroBackbonesData?.content_count &&
                            retroBackbonesData?.content_count > 0
                              ? `${getWord('comment')}（${
                                  retroBackbonesData?.content_count
                                }）`
                              : getWord('comment')}
                          </ButtonWithLoading>
                        )}
                        {access?.authCodeList?.includes(
                          'view-by-backbone.button.edit'
                        ) && (
                          <ButtonWithLoading
                            onClick={() =>
                              setSaveEvent({
                                getRoute: (route) => {
                                  if (route) setSelectedMainTree(route)
                                  setEditMode(true)
                                  setSaveEvent({})
                                }
                              })
                            }
                            size="small"
                            type="primary"
                          >
                            {getWord('edit')}
                          </ButtonWithLoading>
                        )}

                        <ButtonWithLoading
                          onClick={async () => {
                            return new Promise((resolve, reject) => {
                              modal.confirm({
                                title: getWord('confirm-the-route'),
                                content: getWord('route-confirm'),
                                onOk: () =>
                                  setSaveEvent({
                                    getRoute: async (route) => {
                                      if (!route) return
                                      await saveRoute(
                                        normalizeMainTree(route),
                                        'confirm'
                                      )
                                      resolve()
                                    }
                                  }),
                                onCancel: () => reject()
                              })
                            })
                          }}
                          size="small"
                          type="primary"
                        >
                          {getWord('pages.route.edit.label.confirm')}
                        </ButtonWithLoading>
                      </>
                    ))}
                </Space>
              }
            />
            <Launcher
              onMessageWasSent={sendMessage}
              hiddenLauncher={showLauncher}
              isOpen={isOpen}
            />
          </>
        )}
      </div>

      <ReactionDrawer
        projectId={projectId}
        reaction={selectedReaction}
        title={getStepName(selectedNode)}
        onClose={() => setSelectRxnEvent({})}
        retroProcessId={retroProcessId}
        onSelectProcedure={onSelectProcedure}
        mainReaction={selectedMainReaction}
        onUpdate={() =>
          selectedReaction && add(getRxnFromReaction(selectedReaction))
        }
        navigateConfig={
          selectedMainTree &&
          selectedNode &&
          getNavigateConfig(selectedMainTree, selectedNode, (node) => {
            onSelectReaction(node.id)
            setSelectRxnEvent({ select: node.id })
          })
        }
      />
    </PageContainer>
  )
}

export default ViewRoutes
