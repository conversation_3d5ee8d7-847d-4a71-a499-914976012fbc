import ReactionCard from '@/components/ReactionCard'
import { newReaction } from '@/components/ReactionCard/index.d'
import { useBrainFetch } from '@/hooks/useBrainFetch'
import { useFormStorage } from '@/hooks/useFormStorage'
import { ProjectReaction, ProjectRoute, query } from '@/services/brain'
import { getWord } from '@/utils'
import {
  LightFilter,
  PageContainer,
  ProFormSelect,
  ProList
} from '@ant-design/pro-components'
import { useModel } from '@umijs/max'
import { useForm, useWatch } from 'antd/es/form/Form'
import React, { useEffect, useState } from 'react'
import { useCompoundFilterProps } from '../component/Filters/Compound'
import { getProjectOptions, getRoutes } from '../utils'

interface ReactionFilter {
  projectId?: number
  compoundNo?: number
  routeId?: number
}

const MyReactionPage: React.FC = () => {
  const { initialState: { userInfo = undefined } = {} } =
    useModel('@@initialState')
  const userId = userInfo?.id

  const [form] = useForm<ReactionFilter>()
  const compoundNo = useWatch<number>('compoundNo', form)
  const projectId = useWatch<number>('projectId', form)
  const compoundFilterProps = useCompoundFilterProps(userId, projectId, true)
  const [allRoutes, setAllRoutes] = useState<ProjectRoute[]>([])
  const [filter, setFilter] = useState<ReactionFilter>()
  const { fetch } = useBrainFetch()
  const [get, set] = useFormStorage()
  form.setFieldsValue(get())

  useEffect(() => {
    setFilter(form.getFieldsValue())
  }, [])
  useEffect(
    () => form.setFieldsValue({ compoundNo: undefined, routeId: undefined }),
    [projectId]
  )
  useEffect(() => form.setFieldsValue({ routeId: undefined }), [compoundNo])

  useEffect(() => {
    let unmount = false
    getRoutes(userId).then((ops) => {
      if (!unmount) setAllRoutes(ops)
    })
    return () => {
      unmount = false
    }
  }, [userId])

  const request = async (
    params: Partial<
      ReactionFilter & {
        current: number
        pageSize: number
        allRouteIds: number[]
      }
    >
  ) => {
    const p = { current: 1, pageSize: 10, allRouteIds: [], ...params }
    if (!p.allRouteIds.length) return { data: [], success: false }

    const req = query<ProjectReaction>('project-reactions')
      .paginate(p.current, p.pageSize)
      .sortBy([{ field: 'updatedAt', order: 'desc' }])
      .populateWith('project', ['id', 'no'])
      .populateDeep([
        {
          path: 'project_routes',
          fields: ['id', 'name'],
          children: [{ key: 'project_compound', fields: ['id'] }]
        }
      ])
    if (p.projectId) req.filterDeep('project.id', 'eq', p.projectId)
    if (p.routeId) {
      req.filterDeep('project_routes.id', 'eq', p.routeId)
    } else {
      const routes = p.compoundNo
        ? allRoutes.filter((r) => r.project_compound?.no === p.compoundNo)
        : allRoutes
      req.filterDeep(
        'project_routes.id',
        'in',
        routes.length ? routes.map((r) => r.id) : [-1]
      )
    }

    const { data, meta } = await fetch(req.get())
    return {
      data: data || [],
      total: meta?.pagination.total,
      success: !!data
    }
  }

  if (!userId) return null

  const onUpdateFilter = async (filter: ReactionFilter) => setFilter(filter)

  const filterComp = (
    <LightFilter
      bordered
      onFinish={onUpdateFilter}
      form={form}
      onValuesChange={(_, v) => set(v)}
    >
      <ProFormSelect
        name="projectId"
        placeholder={getWord('project-ID')}
        request={({ userId }) => getProjectOptions(userId)}
        params={{ userId }}
        debounceTime={300}
        fieldProps={{ onClick: (e) => e.stopPropagation() }}
        showSearch
      />
      <ProFormSelect
        {...compoundFilterProps}
        name="compoundNo"
        placeholder={getWord('target-molecules')}
      />
      <ProFormSelect
        name="routeId"
        placeholder={getWord('Routes')}
        request={async ({ userId, compoundNo, projectId }) => {
          const rs = await getRoutes(userId, projectId, compoundNo)
          return rs.map((r) => ({ label: r.name, value: r.id }))
        }}
        params={{ userId, compoundNo, projectId }}
        debounceTime={300}
        fieldProps={{ onClick: (e) => e.stopPropagation() }}
        showSearch
      />
    </LightFilter>
  )

  const listComp = (
    <ProList<ProjectReaction>
      ghost
      pagination={{ defaultPageSize: 10, showSizeChanger: false }}
      showActions="hover"
      rowSelection={false}
      grid={{ column: 2 }}
      renderItem={(item) => (
        <ReactionCard
          reaction={item as newReaction}
          enableToReaction
          displayProject
        />
      )}
      request={request}
      params={{ ...filter, allRouteIds: allRoutes }}
      className="noPaddingCard"
    />
  )

  return (
    <PageContainer ghost content={filterComp}>
      {listComp}
    </PageContainer>
  )
}

export default MyReactionPage
