import { useBrainFetch } from '@/hooks/useBrainFetch'
import { useFormStorage } from '@/hooks/useFormStorage'
import useOptions from '@/hooks/useOptions'
import {
  ProjectCompound,
  ProjectCompoundStatus,
  ProjectCompoundType,
  query
} from '@/services/brain'
import { getWord } from '@/utils'
import {
  LightFilter,
  PageContainer,
  ProForm,
  ProFormCheckbox,
  ProFormSelect,
  ProList
} from '@ant-design/pro-components'
import { useModel } from '@umijs/max'
import { useForm } from 'antd/es/form/Form'
import React, { useEffect, useState } from 'react'
import CompoundCard from '../component/CompoundCard'
import { useCompoundFilterProps } from '../component/Filters/Compound'
import SortOrder, { SortOrderValue } from '../component/Filters/SortOrder'
import { getProjectOptions } from '../utils'
import styles from './index.less'

interface CompoundFilter {
  projectId?: number
  no?: string
  type?: ProjectCompoundType
  status?: ProjectCompoundStatus[]
  sortBy: 'no' | 'updatedAt'
  sortOrder: SortOrderValue
}

const initFilter: CompoundFilter = {
  status: ['created', 'designing', 'synthesizing', 'finished'],
  sortOrder: 'asc',
  sortBy: 'no'
}

const MyCompoundPage: React.FC = () => {
  const { typeMapForSelect } = useOptions()
  const { moleculeStatusOptions, sortStandard } = useOptions()
  const { initialState: { userInfo = undefined } = {} } =
    useModel('@@initialState')
  const userId = userInfo?.id
  const compoundFilterProps = useCompoundFilterProps(userId, undefined, true)
  const [form] = useForm<CompoundFilter>()
  const [filter, setFilter] = useState<CompoundFilter>()
  const { fetch } = useBrainFetch()
  const [get, set] = useFormStorage()
  form.setFieldsValue({ ...initFilter, ...get() })

  useEffect(() => {
    setFilter(form.getFieldsValue())
  }, [])

  if (!userId) return null

  const request = async (
    params: Partial<CompoundFilter & { current: number; pageSize: number }>
  ) => {
    const p = { ...initFilter, current: 1, pageSize: 10, ...params }
    const req = query<ProjectCompound>('project-compounds')
      .equalTo('director_id', userId)
      .paginate(p.current, p.pageSize)
      .sortBy([{ field: p.sortBy, order: p.sortOrder }])
      .populateWith('project_routes', ['id'])
      .populateWith('compound', ['smiles'])
      .populateWith('project', ['id', 'no'])
      .notEqualTo('type', 'temp_block')
      .populateDeep([
        {
          path: 'retro_processes',
          fields: ['id'],
          children: [{ key: 'retro_backbones', fields: ['id'] }]
        }
      ])
    if (p.no) req.equalTo('no', p.no)
    if (p.projectId) req.filterDeep('project.id', 'eq', p.projectId)
    if (p.status?.length) req.filterDeep('status', 'in', p.status)
    if (p.type) req.equalTo('type', p.type)

    const { data, meta } = await fetch(req.get())
    data?.forEach((item) => {
      item.project_routes_number = item.project_routes?.length
      item.retro_backbones_number = item.retro_processes?.flatMap(
        (p) => p.retro_backbones
      ).length
    })
    return { data: data || [], total: meta?.pagination.total, success: !!data }
  }
  const onUpdateFilter = async (filter: CompoundFilter) => {
    setFilter(filter)
  }
  const filterComp = (
    <LightFilter
      bordered
      onFinish={onUpdateFilter}
      form={form}
      onValuesChange={(_, v) => set(v)}
    >
      <ProFormSelect
        name="projectId"
        placeholder={getWord('project-ID')}
        request={({ userId }) => getProjectOptions(userId)}
        params={{ userId }}
        debounceTime={300}
        fieldProps={{ onClick: (e) => e.stopPropagation() }}
        showSearch
      />
      <ProFormSelect {...compoundFilterProps} />
      <ProFormSelect
        name="type"
        placeholder={getWord('molecules-type')}
        valueEnum={typeMapForSelect}
        fieldProps={{
          popupMatchSelectWidth: false,
          onClick: (e) => e.stopPropagation()
        }}
      />
      <ProFormCheckbox.Group
        name="status"
        placeholder={getWord('molecules-status')}
        options={moleculeStatusOptions}
      />
    </LightFilter>
  )
  const sortComp = (
    <LightFilter
      bordered
      form={form}
      onFinish={onUpdateFilter}
      className={styles.sort}
      onValuesChange={(_, v) => set(v)}
    >
      <ProFormSelect
        name="sortBy"
        placeholder="排序方式"
        valueEnum={sortStandard}
        allowClear={false}
        fieldProps={{
          popupMatchSelectWidth: false,
          onClick: (e) => e.stopPropagation()
        }}
      />
      <ProForm.Item name="sortOrder">
        <SortOrder />
      </ProForm.Item>
    </LightFilter>
  )
  const listComp = (
    <ProList<ProjectCompound>
      ghost
      pagination={{ defaultPageSize: 12, showSizeChanger: false }}
      showActions="hover"
      rowSelection={false}
      grid={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 4, xxl: 4 }}
      renderItem={(item) => <CompoundCard compound={item} />}
      request={request}
      params={filter}
    />
  )

  return (
    <PageContainer ghost content={filterComp} extraContent={sortComp}>
      {listComp}
    </PageContainer>
  )
}

export default MyCompoundPage
