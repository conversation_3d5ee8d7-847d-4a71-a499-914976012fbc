import ExperimentListTab from '@/components/ReactionTabs/ExperimentListTab'
import { PageContainer } from '@ant-design/pro-components'
import { useModel } from '@umijs/max'
import React from 'react'

const MyExperimentPage: React.FC = () => {
  const { initialState: { userInfo = undefined } = {} } =
    useModel('@@initialState')
  const userId = userInfo?.id
  if (!userId) return null
  return (
    <PageContainer>
      <ExperimentListTab ownerId={userId} />
    </PageContainer>
  )
}

export default MyExperimentPage
