import LazySmileDrawer from '@/components/LazySmileDrawer'

import useOptions from '@/hooks/useOptions'
import { ProjectCompound, ProjectMember } from '@/services/brain'
import { getWord, isEN } from '@/utils'
import { Card, Col, Row, Tag, Typography } from 'antd'
import cs from 'classnames'
import React, { useEffect, useState } from 'react'
import { history } from 'umi'
import { useProjectMembers } from '../../utils'
import styles from './index.less'

export interface CompoundCardProps {
  compound: ProjectCompound
}

const CompoundCard: React.FC<CompoundCardProps> = ({
  compound: initCompound
}) => {
  const { getById } = useProjectMembers()
  const { moleculeStatusOptions, typeMap } = useOptions()
  const [members, setMembers] = useState<ProjectMember[]>([])
  const [projectCompound, setProjectCompound] =
    useState<ProjectCompound>(initCompound)
  const {
    compound,
    project,
    id,
    status,
    priority,
    director_id,
    no,
    type,
    retro_backbones_number,
    project_routes_number
  } = projectCompound

  useEffect(() => {
    getById(project?.id).then((ms) => setMembers(ms))
  }, [project?.id])

  useEffect(() => {
    setProjectCompound(initCompound)
  }, [initCompound])

  return (
    <div className={styles.moleculeCardRoot}>
      <Card
        className={cs('enablePointer', styles.card, status && styles[status])}
        onClick={() =>
          history.push(
            `/projects/${project?.id}/compound/${id}?page=1&pageSize=10`
          )
        }
      >
        <div className="flex-justify-space-between">
          <div className="flex-auto">
            <LazySmileDrawer
              structure={compound?.smiles || ''}
              className={cs('enablePointer', styles.smileDrawer)}
            />
          </div>
          <div className={styles.moleculeInfo}>
            <Row>
              <Typography.Text
                strong
                style={{ maxWidth: 150 }}
                ellipsis={{ tooltip: true }}
              >
                {no}
              </Typography.Text>
            </Row>
            <Row className={styles.title}>
              <Tag color="green">{typeMap[type]}</Tag>
              <Col span={12}>
                <Tag color="gold">
                  {moleculeStatusOptions.find((e) => e.value === status)?.label}
                </Tag>
              </Col>
            </Row>
            <div className={styles.routeInfo}>
              <Row className={cs(styles.item, 'flex-align-items-center')}>
                <div className={styles.label}>
                  {getWord('aiGenerated')}
                  &nbsp;&nbsp;
                </div>
                <div>
                  {retro_backbones_number || 0} {isEN() ? '' : '条'}
                </div>
              </Row>
              <Row className={cs(styles.item, 'flex-align-items-center')}>
                <div className={styles.label}>
                  {getWord('myRoutes')}
                  &nbsp;&nbsp;
                </div>
                <div>
                  {project_routes_number || 0} {isEN() ? '' : '条'}
                </div>
              </Row>
            </div>
            <Row className={cs(styles.item, 'flex-align-items-center')}>
              <div className={styles.label}>
                {getWord('pages.experiment.label.owner')}
              </div>
              <div>
                {
                  members.find((m) => m.user_id === director_id)?.user_info
                    ?.username
                }
              </div>
            </Row>
            <Row className={cs(styles.item, 'flex-align-items-center')}>
              <div className={styles.label}>
                {getWord('menu.list.project-list')}
              </div>
              <div>
                <Typography.Text
                  style={{ width: 80 }}
                  ellipsis={{ tooltip: project?.no }}
                >
                  {project?.no}
                </Typography.Text>
              </div>
            </Row>
            <Row className={cs(styles.item, 'flex-align-items-center')}>
              <div className={styles.label}>{getWord('Priority')}</div>
              {priority}
            </Row>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default CompoundCard
