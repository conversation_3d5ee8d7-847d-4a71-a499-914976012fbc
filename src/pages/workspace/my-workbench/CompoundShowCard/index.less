@import '@/style/variables.less';

.moleculeCardRoot {
  width: 100%;
  height: 240px;
  margin: 4px;
  padding-right: 8px;
  .card {
    color: @color-text-base !important;
    :global .ant-card-body {
      padding: 4px 12px 4px 0px;
    }
    &.created {
      border: 1px solid @color-border-created;
    }
    &.designing {
      border: 1px solid @color-border-designing;
    }
    &.synthesizing {
      border: 1px solid @color-border-synthsizing;
    }
    &.finished {
      border: 1px solid @color-border-finished;
    }
    &.canceled {
      border: 1px solid @color-border-canceled;
    }
    .smileDrawer {
      width: auto;
      height: 226px;
      max-height: 226px;
      overflow: hidden;
    }
    .moleculeInfo {
      flex: 0 0 158px;
      padding-left: 14px;
      cursor: unset !important;
      .title {
        margin-top: 9px;
        margin-bottom: 29px;
      }
      .routeInfo {
        margin-bottom: 29px;
      }
      .item {
        height: 22px;
      }
    }
  }
  .label {
    min-width: 48px;
    margin: 4px 0;
    color: #626262;
    font-weight: 400;
    font-size: 14px;
  }
}
