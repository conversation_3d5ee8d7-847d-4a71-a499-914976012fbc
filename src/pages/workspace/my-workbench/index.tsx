import ReactionCard, { newReaction } from '@/components/ReactionCard'
import ExperimentListTab from '@/components/ReactionTabs/ExperimentListTab'
import SectionTitle from '@/components/SectionTitle'
import { ProjectCompound, ProjectReaction } from '@/services/brain'
import { getWord } from '@/utils'
import { PageContainer, ProList } from '@ant-design/pro-components'
import { history, useModel } from '@umijs/max'
import { Row } from 'antd'
import cs from 'classnames'
import { useEffect, useState } from 'react'
import CompoundShowCard from './CompoundShowCard'
import ProjectSummary from './ProjectSummary'
import {
  useCompound,
  useExperiment,
  useReaction,
  useWorkbenchTotalInfo
} from './fetchs'
import styles from './index.less'

const numsType = [
  'createdCompound',
  'designingCompound',
  'createdExperiment',
  'runningExperiment',
  'completedExperiment',
  'reaction'
] as const
type NumsKey = (typeof numsType)[number]
type NumsMap = {
  [k in NumsKey]?: number
}

export default function MyWorkbench() {
  const { initialState: { userInfo = undefined } = {} } =
    useModel('@@initialState')
  const { initialState } = useModel('@@initialState')
  const userId = userInfo?.id || 0
  const [nums, setNums] = useState<NumsMap>({})
  const { workbenchTotalInfo } = useWorkbenchTotalInfo(userId)
  const { fetchData, fetchNum: fetchCompoundNum } = useCompound(userId)
  const { fetchData: fetchReactionData, fetchNum: fetchReactionNum } =
    useReaction(userId)
  const { fetchNum: fetchExperimentNum } = useExperiment(userId)

  useEffect(() => {
    fetchCompoundNum('created').then((n) =>
      setNums((p) => ({ ...p, createdCompound: n }))
    )
    fetchCompoundNum('designing').then((n) =>
      setNums((p) => ({ ...p, designingCompound: n }))
    )
    fetchExperimentNum('created').then((n) =>
      setNums((p) => ({ ...p, createdExperiment: n }))
    )
    fetchExperimentNum('running').then((n) =>
      setNums((p) => ({ ...p, runningExperiment: n }))
    )
    fetchExperimentNum('completed').then((n) =>
      setNums((p) => ({ ...p, completedExperiment: n }))
    )
    fetchReactionNum().then((n) => setNums((p) => ({ ...p, reaction: n })))
  }, [])
  const reactionComp = (
    <ProList<ProjectReaction>
      ghost
      pagination={false}
      showActions="hover"
      rowSelection={false}
      grid={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 3, xxl: 3 }}
      className={styles.reactionCard}
      renderItem={(item) => (
        <ReactionCard
          reaction={item as newReaction}
          enableToReaction
          displayProject
        />
      )}
      request={fetchReactionData}
    />
  )

  return (
    <PageContainer>
      <section className={cs(styles.myWorkbench, 'flex-justify-space-between')}>
        <div
          className={cs(styles.projectSummary, {
            [styles['unfold']]: !initialState?.isMenuCollapsed,
            [styles['fold']]: initialState?.isMenuCollapsed
          })}
        >
          <SectionTitle word={getWord('project-summary')} />
          <ProjectSummary workbenchTotalInfo={workbenchTotalInfo} />
        </div>
        <div className={styles.detailInfo}>
          <SectionTitle word={`${getWord('pages.my-workbench.todo-list')}`} />
          <Row justify="space-between" className={styles.titleContent}>
            <div className="display-flex">
              <div className={styles.subTitle}>
                待设计的分子&nbsp;&nbsp;
                <span>{nums.createdCompound}个</span>
              </div>
              <span className={styles.dot}>•</span>
              <div className={styles.subTitle}>
                设计中的分子&nbsp;&nbsp;
                <span>{nums.designingCompound}个</span>
              </div>
            </div>
            <div
              className="enablePointer"
              onClick={() => history.push('/workspace/my-compound')}
            >
              {getWord('pages.projectTable.actionLabel.viewDetail')}
            </div>
          </Row>
          <ProList<ProjectCompound>
            ghost
            pagination={false}
            showActions="hover"
            rowSelection={false}
            grid={{ xs: 2, sm: 2, md: 3, lg: 3, xl: 3, xxl: 3 }}
            renderItem={(item) => <CompoundShowCard compound={item} />}
            request={fetchData}
          />
          <Row justify="space-between" className={styles.titleContent}>
            <div>待推进的反应&nbsp;&nbsp;{nums.reaction}个</div>
            <div
              className="enablePointer"
              onClick={() => history.push('/workspace/my-reaction')}
            >
              {getWord('pages.projectTable.actionLabel.viewDetail')}
            </div>
          </Row>
          {reactionComp}
          <Row justify="space-between" className={styles.titleContent}>
            <div className="display-flex">
              <div className={styles.subTitle}>
                待开始的实验&nbsp;&nbsp;
                <span>{nums.createdExperiment}个</span>
              </div>
              <span className={styles.dot}>•</span>
              <div className={styles.subTitle}>
                进行中的实验&nbsp;&nbsp;
                <span>{nums.runningExperiment}个</span>
              </div>
              <span className={styles.dot}>•</span>
              <div className={styles.subTitle}>
                待结论的实验&nbsp;&nbsp;
                <span>{nums.completedExperiment}个</span>
              </div>
            </div>
            <div
              className="enablePointer"
              onClick={() => history.push('/workspace/my-experiment')}
            >
              {getWord('pages.projectTable.actionLabel.viewDetail')}
            </div>
          </Row>
          <ExperimentListTab ownerId={8} isWorkbench={true} />
        </div>
      </section>
    </PageContainer>
  )
}
