@import '@/style/variables.less';
.myWorkbench {
  display: flex;
  padding-left: 16px;
  // background: red;
  // overflow-x: scroll !important;
  .projectSummary {
    padding-right: 50px;
  }
  .unfold {
    flex: 0 0 @workbench-unfold-summary-width;
    // flex: 0 0 calc(100vw - @menu-width - @scrollbar-width - 458px);
  }

  .fold {
    flex: 0 0 @workbench-fold-summary-width;
  }

  .detailInfo {
    flex: 1;
    .titleContent {
      margin-top: 14px;
      padding: 0 8px 6px 4px;
    }
    .subTitle {
      color: rgba(98, 98, 98, 1);
      font-size: 14px;
      line-height: 22px;
      span {
        color: rgba(0, 71, 187, 1);
      }
    }
    .dot {
      margin: 0 12px;
    }
    .reactionCard {
      :global {
        .ant-card-body {
          padding: 0px !important;
        }
      }
    }
  }

  :global {
    .ant-pro-card-body {
      padding: 0px !important;
    }
  }
}
