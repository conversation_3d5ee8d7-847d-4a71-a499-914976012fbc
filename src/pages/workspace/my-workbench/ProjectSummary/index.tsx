import { getWord, isValidArray } from '@/utils'
import { history } from '@umijs/max'
import { Card, Col, Row } from 'antd'
import cs from 'classnames'
import { useEffect, useState } from 'react'
import type {
  CompoundKeyStats,
  ExperimentKeyStats,
  ReactionKeyStats,
  WorkbenchInfo
} from '../index.d'
import styles from './index.less'
interface ProjectSummaryProps {
  workbenchTotalInfo: WorkbenchInfo
}
type SummaryType = 'reaction' | 'experiment' | 'compound'
type CardItem = ReactionKeyStats & CompoundKeyStats & ExperimentKeyStats
export default function ProjectSummary(props: ProjectSummaryProps) {
  const { workbenchTotalInfo } = props

  const workbenchSummaryDes = {
    reaction: getWord('reaction'),
    experiment: getWord('pages.experiment'),
    compound: getWord('molecules')
  }
  const [infoData, setInfoData] = useState<any[]>([])
  const formattDate = () => {
    let finalData = [
      {
        type: 'compound',
        data: workbenchTotalInfo?.compound_key_stats,
        url: workbenchTotalInfo?.compound_url
      },
      {
        type: 'reaction',
        data: workbenchTotalInfo?.reaction_key_stats,
        url: workbenchTotalInfo?.reaction_url
      },
      {
        type: 'experiment',
        data: workbenchTotalInfo?.experiment_key_stats,
        url: workbenchTotalInfo?.experiment_url
      }
    ]
    setInfoData(finalData)
  }

  useEffect(() => {
    formattDate()
  }, [workbenchTotalInfo])

  const NumInfo = ({ curItem }: { curItem: CardItem }) => {
    return (
      <Card className={styles.card}>
        <div className={styles.numTitle}>{curItem?.actual_label}</div>
        <div className={styles.num}>{curItem?.actual_value}</div>
        <div className={(styles.detail, 'flex-justify-space-between')}>
          <div>
            {curItem?.target_label}&nbsp;&nbsp;{curItem?.target_value}
          </div>
          <div>
            {curItem?.progress_label}&nbsp;&nbsp;{curItem?.progress_value}
          </div>
        </div>
        <div className={styles.detail}>
          {curItem?.last_period_label}&nbsp;&nbsp;{curItem?.last_period_value}
        </div>
      </Card>
    )
  }

  const SummaryInfo = ({ curItem }: { curItem: CardItem }) => {
    return (
      <Card className={styles.card}>
        <div className={styles.summaryLabel}>{curItem?.rank_label}</div>
        <div className={styles.summary}>{curItem?.rank_value}</div>
        <div className={styles.diff}>
          {curItem?.last_period_label}
          &nbsp;&nbsp;
          <span className={styles.diffValue}>{curItem?.last_period_value}</span>
        </div>
      </Card>
    )
  }
  const TitleCom = ({ type }: { type?: SummaryType }) => {
    return (
      <div className={cs(styles.titleContent, 'flex-justify-space-between')}>
        <div className={styles.title}>{workbenchSummaryDes[type]}</div>
        <div
          className={(styles.reportLink, 'enablePointer')}
          onClick={() => {
            history.push(infoData.find((e) => e.type === type)?.url as string)
          }}
        >
          详细报表
        </div>
      </div>
    )
  }

  const TargetCom = ({ curItem }: { curItem: CardItem }) => {
    let isCompareCom = curItem.hasOwnProperty('rank_label')
    return isCompareCom ? (
      <SummaryInfo curItem={curItem} />
    ) : (
      <NumInfo curItem={curItem} />
    )
  }

  return (
    <div className={styles.projectSummary}>
      {isValidArray(infoData)
        ? infoData?.map((item: any) => (
            <>
              <TitleCom type={item?.type} />
              <Row gutter={[16, 16]}>
                {isValidArray(item?.data)
                  ? item?.data.map((cur) => (
                      <Col span={12}>
                        <TargetCom curItem={cur} />
                      </Col>
                    ))
                  : ''}
              </Row>
            </>
          ))
        : ''}
    </div>
  )
}
