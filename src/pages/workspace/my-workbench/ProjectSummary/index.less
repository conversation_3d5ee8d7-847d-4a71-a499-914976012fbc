.projectSummary {
  .titleContent {
    margin-top: 16px !important;
    margin-bottom: 4px;
    padding-right: 4px;
    .title {
      height: 24px;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      letter-spacing: 0em;
      text-align: left;
    }
    .reportLink {
      height: 24px;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      letter-spacing: 0em;
      text-align: left;
    }
  }
}

.card {
  height: 114px;
  color: #626262;
  font-size: 12px;
  line-height: 20px;
  letter-spacing: 0em;
  text-align: left;
  .numTitle,
  .summaryLabel {
    height: 22px;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
  .num {
    margin-bottom: 4px;
  }
  .num,
  .summary {
    height: 32px;
    color: black;
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
  }
  .detail {
    height: 20px;
    font-weight: 400;
  }
  .diff {
    margin-top: 24px;
    .diffValue {
      color: #b41500;
    }
  }
  :global {
    .ant-card {
      border-radius: 4px !important;
    }
    .ant-card-body {
      padding: 8px 4px 8px 8px !important;
    }
  }
}
