import { ExperimentStatus } from '@/components/ReactionTabs/ExperimentListTab/util'
import { apiExperimentList, parseResponseResult } from '@/services'
import {
  ProjectCompound,
  ProjectCompoundStatus,
  ProjectReaction,
  ProjectRoute,
  query
} from '@/services/brain'
import { apiGetWorkbenchTotalInfo } from '@/services/workbench'
import { isNil } from 'lodash'
import { useEffect, useState } from 'react'
import { getRoutes } from '../utils'
import type { WorkbenchInfo } from './index.d'

export const useWorkbenchTotalInfo = (userId: number) => {
  const [workbenchTotalInfo, setWorkbenchTotalInfo] = useState<WorkbenchInfo>()
  const getWorkbenchTotalInfo = async () => {
    const res = await apiGetWorkbenchTotalInfo({
      routeParams: String(userId)
    })
    if (parseResponseResult(res).ok) setWorkbenchTotalInfo(res?.data)
  }
  useEffect(() => {
    getWorkbenchTotalInfo()
  }, [userId])
  return { workbenchTotalInfo }
}

export const useCompound = (userId: number, size: number = 3) => {
  const getBasicQuery = () =>
    query<ProjectCompound>('project-compounds')
      .equalTo('director_id', userId)
      .filterDeep('status', 'in', ['created', 'designing'])
      .sortBy([{ field: 'updatedAt', order: 'desc' }])

  const fetchData = async () => {
    const req = getBasicQuery()
      .paginate(1, size)
      .populateWith('project_routes', ['id'])
      .populateWith('compound', ['smiles'])
      .populateWith('project', ['id', 'no'])
      .notEqualTo('type', 'temp_block')
      .populateDeep([
        {
          path: 'retro_processes',
          fields: ['id'],
          children: [{ key: 'retro_backbones', fields: ['id'] }]
        }
      ])

    const { data, meta } = await req.get()
    data?.forEach((item) => {
      item.project_routes_number = item.project_routes?.length
      item.retro_backbones_number = item.retro_processes?.flatMap(
        (p) => p.retro_backbones
      ).length
    })
    return { data: data || [], total: meta?.pagination.total, success: !!data }
  }

  const fetchNum = async (status: ProjectCompoundStatus): Promise<number> => {
    const { meta } = await getBasicQuery()
      .equalTo('status', status)
      .paginate(1, 1)
      .get()
    return meta?.pagination.total || 0
  }

  return { fetchData, fetchNum }
}

export const useReaction = (userId: number, size: number = 3) => {
  const [allRoutes, setAllRoutes] = useState<ProjectRoute[]>()

  const fetchAllRoutes = async (userId: number) => {
    let routes = allRoutes
    if (isNil(routes)) {
      routes = await getRoutes(userId)
      setAllRoutes(routes)
    }
    return routes
  }

  const getBasicQuery = async () => {
    const allRoutes = await fetchAllRoutes(userId)
    return query<ProjectReaction>('project-reactions')
      .filterDeep(
        'project_routes.id',
        'in',
        allRoutes.length ? allRoutes.map((r) => r.id) : [-1]
      )
      .sortBy([{ field: 'updatedAt', order: 'desc' }])
  }

  const fetchData = async () => {
    const req = await getBasicQuery()
    req
      .paginate(1, size)
      .populateWith('project', ['id', 'no'])
      .populateDeep([
        {
          path: 'project_routes',
          fields: ['id', 'name'],
          children: [{ key: 'project_compound', fields: ['id'] }]
        }
      ])

    const { data, meta } = await req.get()
    return {
      data: data || [],
      total: meta?.pagination.total,
      success: !!data
    }
  }

  const fetchNum = async () => {
    const req = await getBasicQuery()

    const { meta } = await req.paginate(1, 1).get()
    return meta?.pagination.total || 0
  }

  return { fetchData, fetchNum }
}

export const useExperiment = (userId: number) => {
  const fetchNum = async (status: ExperimentStatus) => {
    const res = await apiExperimentList({
      data: {
        experiment_owner: userId,
        status: [status],
        page_no: 1,
        page_size: 1
      }
    })
    if (parseResponseResult(res).ok) {
      return res.data?.meta?.total || 0
    }
    return 0
  }

  return { fetchNum }
}
