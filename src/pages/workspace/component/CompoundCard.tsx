import LazySmileDrawer from '@/components/LazySmileDrawer'
import { priorityOptions } from '@/constants'
import { useBrainFetch } from '@/hooks/useBrainFetch'
import useOptions from '@/hooks/useOptions'
import {
  ProjectCompound,
  ProjectCompoundStatus,
  ProjectMember,
  service
} from '@/services/brain'
import { getWord, isEN } from '@/utils'
import { App, Card, Col, Row, Select, Space, Tag, Typography } from 'antd'
import cs from 'classnames'
import React, { useEffect, useState } from 'react'
import { history } from 'umi'
import { useProjectMembers } from '../utils'
import styles from './index.less'

const compoundStatusTransMap: Record<
  ProjectCompoundStatus,
  ProjectCompoundStatus[]
> = {
  created: ['designing', 'canceled'],
  designing: ['synthesizing', 'canceled'],
  synthesizing: ['finished', 'canceled'],
  finished: [],
  canceled: []
}

export interface CompoundCardProps {
  compound: ProjectCompound
}

const CompoundCard: React.FC<CompoundCardProps> = ({
  compound: initCompound
}) => {
  const { getById } = useProjectMembers()
  const [members, setMembers] = useState<ProjectMember[]>([])
  const { fetch, loading } = useBrainFetch()
  const { message } = App.useApp()
  const [projectCompound, setProjectCompound] =
    useState<ProjectCompound>(initCompound)
  const {
    compound,
    project,
    id,
    status,
    priority,
    director_id,
    no,
    type,
    retro_backbones_number,
    project_routes_number
  } = projectCompound

  useEffect(() => {
    getById(project?.id).then((ms) => setMembers(ms))
  }, [project?.id])

  useEffect(() => {
    setProjectCompound(initCompound)
  }, [initCompound])

  const update = async (compound: Partial<ProjectCompound>) => {
    const { data } = await fetch(
      service<ProjectCompound>('project-compounds').update(id, compound)
    )
    if (data) {
      message.success(getWord('success-update'))
      setProjectCompound((pre) => ({ ...pre, ...data }))
    }
  }
  const { moleculeStatusOptions, typeMap } = useOptions()
  return (
    <div className={styles.moleculeCardRoot}>
      <Card
        className={cs('enablePointer', styles.card, status && styles[status])}
        onClick={() =>
          history.push(
            `/projects/${project?.id}/compound/${id}?page=1&pageSize=10`
          )
        }
      >
        <Row>
          <Col flex="auto">
            <LazySmileDrawer
              structure={compound?.smiles || ''}
              height={300}
              className={cs('enablePointer')}
            />
          </Col>
        </Row>
        <Row justify="space-between" align={'middle'} wrap={false}>
          <Col flex="auto">
            <Typography.Text strong ellipsis={{ tooltip: no }}>
              {no}
            </Typography.Text>
          </Col>
          <Col className={styles.tagsWrapper} flex="none">
            <Tag color="green">{typeMap[type]}</Tag>
            {project?.no && <Tag color="blue">{project.no}</Tag>}
          </Col>
        </Row>
        <Row>
          <Col className={cs(styles.routeNum, styles.label)}>
            <Space>
              <div>
                {getWord('aiGenerated')} {retro_backbones_number || 0}{' '}
                {isEN() ? '' : '条'}
              </div>
              <div>•</div>
              <div>
                {getWord('myRoutes')} {project_routes_number || 0}{' '}
                {isEN() ? '' : '条'}
              </div>
            </Space>
          </Col>
        </Row>
        <div
          onClick={(e) => e.stopPropagation()}
          className={cs(styles.actionsWrapper, styles.label)}
        >
          <Row>
            <Col span={12} className={styles.actionWrapper}>
              <span className={styles.label}>{getWord('status')}</span>
              <Select
                rootClassName={styles.alignRight}
                onChange={async (status) => update({ status })}
                disabled={loading}
                size="small"
                style={{ width: '60%' }}
                value={status}
                options={moleculeStatusOptions.filter(
                  (o) =>
                    compoundStatusTransMap[status].includes(
                      o.value as ProjectCompoundStatus
                    ) || o.value === status
                )}
              />
            </Col>
            <Col span={12} className={styles.actionWrapper}>
              <div className={styles.label}>{getWord('Priority')}</div>
              <Select
                size="small"
                value={priority}
                style={{ width: '60%' }}
                options={priorityOptions}
                onChange={async (priority) => update({ priority })}
              />
            </Col>
          </Row>
          <Row>
            <Col span={24} className={styles.actionWrapper}>
              <div className={styles.label}>
                {getWord('pages.experiment.label.owner')}
              </div>
              <Select
                disabled={loading}
                size="small"
                value={director_id}
                style={{ width: '100%' }}
                options={members.map((m) => ({
                  label: m.user_info?.username,
                  value: m.user_id
                }))}
                onChange={async (user_id) => update({ director_id: user_id })}
              />
            </Col>
          </Row>
        </div>
      </Card>
    </div>
  )
}

export default CompoundCard
