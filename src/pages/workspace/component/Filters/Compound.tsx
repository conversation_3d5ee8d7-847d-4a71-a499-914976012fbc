import { getWord } from '@/utils'
import {
  ProFormSelectProps,
  RequestOptionsType
} from '@ant-design/pro-components'
import { DefaultOptionType } from 'antd/es/select'
import { useEffect, useState } from 'react'

import { getProjectCompoundOptions } from '../../utils'

export const useCompoundFilterProps = (
  userId?: number,
  projectId?: number,
  valueByNo?: boolean
): ProFormSelectProps => {
  const [input, setInput] = useState<string>()
  const [options, setOptions] = useState<DefaultOptionType[]>([])

  const [initOptions, setInitOptions] = useState<DefaultOptionType[]>([])

  useEffect(() => {
    getProjectCompoundOptions(userId, '', projectId, valueByNo).then((ops) => {
      setInitOptions(ops)
    })
  }, [])

  return {
    name: valueByNo ? 'no' : 'id',
    placeholder: getWord('enter-select-tip'),
    request: async ({ userId, input, projectId }) => {
      const re = await getProjectCompoundOptions(
        userId,
        input,
        projectId,
        valueByNo
      )
      setOptions(re) // fix the antd pro light select issue that the options will be empty when requesting data
      return re as RequestOptionsType[]
    },
    params: { userId, input, projectId },
    options,
    debounceTime: 100,
    fieldProps: {
      onSearch: setInput,
      onDropdownVisibleChange: (open) => {
        if (!open && initOptions.length) setOptions(initOptions)
      },
      onClick: (e) => e.stopPropagation(),
      resetAfterSelect: true,
      showSearch: true
    }
  }
}
