import { ReactComponent as SortSvg } from '@/assets/svgs/sort.svg'
import cs from 'classnames'
import React, { useState } from 'react'
import styles from './index.less'

export type SortOrderValue = 'asc' | 'desc'

export interface SortOrderProps {
  value?: SortOrderValue
  onChange?: (value: SortOrderValue) => void
}

const SortOrder: React.FC<SortOrderProps> = ({ value = 'asc', onChange }) => {
  const [order, setOrder] = useState<SortOrderValue>(value)

  const updateOrder = (order: SortOrderValue) => {
    setOrder(order)
    onChange?.(order)
  }

  return (
    <SortSvg
      value={value}
      className={cs(styles.sortButton, { [styles.desc]: order === 'desc' })}
      onClick={() => updateOrder(value === 'asc' ? 'desc' : 'asc')}
    />
  )
}

export default SortOrder
