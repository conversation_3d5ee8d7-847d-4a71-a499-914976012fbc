@import '@/style/variables.less';
.moleculeCardRoot {
  min-height: 426px;
  margin: 4px;
  .card {
    :global .ant-card-body {
      padding: 4px 12px;
    }
    &.created {
      border: 1px solid @color-border-created;
    }
    &.designing {
      border: 1px solid @color-border-designing;
    }
    &.synthesizing {
      border: 1px solid @color-border-synthsizing;
    }
    &.finished {
      border: 1px solid @color-border-finished;
    }
    &.canceled {
      border: 1px solid @color-border-canceled;
    }
  }

  .routeNum {
    min-width: 38px;
  }
  .label {
    min-width: 48px;
    margin: 4px 0;
    color: #626262;
    font-weight: 400;
    font-size: 14px;
  }
  .actionsWrapper {
    cursor: auto;
    .actionWrapper {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: space-between;
      padding-right: 4px;
    }
  }
  .tagsWrapper {
    :global {
      .ant-tag:last-child {
        margin-right: 0;
      }
    }
  }
  .alignRight {
    margin-left: auto;
  }

  .desItem {
    min-width: 206px;
    padding-bottom: 8px;
    overflow-x: hidden;
    :global {
      .ant-tag {
        margin-inline-end: 0px !important;
      }
    }
  }
  .routesAmount {
    .desItem;
    padding-bottom: 0px;
  }
  :global {
    .ant-card-head {
      padding: 0 12px;
    }
    .ant-card-body {
      padding: 4px 16px 2px;
    }
  }
  .moleculeNo {
    color: black;
    font-weight: 500;
    font-size: 16px;
  }
  .valueItem {
    color: @brand-primary;
  }
  .normalText {
    color: black;
  }
}
