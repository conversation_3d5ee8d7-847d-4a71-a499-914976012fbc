import {
  Project,
  ProjectCompound,
  ProjectMember,
  ProjectRoute,
  query
} from '@/services/brain'
import { RequestOptionsType } from '@ant-design/pro-components'
import { DefaultOptionType } from 'antd/es/select'
import { isNil, uniqBy } from 'lodash'

export const getProjectOptions = async (
  userId: number,
  valueAsString?: boolean
): Promise<RequestOptionsType[]> => {
  const { data: members } = await query<ProjectMember>('project-members')
    .equalTo('user_id', userId)
    .populateWith('project', ['id', 'no'])
    .paginate(1, 1000)
    .get()
  return uniqBy(
    members?.map((c) => c.project).filter((p) => !!p) as Project[],
    'id'
  ).map((p) => ({ value: valueAsString ? `${p.id}` : p.id, label: p.no }))
}

export const getProjectCompoundOptions = async (
  userId?: number,
  input: string = '',
  projectId?: number,
  valueByNo?: boolean
): Promise<DefaultOptionType[]> => {
  if (!userId || projectId === 0) return []
  const req = query<ProjectCompound>('project-compounds', {}, ['no'])
    .equalTo('director_id', userId)
    .notEqualTo('type', 'temp_block')
    .contains('no', input)
    .paginate(1, 1000)
  if (!isNil(projectId)) {
    req.filterDeep('project.id', 'eq', projectId)
  }
  const { data } = await req.get()
  const compounds = data || []
  if (valueByNo) {
    return uniqBy(compounds, (c) => c.no).map((c) => ({
      value: c.no,
      label: c.no
    }))
  }
  return compounds.map((c) => ({ value: c.id, label: c.no }))
}

export const getRoutes = async (
  userId?: number,
  projectId?: number,
  compoundNo?: string
) => {
  if (!userId) return []
  const req = query<ProjectRoute>('project-routes', {}, ['name'])
    .equalTo('status', 'confirmed')
    .filterDeep('project_compound.director_id', 'eq', userId)
    .populateWith('project_compound', ['id', 'no'])
    .paginate(1, 1000)
  if (projectId) {
    req.filterDeep('project_compound.project.id', 'eq', projectId)
  }
  if (compoundNo) {
    req.filterDeep('project_compound.no', 'eq', compoundNo)
  }
  const { data } = await req.get()
  return data || []
}

export const useProjectMembers = () => {
  const cached: Record<number, Promise<ProjectMember[]>> = {}

  const getById = async (id?: number): Promise<ProjectMember[]> => {
    if (!id) return []
    if (!cached[id]) {
      cached[id] = query<Project>('projects')
        .equalTo('id', id)
        .populateWith('project_members')
        .get()
        .then((r) => uniqBy(r.data?.[0]?.project_members || [], 'user_id'))
    }
    return cached[id]
  }

  return { getById }
}
