import { getWord } from '@/utils'
import { HeartTwoTone, SmileTwoTone } from '@ant-design/icons'
import { PageContainer } from '@ant-design/pro-components'
import { Al<PERSON>, Card, Typography } from 'antd'
import React from 'react'

const Admin: React.FC = () => {
  return (
    <PageContainer content={getWord('pages.admin.subPage.title')}>
      <Card>
        <Alert
          message={getWord('pages.welcome.alertMessage')}
          type="success"
          showIcon
          banner
          style={{
            margin: -12,
            marginBottom: 48
          }}
        />
        <Typography.Title level={2} style={{ textAlign: 'center' }}>
          <SmileTwoTone /> Ant Design Pro{' '}
          <HeartTwoTone twoToneColor="#eb2f96" /> You
        </Typography.Title>
      </Card>
      <p style={{ textAlign: 'center', marginTop: 24 }}>
        Want to add more pages? Please refer to{' '}
        <a
          href="https://pro.ant.design/docs/block-cn"
          target="_blank"
          rel="noopener noreferrer"
        >
          use block
        </a>
        。
      </p>
    </PageContainer>
  )
}

export default Admin
