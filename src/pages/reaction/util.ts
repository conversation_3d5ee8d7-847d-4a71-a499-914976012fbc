import {
  MaterialTable,
  Procedure,
  ProcedureForCreate,
  ProcedureRxnMatchResult,
  ReactionRole,
  RetroReaction,
  queryWithDefaultOrder
} from '@/services/brain'
import { AiProcedure } from '@/types/Procedure'
import { isNil } from 'lodash'
import { Reaction } from '../route/util'

export const getReactionFromRxn = (rxn: string): Reaction => {
  let reactants: string[] = []
  let product = ''
  if (rxn.includes('>>')) {
    let [reactentsStr, productStr] = rxn.split('>>')
    reactants = reactants.concat(reactentsStr.split('.'))
    product = productStr
  } else {
    let [reactentsStr, midReactentsStr, productStr] = rxn.split('>')
    reactants = reactants.concat(
      reactentsStr.split('.'),
      midReactentsStr.split('.')
    )
    product = productStr
  }
  return { product, reactants }
}

export const defaultReferenceType = 'C12'

export const aiProcedureToProcedure = (
  ai: AiProcedure,
  userId: string,
  referenceType: string = defaultReferenceType
): Procedure => ({
  id: ai.id,
  yields: ai.yieldNumber,
  smiles: ai.query || '',
  procedure: ai.text || ai.experimentalProcedure || '',
  material_table: [],
  reference_type: referenceType,
  reference_smiles: ai.rxn,
  created_by: userId,
  origin: ai
})

const reactionRoleOrder: Record<ReactionRole, number> = {
  product: 1,
  main_reactant: 2,
  reactant: 3,
  other_reagent: 4,
  solvent: 5,
  catalyst: 6
}

export const sortMaterialTable = (table: MaterialTable[]): MaterialTable[] => {
  return table.sort(
    (a, b) => reactionRoleOrder[a.role] - reactionRoleOrder[b.role]
  )
}

export const updateMaterialTable = (
  rxn: string,
  roleMap: Record<string, ReactionRole>,
  oldTables?: MaterialTable[]
): MaterialTable[] => {
  const reaction = getReactionFromRxn(rxn)
  return sortMaterialTable(
    [reaction.product, ...reaction.reactants].map((smiles) => {
      const old = oldTables?.find((m) => m.smiles === smiles)
      return {
        smiles,
        role: roleMap[smiles] || 'other_reagent',
        no: '',
        equivalent: old?.equivalent || 1,
        unit: old?.unit || 'eq'
      }
    })
  )
}

const addNoForProcedureTable = (table: MaterialTable[]): MaterialTable[] =>
  sortMaterialTable(table).map((t, index) => ({
    ...t,
    no: `R${index}`
  }))

const getSmilesFromMaterialTable = (table: MaterialTable[]): string => {
  const items = sortMaterialTable(table)
  const reactants = items.filter((m) => m.role !== 'product')
  const product = items.filter((m) => m.role === 'product')
  return `${reactants.map((m) => m.smiles).join('.')}>>${product
    .map((m) => m.smiles)
    .join('.')}`
}

export const formatProcedureForCreate = (
  p: Procedure,
  userId: string
): ProcedureForCreate => ({
  yields: p.yields,
  rxn_yields: isNil(p.yields) ? '' : `${p.yields}%`,
  smiles: getSmilesFromMaterialTable(p.material_table),
  procedure: p.procedure,
  material_table: addNoForProcedureTable(p.material_table),
  reference_type: defaultReferenceType,
  reference_smiles: p.reference_smiles,
  created_by: userId
})

export const fetchAllRetroReactions = async (
  reaction: Reaction,
  idOnly: boolean = false,
  findProps?: (keyof RetroReaction)[]
) => {
  const props = (idOnly ? ['id'] : findProps ? findProps : undefined) as
    | (keyof RetroReaction)[]
    | undefined
  const { data, error } = await queryWithDefaultOrder<RetroReaction>(
    'retro-reactions/list',
    { method: 'post', data: { filters: reaction } },
    props
  )
    .paginate(1, 1000)
    .get()
  if (error || !data) return []
  return data
}

export const matchResultToProcedure = (
  result: ProcedureRxnMatchResult
): Procedure => ({
  id: result.id,
  yields: result.yields || undefined,
  smiles: result.rxn,
  procedure: result.procedure || '',
  material_table: result.material_table || [],
  reference_type: result.reference_type,
  created_by: result.created_by || '',
  last_update_time: result.last_update_time || ''
})
