import SmilesInput from '@/components/SmilesInput'
import { reactionUnitOptions } from '@/constants'
import { useBrainFetch } from '@/hooks/useBrainFetch'
import useOptions from '@/hooks/useOptions'
import { getRxnFromMaterialTable } from '@/pages/route/util'
import { apiCreateExperimentDesigns } from '@/services'
import {
  MaterialTable,
  Procedure,
  ProcedureRoleResponse,
  ProjectReaction,
  query,
  service
} from '@/services/brain'
import { getWord } from '@/utils'
import { updateRoleMap } from '@/utils/reactionRoles'
import {
  ModalForm,
  ProForm,
  ProFormDigit,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
  ProFormText,
  ProFormTextArea
} from '@ant-design/pro-components'
import { useModel } from '@umijs/max'
import { App, Button, Form } from 'antd'
import React, { useEffect, useState } from 'react'
import {
  formatProcedureForCreate,
  getReactionFromRxn,
  updateMaterialTable
} from '../util'
import './index.less'
import { addOtherToTable } from './util'

export interface MyReactionDialogProps {
  projectReaction: ProjectReaction
  procedure?: Procedure
  mode?: 'create' | 'edit' | 'reference'
  open?: boolean
  onOpenChange?: (open: boolean) => void
  onCreated?: (projectReaction: ProjectReaction) => void
  projectId?: number
}

const getProjectReaction = async (rxn: string, projectId: number) => {
  const data = await query<ProjectReaction>('project-reaction/get-or-create', {
    method: 'post',
    data: { reaction: rxn, projectId },
    normalizeData: false
  }).get()

  return (data as unknown as ProjectReaction[])?.[0]
}

const MyReactionDialog: React.FC<MyReactionDialogProps> = ({
  projectReaction,
  procedure: propProcedure,
  open,
  mode,
  onOpenChange,
  onCreated,
  projectId
}) => {
  const { reactionRoleOptions } = useOptions()
  const [procedure, setProcedure] = useState<Procedure | undefined>(
    propProcedure
  )
  const { notification } = App.useApp()

  const [form] = Form.useForm<Procedure>()
  const { fetch: fetchRoles } = useBrainFetch(undefined, false)
  const { fetch: createProcess } = useBrainFetch(undefined, false)
  const { reactants, product } = getReactionFromRxn(projectReaction.reaction)
  const { initialState } = useModel('@@initialState')
  const [loading, setLoading] = useState<boolean>(false)

  const createProcedure = async (procedure: Procedure) => {
    const formatedProcedure = formatProcedureForCreate(
      procedure,
      `${initialState?.userInfo?.id || -1}`
    )
    const { data } = await createProcess(
      query<number>('procedure/create', {
        normalizeData: false,
        method: 'post',
        data: { procedure: formatedProcedure, searchable: false }
      }).get()
    )
    if (!data) return

    let newEffective = new Set<number>(projectReaction.effective_procedures)
    let newHistory = new Set<number>(projectReaction.history_procedures)
    if (projectId) {
      const latest = await getProjectReaction(
        projectReaction.reaction,
        projectId
      )
      newEffective = new Set(latest.effective_procedures)
      newHistory = new Set(latest.history_procedures)
    }
    newEffective.add(data as unknown as number)
    const oldProcedureId = propProcedure?.id
    if (oldProcedureId) {
      newEffective.delete(oldProcedureId)
      newHistory.add(oldProcedureId)
    }

    const { data: newProjectReaction } = await service<ProjectReaction>(
      'project-reactions'
    ).update(projectReaction.id, {
      effective_procedures: Array.from(newEffective.values()),
      history_procedures: Array.from(newHistory.values())
    })

    const material_mapping = (
      (await query('procedure/material_replacement', {
        method: 'post',
        data: {
          rxn: getRxnFromMaterialTable(procedure.material_table),
          reference: propProcedure?.reference_smiles,
          procedure: procedure.procedure
        },
        normalizeData: false
      })
        .get()
        .catch()) as unknown as { ReactantMap: any }
    )?.ReactantMap

    try {
      await apiCreateExperimentDesigns({
        data: {
          rxn_no: data,
          rxn: formatedProcedure.smiles,
          project_no: projectReaction.project?.id,
          reference_text: formatedProcedure.procedure,
          material_mapping: material_mapping || {},
          materials: formatedProcedure.material_table
        }
      })
    } catch (e) {
      console.error(`create experiment design failed with ${e}`)
    }
    if (newProjectReaction) {
      onCreated?.(newProjectReaction)
    }
  }

  const updateRxn = async (rxn: string, addCatalyst: boolean = false) => {
    const { data } = await fetchRoles(
      query<ProcedureRoleResponse>('procedure/role', {
        normalizeData: false,
        method: 'post',
        data: { reaction_smiles: rxn }
      }).get()
    )

    if (!data) return
    const roleMap = updateRoleMap(
      (data as unknown as ProcedureRoleResponse).role,
      getReactionFromRxn(projectReaction.reaction).reactants
    )
    if (roleMap[product] !== 'product') {
      notification.error({
        message: getWord('pages.reaction.label.warn.productDiff')
      })
      return
    }
    if (reactants.some((v) => !roleMap[v])) {
      notification.error({
        message: getWord('pages.reaction.label.warn.materialDiff')
      })
      return
    }

    const material_table = updateMaterialTable(
      rxn,
      roleMap,
      procedure?.material_table
    )

    if (!addCatalyst || !propProcedure?.procedure) {
      setProcedure((pre) =>
        pre
          ? { ...pre, smiles: rxn, material_table, temp_smiles: '' }
          : undefined
      )
      return
    }

    const addedTable = await addOtherToTable(material_table, propProcedure)

    setProcedure((pre) =>
      pre
        ? { ...pre, smiles: rxn, material_table: addedTable, temp_smiles: '' }
        : undefined
    )
  }

  useEffect(() => {
    if (procedure) {
      if (!procedure?.material_table?.length && procedure.smiles) {
        setLoading(true)
        updateRxn(procedure.smiles, true).finally(() => setLoading(false))
      } else {
        form.setFieldsValue(procedure)
      }
    }
  }, [procedure])

  useEffect(() => {
    setProcedure(propProcedure)
  }, [propProcedure])

  return (
    <ModalForm<Procedure>
      className="reaction-dialog-root"
      title={getWord(`pages.reaction.label.${mode}Reaction`)}
      form={form}
      width={1000}
      autoFocusFirstInput
      open={open}
      disabled={loading}
      onOpenChange={onOpenChange}
      modalProps={{ destroyOnClose: true, centered: true }}
      submitTimeout={2000}
      onFinish={async (values) => {
        await createProcedure(values)
        return true
      }}
    >
      <ProFormDigit
        width="md"
        name="yields"
        label={getWord('yield')}
        min={0}
        max={100}
        fieldProps={{ precision: 0, addonAfter: '%' }}
      />
      <ProFormText
        name="temp_smiles"
        label={getWord('pages.reaction.label.createMaterialTableFromRxn')}
        fieldProps={{ style: { width: 900 } }}
        addonAfter={
          <>
            <Button
              type="link"
              onClick={() => {
                updateRxn(form.getFieldValue('temp_smiles'))
              }}
            >
              {getWord('pages.route.edit.label.confirm')}
            </Button>
          </>
        }
      />
      <ProFormList
        name="material_table"
        label={getWord('material-sheet')}
        creatorButtonProps={{
          creatorButtonText: getWord('add-raw-materials')
        }}
        copyIconProps={false}
        actionRender={(field, _, defaultActionDom) => {
          const item = form.getFieldValue('material_table')[
            field.name
          ] as MaterialTable
          if (item.smiles === product || reactants.includes(item.smiles)) {
            return []
          }
          return defaultActionDom
        }}
      >
        {(field) => {
          const item = form.getFieldValue('material_table')[
            field.name
          ] as MaterialTable
          const disabled =
            item.smiles === product || reactants.includes(item.smiles)
          return (
            <ProFormGroup key="group">
              <ProFormSelect
                disabled={disabled}
                name="role"
                width={150}
                label={getWord('role')}
                options={reactionRoleOptions.filter(
                  (r) => r.value !== 'product'
                )}
                required
                rules={[{ required: true }]}
              />
              <ProForm.Item
                className="filter-form-root"
                name={'smiles'}
                label={getWord('structural')}
                required
                rules={[{ required: true }]}
              >
                <SmilesInput disabled={disabled} multiple={false} />
              </ProForm.Item>
              <ProFormDigit
                name="equivalent"
                label={getWord('EWR')}
                required
                rules={[
                  { required: true },
                  {
                    pattern: /^(?!0*(\.0{1,2})?$)\d+(\.\d{1,2})?$/,
                    message: getWord('enter-two-decimal')
                  }
                ]}
              />
              <ProFormSelect
                name="unit"
                label={getWord('unit')}
                options={reactionUnitOptions}
                required
                rules={[{ required: true }]}
              />
            </ProFormGroup>
          )
        }}
      </ProFormList>
      <ProFormTextArea
        disabled={false}
        name="procedure"
        label="Procedure"
        required={false}
        fieldProps={{
          autoSize: { minRows: 5, maxRows: 8 }
        }}
      />
    </ModalForm>
  )
}

export default MyReactionDialog
