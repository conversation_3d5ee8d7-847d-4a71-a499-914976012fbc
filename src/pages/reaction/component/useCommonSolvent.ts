import { query, SpecialMaterial } from '@/services/brain'
import { useEffect, useState } from 'react'

export const useFilteredMaterials = (): {
  smiles: Set<string>
  names: Set<string>
} => {
  const [materials, setMaterials] = useState<SpecialMaterial[]>([])

  const fetchSpecialMaterials = async (): Promise<SpecialMaterial[]> => {
    const { data } = await query<SpecialMaterial>('special-materials')
      .filterDeep('type', 'in', [
        'common_solvent',
        'inert_gas',
        'other_materials_not_be_auto_extracted_in_material_table'
      ])
      .paginate(1, 1000)
      .get()
    return data || []
  }

  useEffect(() => {
    let unmount = false
    fetchSpecialMaterials().then((css) => {
      if (!unmount) setMaterials(css)
    })

    return () => {
      unmount = true
    }
  }, [])

  return {
    smiles: new Set(materials.map((s) => s.canonical_smiles)),
    names: new Set(materials.map((s) => s.synonyms || []).flat())
  }
}
