import { MaterialTable, Procedure, query } from '@/services/brain'
import { ProcedureToTextResponse } from '@/services/brain/types/procedureToText'

export const addOtherToTable = async (
  material_table: MaterialTable[],
  propProcedure: Procedure
): Promise<MaterialTable[]> => {
  const procedureText: ProcedureToTextResponse = (await query(
    'procedure_parse/from-text',
    {
      method: 'post',
      data: {
        procedure: propProcedure.procedure,
        rxn: propProcedure.reference_smiles,
        patent_id: propProcedure.origin.reference.no,
        title: propProcedure.origin.reference.title,
        rxn_id: propProcedure.id || -1
      },
      normalizeData: false
    }
  )
    .get()
    .catch()) as unknown as ProcedureToTextResponse

  if (!procedureText || !procedureText.entities) {
    console.log(`No procedureText, skip add others to material table`)
    return material_table
  }

  const allOthers = procedureText.entities.filter((e) => e.role === 'other')

  if (!allOthers.length) {
    return material_table
  }

  const newMaterial: MaterialTable[] = []

  allOthers.forEach((n) => {
    const s = n.canonicalized_smiles || n.normalized_smiles
    if (!s) {
      console.warn(`not smiles for ${n.name}, skip add to material table`)
      return
    }
    const smiles = s.split('.').sort().join('.')

    newMaterial.push({
      smiles,
      role: 'other_reagent',
      no: '',
      equivalent: 1,
      unit: 'eq'
    })
  })

  return [...material_table, ...newMaterial]
}
