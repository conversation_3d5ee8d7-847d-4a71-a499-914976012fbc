.reaction-dialog-root {
  .product-equivalent {
    display: none;
  }
  .ant-pro-form-list-action {
    align-self: baseline;
  }
  .smiles-list {
    .ant-upload-list-picture-card-container,
    .ant-upload-select-picture-card {
      width: 60px;
      height: 60px;
    }
    &.reaction-list .ant-upload-list-picture-card-container {
      width: fit-content;
    }
    .ant-upload-list-picture-card
      .ant-upload-list-item-file
      + .ant-upload-list-item-name {
      display: none !important; // FIXME
    }
    .add-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }
    &.hide-upload-btn {
      .ant-upload {
        display: none;
      }
    }
  }
  .ant-form-item-control-input-content {
    .ant-pro-form-list-item:first-child {
      .ant-form-item {
        margin-bottom: 0;
      }
      .ant-col.ant-form-item-control {
        height: 0;
        opacity: 0;
      }
    }
  }
}
