import LazySmileDrawer from '@/components/LazySmileDrawer'
import { getDetectRecordTabConfig } from '@/components/ReactionTabs/DetectRecord'
import { getExperimentListTabConfig } from '@/components/ReactionTabs/ExperimentListTab'
import { getMyReactionDesignTabConfig } from '@/components/ReactionTabs/MyReactionDesignTab'
import ReactionDesignCard from '@/components/ReactionTabs/MyReactionDesignTab/ReactionDesignCard'
import ReactionLibTab from '@/components/ReactionTabs/ReactionLibTab'
import ProcedureCardInDetailPage from '@/components/ReactionTabs/ReactionLibTab/ProcedureCardInDetailPage'
import { getRetroReactionTabConfig } from '@/components/ReactionTabs/RetroReactionTab'
import { useBrainFetch } from '@/hooks/useBrainFetch'
import { usePathNumParam } from '@/hooks/usePathNumParam'
import { Procedure, ProjectReaction, service } from '@/services/brain'
import { AiProcedure } from '@/types/Procedure'
import { getWord } from '@/utils'
import { PageContainer, ProTable } from '@ant-design/pro-components'
import { useModel, useSearchParams } from '@umijs/max'
import { Button, Col, Row, Space, Switch, TabsProps } from 'antd'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { useAccess } from 'umi'
import {
  Reaction,
  getDeepthMap,
  getIdOfReaction,
  getNameOfReaction
} from '../route/util'
import MyReactionDialog from './component/MyReactionDialog'
import './index.less'
import {
  aiProcedureToProcedure,
  defaultReferenceType,
  getReactionFromRxn
} from './util'

type Tab = Exclude<TabsProps['items'], undefined>[number]
const ReactionDetail: React.FC = ({}) => {
  const access = useAccess()
  const id = usePathNumParam('reactionId')
  const [searchParams, setSearchParams] = useSearchParams()
  const [tab, setTab] = useState<string>(searchParams.get('tab') as string)
  const { fetch, loading } = useBrainFetch()
  const [projectReaction, setProjectReaction] = useState<ProjectReaction>()
  const [reaction, setReaction] = useState<Reaction>()
  const ref = useRef<HTMLDivElement>()
  const { initialState } = useModel('@@initialState')
  const [editingProcedure, setEditingProcedure] = useState<Procedure>()
  const [action, setAction] = useState<'create' | 'edit' | 'reference'>(
    'create'
  )
  const [open, setOpen] = useState<boolean>(false)
  const [showCanceledDesign, setShowCanceledDesign] = useState<boolean>(false)
  const [updateEvent, setUpdateEvent] = useState<Record<never, never>>({})

  const fetchProjectReaction = async (id: number) => {
    const { data } = await fetch(
      service<ProjectReaction>('project-reactions')
        .selectManyByID([id])
        .populateWith('project_routes', ['main_tree', 'status', 'name'])
        .populateWith('project', ['id'])
        .get()
    )
    return data
  }
  const updateReaction = async (id: number) => {
    await fetchProjectReaction(id).then((data) => {
      if (data?.length) {
        setProjectReaction(data[0])
        setReaction(getReactionFromRxn(data[0].reaction))
      }
    })
  }
  const onReference = async (procedure: AiProcedure): Promise<void> => {
    setEditingProcedure(
      aiProcedureToProcedure(procedure, `${initialState?.userInfo?.id || -1}`)
    )
    setAction('reference')
    setOpen(true)
  }
  const onCreate = async (): Promise<void> => {
    setEditingProcedure({
      smiles: projectReaction?.reaction || '',
      procedure: '',
      material_table: [],
      reference_type: defaultReferenceType,
      created_by: `${initialState?.userInfo?.id || -1}`
    })
    setAction('create')
    setOpen(true)
  }
  const onEdit = async (procedure: Procedure): Promise<void> => {
    setEditingProcedure(procedure)
    setAction('edit')
    setOpen(true)
  }
  const onDelete = async (procedure: Procedure): Promise<void> => {
    if (!projectReaction || !procedure.id) return
    const newEffective = new Set<number>(projectReaction.effective_procedures)
    newEffective.delete(procedure.id)
    const newHistory = new Set<number>(projectReaction.history_procedures)
    newHistory.add(procedure.id)

    const { data: newProjectReaction } = await service<ProjectReaction>(
      'project-reactions'
    ).update(projectReaction.id, {
      effective_procedures: Array.from(newEffective.values()),
      history_procedures: Array.from(newHistory.values())
    })
    if (newProjectReaction) {
      updateReaction(id)
    }
  }

  useEffect(() => {
    updateReaction(id)
  }, [id])

  const reactionLibTab: Tab = {
    key: 'reaction-lib',
    label: <>{getWord('reaction-lib')}</>,
    children: reaction ? (
      <ReactionLibTab
        reaction={reaction}
        renderer={(procedure, isSame) => (
          <ProcedureCardInDetailPage
            procedure={procedure}
            isSame={isSame}
            onReference={onReference}
            projectId={projectReaction?.project?.id || 0}
            reactionId={id}
          />
        )}
        actionSlot={tab === 'reaction-lib' ? ref.current : undefined}
      />
    ) : null
  }
  const myReactionDesignTab: Tab = useMemo(
    () =>
      getMyReactionDesignTabConfig(
        reaction,
        projectReaction,
        (p, d) => (
          <ReactionDesignCard
            key={d.id}
            procedure={p}
            design={d}
            projectReactionId={id}
            onEdit={onEdit}
            onDelete={onDelete}
            onUpdated={(action) => {
              updateReaction(id)
              setUpdateEvent({})
              if (action === 'create') setTab('my-experiment')
            }}
          />
        ),
        showCanceledDesign
      ),
    [reaction, projectReaction, showCanceledDesign]
  )
  const experimentListTab: Tab = useMemo(
    () =>
      getExperimentListTabConfig(
        projectReaction?.project?.id,
        projectReaction?.id,
        updateEvent
      ),
    [projectReaction, updateEvent]
  )

  const detectRecordTab: Tab = useMemo(
    () =>
      getDetectRecordTabConfig(
        projectReaction?.experimentNo,
        projectReaction?.project?.id,
        projectReaction?.id,
        updateEvent
      ),
    [projectReaction, updateEvent]
  )

  const retroReactionTab: Tab = useMemo(
    () => getRetroReactionTabConfig(reaction),
    [reaction]
  )

  const tabs = useMemo(() => {
    const tabs = []
    const authCodeList = access?.authCodeList as string[]
    if (authCodeList.includes('reaction-detail.tab.reactionLib'))
      tabs.push(reactionLibTab)
    if (authCodeList.includes('reaction-detail.tab.myReactionDesign'))
      tabs.push(myReactionDesignTab)
    if (authCodeList.includes('projects_reaction_experiment'))
      tabs.push(experimentListTab)
    if (authCodeList.includes('reaction-detail.tab.retroReaction'))
      tabs.push(retroReactionTab)
    if (authCodeList.includes('reaction-detail.tab.analysisRecord'))
      tabs.push(detectRecordTab)
    if (!tab) setTab(tabs[0]?.key)
    return tabs as Tab[]
  }, [
    access?.authCodeList,
    reactionLibTab,
    myReactionDesignTab,
    experimentListTab,
    retroReactionTab
  ])

  useEffect(() => {
    let defaultExperimentNo = searchParams.get('experimentNo')
    if (defaultExperimentNo) {
      setSearchParams(
        { tab, experimentNo: defaultExperimentNo },
        { replace: true }
      )
    } else {
      setSearchParams({ tab }, { replace: true })
    }
  }, [tab])

  const reactionContent = (
    <>
      <Row>
        <Col md={8} lg={6}>
          {getWord('reaction-no')}: {id}
          {projectReaction?.reaction ? (
            <LazySmileDrawer
              structure={projectReaction?.reaction}
              clickToCheckDetail
              dbClickToCopy
            />
          ) : (
            '无反应式'
          )}
        </Col>
        <Col md={16} lg={18}>
          <ProTable
            className="route-table-root"
            ghost
            size="small"
            search={false}
            pagination={{
              defaultPageSize: 5,
              showSizeChanger: false,
              simple: true
            }}
            columns={[
              {
                title: getWord('pages.reaction.label.name'),
                dataIndex: 'name',
                render: (dom, route) => (
                  <Button
                    type="link"
                    onClick={() => {
                      window.open(`/route/view/${route.id}`, '_blank')
                    }}
                  >
                    {dom}
                  </Button>
                )
              },
              {
                title: getWord('pages.reaction.label.status'),
                dataIndex: 'status',
                valueEnum: {
                  editing: {
                    text: getWord('pages.reaction.statusLabel.editing')
                  },
                  confirmed: {
                    text: getWord('pages.reaction.statusLabel.confirmed')
                  },
                  finished: {
                    text: getWord('component.notification.statusValue.success')
                  },
                  canceled: {
                    text: getWord('pages.reaction.statusLabel.canceled')
                  }
                }
              },
              {
                title: getWord('pages.reaction.label.stepName'),
                render: (_, { main_tree }) => {
                  if (!reaction || !main_tree) return '-'
                  const id = getIdOfReaction(reaction, main_tree)
                  const deepthMap = getDeepthMap(main_tree)
                  const deepth = id && deepthMap.get(id)
                  if (deepth) {
                    return getNameOfReaction(deepth[0], deepth[1])
                  }
                  return '-'
                }
              }
            ]}
            headerTitle={getWord('routes-citing')}
            showHeader={true}
            dataSource={projectReaction?.project_routes?.filter(
              (r) => r.status === 'confirmed'
            )}
          />
        </Col>
      </Row>
    </>
  )

  return (
    <PageContainer
      className="reaction-page-root"
      content={reactionContent}
      loading={loading}
      fixedHeader={false}
      tabList={tabs}
      tabProps={{ destroyInactiveTabPane: true }}
      onTabChange={(tab) => setTab(tab)}
      tabActiveKey={tab}
      tabBarExtraContent={
        <Space>
          {tab === 'my-reaction-design' && (
            <>
              {access?.authCodeList?.includes(
                'reaction-detail.myReactionDesign.newMyReaction'
              ) && (
                <Button type="primary" size="middle" onClick={onCreate}>
                  {getWord('new-my-reaction')}
                </Button>
              )}
              <Switch
                checkedChildren={getWord('all-my-reactions')}
                unCheckedChildren={getWord('all-my-reactions')}
                checked={showCanceledDesign}
                onChange={(checked) => setShowCanceledDesign(checked)}
              />
            </>
          )}
          <div ref={(e) => (ref.current = e || undefined)} />
        </Space>
      }
    >
      {projectReaction && (
        <MyReactionDialog
          projectReaction={projectReaction}
          procedure={editingProcedure}
          open={open}
          mode={action}
          onOpenChange={(open) => {
            setOpen(open)
            if (!open) setEditingProcedure(undefined)
          }}
          onCreated={async (p) => {
            setOpen(false)
            await updateReaction(p.id)
            if (action === 'reference') {
              setTab('my-reaction-design')
            }
          }}
        />
      )}
    </PageContainer>
  )
}

export default ReactionDetail
