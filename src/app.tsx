import LabwiseAvatar from '@/assets/images/labwise-avatar.png'
import Footer from '@/components/GlobalFooter'
import LoadingTip from '@/components/LoadingTip'
import { IconMap } from '@/components/MenuIcon'
import { Notification } from '@/components/Notification'
import { currentMicrosoftUser, updateEnvConfig } from '@/services'
import {
  getEnvConfig,
  getToken,
  isValidArray,
  setToken,
  toLoginPage
} from '@/utils'
import Icon from '@ant-design/icons'
// import jp form 'jsonpath'
import type { Settings as LayoutSettings } from '@ant-design/pro-components'
import { FloatButton } from 'antd'
import { flushSync } from 'react-dom'
import type { RunTimeLayoutConfig } from 'umi'
import { history } from 'umi'
import defaultSettings from '../config/defaultSettings'
import {
  AvatarDropdown,
  AvatarName
} from './components/RightContent/AvatarDropdown'
import { errorConfig } from './requestErrorConfig'
const loginPath = '/user/login'
// const { mockUserData } = require('@/mock')
/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { isEqual, isNil } from 'lodash'
import { useEffect } from 'react'
import ErrorBoundary from './components/ErrorBoundary'
import Labbot from './components/LabBot'
import UserGuide from './components/UserGuide'
import UserSettings from './components/UserSettings'
import Watermark from './components/WaterMark'
import { apiGetUserInfo, parseResponseResult } from './services'
import { query } from './services/brain'
import { LangData } from './types/common'
import { handleMenu } from './utils/menu'
import { get, removeLocalValue, set } from './utils/storage'

let curLocalAuthCodeList = get('localAuthCodeList')
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>
  currentUser?: API.CurrentUser
  userInfo: API.NewUserInfo // IUpdateInfo //
  loading?: boolean
  isMenuCollapsed?: boolean
  queryUserInfo?: () => unknown // IUpdateInfo | null // | Promise<API.NewUserInfo | undefined>
}> {
  const getLangInfo = async () => {
    const { data: locales, error: langError } = await query(
      'ui-18ns',
      undefined,
      ['code', 'value']
    )
      .paginate(1, Infinity)
      .get()
    if (langError) return []
    set('localLangData', locales as LangData[])
    return locales
  }

  const getUserInfo = async () => {
    const res = await apiGetUserInfo({
      params: { populate: ['role', 'personal_project'] }
    })
    if (parseResponseResult(res).ok) {
      if (!isEqual(res?.data?.menu, curLocalAuthCodeList)) {
        removeLocalValue('localAuthCodeList')
      }
      set('localAuthCodeList', res?.data?.menu)
      return res.data
    }
  }

  const queryUserInfo = () => {
    getLangInfo()
    updateEnvConfig()
    // 如果不是登录页且没有token则执行
    if (isNil(getToken())) {
      const { location } = history
      if (location.pathname !== loginPath) {
        toLoginPage()
      }
      return {}
    }
    return getUserInfo()
  }

  return {
    queryUserInfo,
    userInfo: await queryUserInfo(),
    settings: defaultSettings as Partial<LayoutSettings>
  }
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout

export const layout: RunTimeLayoutConfig = ({
  initialState,
  setInitialState
}) => {
  let [redirectPath, token] = window.location.href.split('/?access_token=')
  const handleMSToken = async () => {
    // 会返回包含 access_token、refresh_token 等信息，access_token 可在线解析成明文数据：https://jwt.io/
    let jwtInfo = await currentMicrosoftUser({
      params: { query: `access_token=${token}` }
    })
    if (jwtInfo?.data) setToken(jwtInfo?.data?.jwt)
    return window.location.replace(redirectPath)
  }

  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (token) handleMSToken()
  }, [token])

  const queryClient = new QueryClient()

  return {
    menuDataRender: (menuData) => {
      const newMenuData = handleMenu(
        menuData,
        curLocalAuthCodeList || initialState?.userInfo?.menu
      )
      return newMenuData.map((item) => {
        return {
          ...item,
          icon:
            typeof item.icon === 'string' && item.icon.indexOf('|svg') > -1 ? (
              <Icon
                component={IconMap[item.icon.replace('|svg', '')]}
                style={{
                  fontSize: 18,
                  width: '20px',
                  position: 'relative',
                  right:
                    item.icon.startsWith('lab_manage') ||
                    item.icon.startsWith('experimental_zone')
                      ? '1.6px'
                      : '0px',
                  color: '#626262'
                }}
              />
            ) : (
              <div style={{ fontSize: 18, width: '20px', color: '#626262' }}>
                {item.icon}
              </div>
            )
        }
      })
    },
    menuExtraRender: () => (
      <QueryClientProvider client={queryClient}>
        {/* TODO style 两种风格的语言样式交互 */}
        {/* <SwitchLang /> */}
        {initialState?.userInfo && getEnvConfig().internalStaff && <Labbot />}
        {isValidArray(initialState?.userInfo?.menu) &&
          initialState?.userInfo?.menu?.includes('settings') && (
            <UserSettings />
          )}
        <UserGuide />
      </QueryClientProvider>
    ),
    collapsed: !!initialState?.isMenuCollapsed,
    onCollapse: async (collapsed: boolean) => {
      flushSync(() => {
        setInitialState((preInitialState) => ({
          ...preInitialState,
          isMenuCollapsed: collapsed
        }))
      })
    },
    actionsRender: () => [<Notification key="notification" />],
    avatarProps: {
      src: <img className="labWiseAvatar" src={LabwiseAvatar} />,
      title: <AvatarName />,
      render: (_, avatarChildren) => {
        return <AvatarDropdown>{avatarChildren}</AvatarDropdown>
      }
    },
    footerRender: () => <Footer />,
    onPageChange: async () => {
      setInitialState((preInitialState) => ({
        ...preInitialState,
        userInfo: undefined
      }))
      const newUserInfo = await initialState?.queryUserInfo?.()
      if (newUserInfo) {
        flushSync(() => {
          setInitialState((preInitialState) => ({
            ...preInitialState,
            userInfo: newUserInfo
          }))
        })
      }
    },
    layoutBgImgList: [
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',
        left: 85,
        bottom: 100,
        height: '303px'
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',
        bottom: -68,
        right: -45,
        height: '303px'
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',
        bottom: 0,
        left: 0,
        width: '331px'
      }
    ],
    links: [],
    // [
    //   <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
    //     <LinkOutlined />
    //     <span>OpenAPI 文档</span>
    //   </Link>,
    // ]
    menuHeaderRender: undefined,
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    childrenRender: (children) => {
      if (initialState?.userInfo?.username) {
        return (
          <QueryClientProvider client={queryClient}>
            {children}
            <Watermark
              domain={`${location.hostname.split('.labwise.cn')[0]}-${
                initialState?.userInfo?.username
              }`}
              isBlind={true}
            />
            <FloatButton.BackTop visibilityHeight={64} />
          </QueryClientProvider>
        )
      }
      return (
        <div className="loadingPage">
          <LoadingTip />
        </div>
      )
    },
    ErrorBoundary,
    ...initialState?.settings
  }
}

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request = {
  ...errorConfig
}
