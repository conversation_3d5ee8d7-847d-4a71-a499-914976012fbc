/*
默认主题
*/
@hd: 1px; // 基本单位
@editorPanelWidth: 564px; // 652px 352px
@siderWidth: 256px;
@menu-width: 256px;
@fold-menu-width: 64px;
@minContainerWidth: calc(1366px - 256px);
@content-width:calc (
  100vw - @menu-width - @scrollbar-width - @layout-horizontal-padding
);
@content-width-foldMenu: calc(
  100vw - @fold-menu-width - @layout-horizontal-padding - @scrollbar-width
);

/* 设计规范主色 */
@color-design-a: rgba(2, 122, 255, 1);
@color-design-b: rgba(2, 122, 255, 0.1);
@color-design-c: rgba(2, 122, 255, 0.85);
@color-design-d: rgba(2, 104, 217, 1);
@color-design-e: rgb(64, 150, 255, 1);

/* 功能色-连接 */
@color-connect-a: rgb(2, 5, 8);
@color-connect-b: rgba(23, 144, 254, 0.1);
@color-connect-c: rgba(23, 144, 254, 0.85);
@color-connect-d: rgba(20, 122, 216, 1);

/* 功能色-成功 */
@color-success-a: rgba(64, 187, 90, 1);
@color-success-b: rgba(64, 187, 90, 0.1);
@color-success-c: rgba(64, 187, 90, 0.85);
@color-success-d: rgba(54, 159, 77, 1);

/* 功能色-警示 */
@color-warn-a: rgba(255, 160, 30, 1);
@color-warn-b: rgba(255, 160, 30, 0.1);
@color-warn-c: rgba(255, 160, 30, 0.85);
@color-warn-d: rgba(217, 136, 25, 1);
@color-pending: #ffb044;
@color-running: #1a90ff;
@color-completed: #40ba5a;
@color-failed: #ff4d4f;

/* 功能色-失败 */
@color-fail-a: rgba(255, 77, 79, 1);
@color-fail-b: rgba(255, 77, 79, 0.1);
@color-fail-c: rgba(255, 77, 79, 0.85);
@color-fail-d: rgba(217, 65, 67, 1);

/* ---------色彩--------- */
// 文字色
@color-text-base: #000000; // 基本
@color-text-base-inverse: #fff; // 基本 - 反色
@color-text-secondary: #555555; // 辅助色
@color-text-placeholder: #bbb; // 文本框提示
@color-text-disabled: #bbb; // 失效
@color-text-caption: #888; // 辅助描述
@color-text-paragraph: #333; // 段落
@color-link: @brand-primary; // 链接
@color-text-gray: #626262;
@text-choosed: #0047bb;

/* 中性色-文字颜色 */
@color-text-a: rgba(25, 25, 25, 1);
@color-text-b: rgba(51, 51, 51, 1);
@color-text-label: rgba(85, 85, 85, 1);
@color-text-d: rgba(153, 153, 153, 1);
@color-text-e: rgba(173, 173, 173, 1);
@color-text-f: rgba(204, 204, 204, 1);
@color-text-g: rgba(221, 221, 221, 1);
@color-text-h: rgba(238, 238, 238, 1);
@color-text-i: rgba(245, 245, 245, 1);
@color-text-j: rgba(249, 249, 249, 1);
@color-footer-text: #a1a1a1;

// 边框
@color-border-created: #f5b544;
@color-border-designing: #4b9f47;
@color-border-synthsizing: #f577d2;
@color-border-finished: #1890ff;
@color-border-canceled: #979797;

// 背景色
@fill-bg-light: #ffffff;
@fill-bg-textarea: rgba(247, 247, 247, 1);
@fill-bg-primary-button: #336cc9;
@fill-body: #f5f7fb; // 页面背景
@fill-base: #fff; // 组件默认背景
@fill-tap: #ddd; // 组件默认背景 - 按下
@fill-disabled: #ddd; // 通用失效背景
@fill-mask: rgba(0, 0, 0, 0.4); // 遮罩背景
@color-icon-base: #ccc; // 许多小图标的背景，比如一些小圆点，加减号
@fill-grey: #f7f7f7;
@fill-orange: #f89439; // 已编辑
@fill-apply: #1296db; // 已应用 成功
@fill-cursor: #336cc9;
@fill-generate: #2ad259; // 已生成
@fill-purple: #a52ad2; // 已生成
@fill-choosed: #e4edf8;

// 透明度
@opacity-disabled: 0.3; // switch checkbox radio 等组件禁用的透明度

// 全局/品牌色
@brand-primary: #108ee9;
@brand-primary-tap: #0e80d2;
@brand-success: #6abf47;
@brand-warning: #ffc600;
@brand-error: #f4333c;
@brand-important: #ff5b05;
@react-error: #e65441;

// 边框色
@border-color-base: #ddd;

// 字体尺寸
@font-size-icontext: 10 * @hd;
@font-size-caption-sm: 12 * @hd;
@font-size-base: 14 * @hd;
@font-size-subhead: 15 * @hd;
@font-size-caption: 16 * @hd;
@font-size-heading: 17 * @hd;

// 圆角
@radius-xs: 2 * @hd;
@radius-sm: 3 * @hd;
@radius-md: 5 * @hd;
@radius-lg: 7 * @hd;
@radius-circle: 50%;

@main-layout-padding_top: 2px;
@main-content-height: calc(100vh - 48px - @footer-height);
@main-content-height_noHeader: calc(
  100vh - @footer-height - @main-layout-padding_top
);
@crumbs-height: 42px;
@main-content-height_hasCrumbs: calc(
  100vh - @crumbs-height - 16px - @footer-height
);
@pagination-height: 52px;
@footer-height: 24px;
@anchor-width: 120px;
@scrollbar-width: 10px;
@layout-horizontal-padding: 10px;

// 边框尺寸
@border-width-sm: 1px;
@border-width-md: 1px;
@border-width-lg: 2 * @hd;

/* ---------间距--------- */
// 水平间距
@h-spacing-sm: 5 * @hd;
@h-spacing-md: 8 * @hd;
@h-spacing-lg: 15 * @hd;

// 垂直间距
@v-spacing-xs: 3 * @hd;
@v-spacing-sm: 6 * @hd;
@v-spacing-md: 9 * @hd;
@v-spacing-lg: 15 * @hd;
@v-spacing-xl: 21 * @hd;

// 高度
@line-height-base: 1; // 单行行高
@line-height-paragraph: 1.5; // 多行行高

// 图标尺寸
@icon-size-xxs: 15 * @hd;
@icon-size-xs: 18 * @hd;
@icon-size-sm: 21 * @hd;
@icon-size-md: 22 * @hd; // 导航条上的图标、grid的图标大小
@icon-size-lg: 36 * @hd;

// 动画缓动
@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);

// z-index
@sider-extra-ZIndex: 11;
@workbench-unfold-summary-width: 392px;
@workbench-fold-summary-width: 458px;
