import { LOGIN_PATH } from '@/constants'
import { Navigate, Outlet, useAccess, useRouteProps } from 'umi'
import routes from '../../config/routes'
export default () => {
  const access = useAccess()
  function convertMenu(menuTreeList, children) {
    menuTreeList.forEach((item) => {
      children.push(item)
      if (item.routes && item.routes.length !== 0) {
        item.children = convertMenu(item.routes, children)
      }
    })
    return children
  }
  const routeProps = useRouteProps()

  const allRoutes = convertMenu(routes, [])
  const curRouteIdx = allRoutes.findIndex(
    (item) => item.path === routeProps.originPath
  )

  if (curRouteIdx > -1) {
    const routeCode = allRoutes[curRouteIdx]?.resource
    if (routeCode && access?.authCodeList?.includes(routeCode)) {
      return <Outlet />
    } else {
      return <Navigate to="/workspace" />
    }
  }
  return <Navigate to={LOGIN_PATH} />
}
