{"name": "labwise-web", "version": "0.2.2", "private": true, "author": "c12 FE", "repository": {"type": "git", "url": "https://github.com/c12-ai/brain-web"}, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build ANALYZE=1", "clean": "rimraf node_modules yarn-error.log && npm cache clean -f && yarn cache clean", "cz": "cz-cust", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "husky install", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env max dev", "start-dev": "cross-env UMI_ENV=develop MOCK=none max dev --expose-gc", "start-test": "cross-env UMI_ENV=test MOCK=none max dev --expose-gc", "start-partener": "cross-env UMI_ENV=partener MOCK=none max dev --expose-gc", "start-cp": "cross-env UMI_ENV=chempartner MOCK=none max dev --expose-gc", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^4.8.0", "@ant-design/pro-components": "^2.3.57", "@ant-design/use-emotion-css": "1.0.4", "@antv/g6": "^4.8.17", "@antv/g6-react-node": "^1.4.5", "@bpmn-io/properties-panel": "^0.13.2", "@eyevinn/webrtc-player": "^0.12.1", "@kmariappan/strapi-client-js": "^1.4.0", "@lottiefiles/react-lottie-player": "^3.5.2", "@rdkit/rdkit": "^2023.9.2-1.0.0", "@sentry/react": "^7.54.0", "@superset-ui/embedded-sdk": "^0.1.0-alpha.9", "@tanstack/react-query": "^4.x.x", "@types/file-saver": "^2.0.5", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@umijs/max": "^4.0.64", "@umijs/route-utils": "^2.2.2", "ahooks": "^3.7.8", "antd": "^5.4.0", "axios": "^0.26.1", "bpmn-js": "^9.0.3", "bpmn-js-properties-panel": "^1.0.0", "bpmn-js-token-simulation": "^0.28.0", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.10.4", "dexie": "^4.0.9", "dexie-react-hooks": "^1.1.7", "file-loader": "^6.2.0", "file-saver": "^2.0.5", "form-render": "^2.3.5", "git-repo-info": "^2.1.1", "hls.js": "^1.5.6", "js-cookie": "^3.0.5", "ketcher-core": "2.11.0", "ketcher-react": "2.8.0", "ketcher-standalone": "2.11.0", "lodash": "^4.17.21", "nprogress": "^0.2.0", "omit.js": "^2.0.2", "plyr": "^3.7.8", "qs": "^6.11.2", "query-string": "^7.1.1", "rc-menu": "^9.8.2", "rc-util": "^5.27.2", "react": "^17.x", "react-color": "^2.19.3", "react-dev-inspector": "^1.8.4", "react-dom": "^17.x", "react-error-boundary": "^4.0.10", "react-helmet-async": "^1.3.0", "react-linkify": "^1.0.0-alpha", "react-syntax-highlighter": "^15.5.0", "react-use-websocket": "^4.8.1", "simple-peer": "^9.11.1", "simplewebrtc": "^3.0.2", "smiles-drawer": "^2.1.7", "socket.io-client": "^4.7.1", "umi-presets-pro": "^2.0.3", "use-draggable-scroll": "^0.1.0", "use-sync-external-store": "^1.4.0", "usehooks-ts": "^2.9.1", "uuid": "^9.0.0", "video.js": "^8.10.0", "webrtc-adapter": "7.4.0", "xml2js": "^0.4.23", "xss": "^1.0.14", "zustand": "^4.4.1"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.5", "@testing-library/react": "^13.4.0", "@types/classnames": "^2.3.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@types/express": "^4.17.17", "@types/history": "^4.7.11", "@types/jest": "^29.4.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.191", "@types/react": "^17.x", "@types/react-dom": "^17.x", "@types/react-helmet": "^6.1.6", "@umijs/fabric": "^2.14.1", "@umijs/lint": "^4.0.52", "cross-env": "^7.0.3", "eslint": "^8.34.0", "eslint-plugin-react": "^7.22.0", "eslint-plugin-react-hooks": "^4.6.0", "express": "^4.18.2", "gh-pages": "^3.2.3", "husky": "^7.0.4", "jest": "^29.4.3", "jest-environment-jsdom": "^29.4.3", "lint-staged": "^10.5.4", "mockjs": "^1.1.0", "prettier": "^2.8.4", "swagger-ui-dist": "^4.15.5", "ts-loader": "^8.0.18", "ts-node": "^10.9.1", "typescript": "^5.0.4"}, "engines": {"node": ">=12.0.0"}, "volta": {"node": "16.20.2", "yarn": "1.22.19"}}