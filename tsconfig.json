{
  "compilerOptions": {
    "allowUnreachableCode": true,
    "allowUnusedLabels": false,
    "noEmitOnError": false,

    /* Advanced Options */
    "forceConsistentCasingInFileNames": false,

    /* Basic Options */
    "incremental": true,
    "target": "esnext",
    "module": "esnext",
    "lib": ["es6", "ES2018", "ES2019", "dom"],
    "skipLibCheck": true,
    "allowJs": true,
    "checkJs": true,
    "jsx": "preserve",
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "outDir": "./dist",
    "rootDir": "./",
    "composite": true,
    "removeComments": true,
    "downlevelIteration": true,
    "isolatedModules": true,

    /* Strict Type-Checking Options */
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "alwaysStrict": true,

    /* Additional Checks */
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,

    /* Module Resolution Options */
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"],
      "@@/*": ["src/.umi/*"],
      "@@test/*": ["src/.umi-test/*"],
      "@components/*": ["src/components/*"],
      "@types": ["src/types/models"],
      "@utils/*": ["src/utils/*"]
    },
    "rootDirs": [],
    "typeRoots": ["node_modules/@types"],
    "types": [],
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "preserveSymlinks": true,

    /* Source Map Options */
    "sourceRoot": "",
    "mapRoot": "",
    "inlineSources": true,

    /* Experimental Options */
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "noEmit": true,
    "importHelpers": true
  },
  "include": ["./**/*.d.ts", "./**/*.ts", "./**/*.tsx"],
  "exclude": [
    "node_modules",
    "lib",
    "es",
    "dist",
    "typings",
    "**/__test__",
    "test",
    "docs",
    "tests"
  ],
  "compileOnSave": true
}
