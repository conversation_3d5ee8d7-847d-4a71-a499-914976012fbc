# Labwise-web

- [figma](https://www.figma.com/file/BcM9UJGE3K6DoOo3lV72h8/Labwise?type=design&node-id=5-31571&mode=design&t=pIePvHgSIGHOX9lg-0)
- [设计稿](https://www.figma.com/file/eGbGhDIP92XuAcNRBBRkmM/C12.ai%26Labwise?type=design&node-id=23-475&mode=design&t=ciKiU6nTA76ain37-0)
- [需求文档](https://carbon12.feishu.cn/wiki/FI8Aw3THBiJSO9kWwqzc4Gy9n0g#part-EW6Vdgob6o7AmcxRRH0cp4aEnVq)
- 环境地址
  - [staging](https://staging.poc.labwise.cn/user/login)
  - [dev](https://web-dev.labwise.cn/projects)
  - [cp](https://chempartner.labwise.cn/projects)
  - [staging](https://staging.trail.labwise.cn/user/login)
- 开发须知
  - Network Invert
    - inchi socket svg dict umi mf
  - [图标库](https://www.figma.com/file/eGbGhDIP92XuAcNRBBRkmM/C12.ai%26Labwise?type=design&node-id=104-12328&mode=design&t=j1CpSGqcmMqgifpm-0)
  - [国际化文档](https://carbon12.feishu.cn/wiki/OBkNw26xdisIz2kmjwQcMyepnQf)
  - 推流测试链接：
    - wifi：c12-tplink
      - http://************:2022/tools/player.html?url=http://************:2022/live/robot-1-board.m3u8
    - wifi：c12
      - http://************:2022/tools/player.html?url=http://************:2022/live/robot-1-left.m3u8

  ```js
  import { getWord } from '@/utils'
  getWord('')
  ```

  - [procomponents](https://procomponents.ant.design/)
  - [Ant Design Pro](https://pro.ant.design/zh-CN/docs/overview)
  - e.g.[strapi 常用 api](https://github.com/kmariappan/strapi-client-js#readme)

  ```js
  // 路径参数 query
  ,{
    customQuery: `id=${projectId}`
  }
  // 查找某一个id数据
  .selectManyByID(['..'])
  ```

  当响应字段为 对象时，需要平铺出来，需要使用 [query 的 populateWith](https://github.com/kmariappan/strapi-client-js#readme) filterDeep 进行筛选查询，单选使用 eq,多选使用 in

- 登录逻辑

  - 1、目前登录使用 keycloack 还是微软，如之前约定是按照环境变量 process.env.auth_provider 控制
  - 2、如果 process.env.auth_provider 为 microsoft 则通过接口 client-secret 获取登录页地址
  - 3、如果如果 process.env.auth_provider 不是 microsoft，则跳到自己系统的登录页，输入账号密码后，同时将 client-secret 返回值作为请求参数 client_secret 传给 realms/strapi/protocol/openid-connect/token 来获取 jwt，进行登录操作

  - [权限控制](https://carbon12.feishu.cn/wiki/DehFwtaOKiiz2Bk9efCcAi2ZnLe?from=from_lark_group_search&hyperlink_open_type=lark.open_in_browser&disposable_login_token=eyJ1c2VyX2lkIjoiNzE3MDkyNTk2NTcyMjQwMjgxNyIsImRldmljZV9sb2dpbl9pZCI6IjcxNzA5MzA3NzkxMjgzNjUwNTgiLCJ0aW1lc3RhbXAiOjE3MDY1MTQzMTIsInVuaXQiOiJldV9uYyIsInB3ZF9sZXNzX2xvZ2luX2F1dGgiOiIxIiwidmVyc2lvbiI6InYzIiwidGVuYW50X2JyYW5kIjoiZmVpc2h1IiwicGtnX2JyYW5kIjoi6aOe5LmmIn0=.62c177e4c8c782ca8153658c9c478944551ba8d8508eab183d4c9ed9575b9934)

  ```js
  const access = useAccess()
  {access?.authCodeList?.includes('') && ...
  ```

- living comment UI
  - [ ] [](https://github.com/kingofthestack/react-chat-window)
  - [ ] scroll to the bottom when send comment
  - [ ] button hover style

## User Info

| Username     | Email               |
| ------------ | ------------------- |
| client1      | <EMAIL>      |
| junior1      | <EMAIL>      |
| groupleader1 | <EMAIL> |
| leader1      | <EMAIL>      |
| pm1          | <EMAIL>          |
| senior1      | <EMAIL>      |
| test3        | <EMAIL>        |

### staging

| Username | pasword         |
| -------- | --------------- |
| wuwenyan | <EMAIL> |

### test

| Username    | pasword     |
| ----------- | ----------- |
| cai_shipeng | Carb0n12    |
| peng_sifan  | peng_sifan  |
| yuan_xiaoyu | yuan_xiaoyu |
| wuwenyan    | wuwenyan    |
| mao_yujie   | mao_yujie   |
| demo        | demo        |

### partner

| Username | pasword  |
| -------- | -------- |
| demo     | Demo1234 |
| test1    | test1    |

### 微软

| Username                         | pasword  |
| -------------------------------- | -------- |
| <EMAIL> | Carb0n12 |

## Environment Prepare

Install `node_modules`:

```bash
npm install
# or
yarn
```

## Provided Scripts

Ant Design Pro provides some useful script to help you quick start and build with web project, code style check and test.

Scripts provided in `package.json`. It's safe to modify or add additional script:

```bash
npm start # Start project
npm run build # Build project
npm run lint # Check code style
npm run lint:fix # You can also use script to auto fix some lint error:
npm test # Test code
```

## 登录逻辑

- 1、目前登录使用 keycloack 还是微软，如之前约定是按照环境变量 process.env.auth_provider 控制
- 2、如果 process.env.auth_provider 为 microsoft 则通过接口 client-secret 获取登录页地址
- 3、如果 process.env.auth_provider 不是 microsoft，则跳到自己系统的登录页，输入账号密码后，同时将 client-secret 返回值作为请求参数 client_secret 传给 realms/strapi/protocol/openid-connect/token 来获取 jwt，进行登录操作